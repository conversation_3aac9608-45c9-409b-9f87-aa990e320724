#!/bin/bash

# Local test script to verify test configuration
set -e

echo "🧪 Running local test verification..."

# Check if we're in the right directory
if [ ! -f "pyproject.toml" ]; then
    echo "❌ pyproject.toml not found. Please run from project root."
    exit 1
fi

# Install dependencies if uv is available
if command -v uv &> /dev/null; then
    echo "📦 Installing dependencies with uv..."
    uv sync
    uv sync --group test
    
    echo "🔍 Running tests locally..."
    uv run python run_tests.py
    
    echo "📁 Checking generated files:"
    if [ -d "htmlcov" ]; then
        echo "✅ htmlcov directory exists"
        ls -la htmlcov/ | head -5
    else
        echo "❌ htmlcov directory not found"
    fi
    
    if [ -f "coverage.xml" ]; then
        echo "✅ coverage.xml exists"
        head -3 coverage.xml
    else
        echo "❌ coverage.xml not found"
    fi
    
    if [ -f "junit.xml" ]; then
        echo "✅ junit.xml exists"
        head -3 junit.xml
    else
        echo "❌ junit.xml not found"
    fi
    
else
    echo "⚠️  uv not found. Please install uv first:"
    echo "   curl -LsSf https://astral.sh/uv/install.sh | sh"
    exit 1
fi

echo "🎉 Local test verification completed!"
