-- Migration v1.1: Add hello_message and bot_type columns to chatbots table
ALTER TABLE "public"."chatbots"
ADD COLUMN "hello_message" text DEFAULT 'Hello! How can I assist you today?';

-- Add bot_type column with default value 'normal'
ALTER TABLE "public"."chatbots"
ADD COLUMN "bot_type" varchar DEFAULT 'normal' NOT NULL;

ALTER TABLE chatbots
ADD COLUMN extra_data JSONB;

-- Update existing records to have the default hello message
UPDATE "public"."chatbots"
SET
  "hello_message" = 'Hello! How can I assist you today?'
WHERE
  "hello_message" IS NULL;

-- Update existing records to have the default bot_type
UPDATE "public"."chatbots"
SET
  "bot_type" = 'normal'
WHERE
  "bot_type" IS NULL;

-- Add check constraint to ensure bot_type is either 'normal' or 'mission'
ALTER TABLE "public"."chatbots" ADD CONSTRAINT "check_bot_type" CHECK ("bot_type" IN ('normal', 'mission'));