CREATE TABLE
  IF NOT EXISTS user_chat_history (
    id TEXT PRIMARY KEY,
    bot_id TEXT NOT NULL,
    user_id INTEGER NOT NULL,
    message JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL
  );

-- add index to user_id column
CREATE INDEX IF NOT EXISTS idx_user_chat_history_user_id ON user_chat_history (user_id);

-- add index to bot_id column
CREATE INDEX IF NOT EXISTS idx_user_chat_history_bot_id ON user_chat_history (bot_id);

-- add index to created_at column
CREATE INDEX IF NOT EXISTS idx_user_chat_history_created_at ON user_chat_history (created_at);

-- add index to updated_at column
CREATE INDEX IF NOT EXISTS idx_user_chat_history_updated_at ON user_chat_history (updated_at);

-- add index to deleted_at column
CREATE INDEX IF NOT EXISTS idx_user_chat_history_deleted_at ON user_chat_history (deleted_at);

CREATE TRIGGER user_chat_history_updated_at BEFORE
UPDATE ON user_chat_history FOR EACH ROW EXECUTE FUNCTION update_updated_at_column ();