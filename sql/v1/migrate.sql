
-- Table Definition
CREATE TABLE "public"."chatbots" (
    "id" varchar NOT NULL,
    "name" varchar NOT NULL,
    "persona" text NOT NULL,
    "image_url" varchar(255),
    "voice" varchar(100) DEFAULT 'en_female_amanda_mars_bigtts'::character varying,
    "voice_speed" varchar DEFAULT '1.0'::character varying,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" timestamptz DEFAULT NULL,
    PRIMARY KEY ("id")
);

CREATE TRIGGER update_chatbots_updated_at
BEFORE UPDATE ON chatbots
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- before PostgreSQL 11 
CREATE TRIGGER update_chatbots_updated_at
BEFORE UPDATE ON chatbots
FOR EACH ROW
EXECUTE PROCEDURE update_updated_at_column();