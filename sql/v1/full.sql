-- -------------------------------------------------------------
-- TablePlus 6.4.8(608)
--
-- https://tableplus.com/
--
-- Database: gusto_ai_base_service
-- Generation Time: 2025-06-23 11:09:57.3040
-- -------------------------------------------------------------

-- Table Definition
CREATE TABLE "public"."tasks" (
    "task_id" text NOT NULL,
    "task_type" text NOT NULL,
    "input_data" jsonb NOT NULL,
    "status" text NOT NULL,
    "result" jsonb,
    "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" timestamptz,
    PRIMARY KEY ("task_id")
);

-- Table Definition
CREATE TABLE "public"."chatbots" (
    "id" varchar NOT NULL,
    "name" varchar NOT NULL,
    "persona" text NOT NULL,
    "image_url" varchar(255),
    "voice" varchar(100) NOT NULL DEFAULT 'en_female_amanda_mars_bigtts',
    "voice_speed" varchar(10) NOT NULL DEFAULT '1.0',
    "created_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamptz NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted_at" timestamptz,
    PRIMARY KEY ("id")
);

-- Function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for auto-updating updated_at column
CREATE TRIGGER update_tasks_updated_at
    BEFORE UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chatbots_updated_at
    BEFORE UPDATE ON chatbots
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
