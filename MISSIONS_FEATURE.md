# Extra Data and Missions Feature Implementation

## Overview

This implementation adds support for `extra_data` (JSON type) to the chatbots table and implements missions functionality for mission-type bots.

## Changes Made

### Backend (Python)

#### 1. Database Schema (`src/db.py`)

- Added `extra_data = Column(JSONB, nullable=True)` to the `Chatbot` model
- Updated `create_or_update_chatbot()` function to accept and handle `extra_data` parameter
- Updated all chatbot retrieval functions to return `extra_data` field

#### 2. API Models (`src/models.py`)

- Added `extra_data: Optional[Dict[str, Any]] = None` to both `ChatbotRequest` and `ChatbotResponse` models

#### 3. API Endpoints (`src/api.py`)

- Updated `create_or_update_chatbot_endpoint` to pass `request.extra_data` to the database function

#### 4. Database Migration (`sql/v1.1/migrate.sql`)

- Migration already includes: `ALTER TABLE chatbots ADD COLUMN extra_data JSONB;`

### Frontend (React)

#### 1. Bot Model (`src/types/Bot.js`)

- Added `extra_data` property to the Bot constructor
- Implemented `getMissions()` method to retrieve missions from extra_data
- Implemented `setMissions()` method to set missions in extra_data
- Implemented `hasMissions()` method to check if missions are defined
- Updated `toAPIFormat()`, `fromAPI()`, and `fromStorage()` methods to include extra_data

#### 2. AddBotModal Component (`src/components/Modal/AddBotModal.js`)

- Added `missions` state variable
- Added missions textarea field that appears when bot type is "mission"
- Added useEffect to clear missions when bot type changes to "normal"
- Updated `handleSave()` to prepare extra_data with missions for mission-type bots
- Updated useEffect to load missions from editBot when editing

#### 3. App.js

- Updated `saveBotData()` function to accept and handle `extraData` parameter
- Updated bot creation and update logic to include extra_data

#### 4. BotList Component (`src/components/BotList/BotList.js`)

- Added missions badge (✅) for mission bots that have missions defined
- Updated bot description to show missions summary for mission bots

#### 5. API Services (`src/services/apiServices.js`)

- Updated `saveChatbot()` function to include `bot_type` and `extra_data` in the request body

#### 6. Styling (`src/components/Modal/Modal.css`, `src/components/BotList/BotList.css`)

- Added special styling for missions textarea with red border accent
- Added styling for missions badge

## Usage

### Creating a Mission Bot

1. Set bot type to "Mission - Complete tasks"
2. Fill in the missions textarea with specific tasks/goals
3. The missions will be stored in `extra_data.missions`

### Bot List Display

- Mission bots show 🎯 emoji
- Bots with defined missions show ✅ badge
- Mission bot descriptions show missions summary instead of persona

### API Integration

- All data is properly serialized to/from the backend
- Backward compatibility maintained (existing bots work unchanged)

## Database Schema

```sql
-- The extra_data column is already added in the migration
ALTER TABLE chatbots ADD COLUMN extra_data JSONB;
```

## Example extra_data Structure

```json
{
  "missions": "Help users learn JavaScript programming, provide code examples, debug code issues, and explain programming concepts in simple terms."
}
```

## Testing

- ✅ Create new mission bot with missions
- ✅ Edit existing bot to add/remove missions
- ✅ Switch bot type from mission to normal (clears missions)
- ✅ Switch bot type from normal to mission (enables missions field)
- ✅ API integration works correctly
- ✅ Backward compatibility maintained
