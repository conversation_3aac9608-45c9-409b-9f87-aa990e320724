# Mission Feature Documentation

## Overview

The Mission feature allows users to create and track conversation-based objectives while chatting with AI bots. It provides a structured way to set goals, track progress, and analyze conversation effectiveness.

## Features

### 1. Mission Creation

- Create missions with detailed descriptions
- AI automatically generates:
  - Mission title
  - Objectives list
  - Step-by-step action items
  - Status tracking for each step

### 2. Mission Management

- **Local Storage**: Missions are automatically saved to localStorage with key `mission-[botId]`
- **Per-Bot Missions**: Each bot can have its own unique mission
- **Persistent Data**: Missions persist across browser sessions

### 3. Mission Status Checking

- Analyze conversation history against mission objectives
- Automatic status updates for mission steps:
  - ⏳ `pending` - Not started
  - 🔄 `in_progress` - Currently working on
  - ✅ `completed` - Successfully finished
  - ❌ `failed` - Could not complete

### 4. User Interface

- **Mission Button**: Accessible from the chat settings menu (⚙️ → 🎯 Mission)
- **Modal Interface**: Full-screen modal for mission management
- **Status Visualization**: Color-coded progress indicators
- **Responsive Design**: Works on desktop and mobile devices

## Usage

### Creating a Mission

1. Click the settings button (⚙️) in the chat header
2. Select "Mission" (🎯) from the dropdown
3. Click "Create New Mission"
4. Enter detailed mission objectives and goals
5. Click "Create Mission"

### Checking Mission Progress

1. Open the Mission modal
2. Click "Check Status" to analyze current conversation
3. Review updated step statuses
4. Continue conversation to progress through mission steps

### Managing Missions

- **Delete Mission**: Remove current mission (with confirmation)
- **Create New Mission**: Replace existing mission with a new one
- **View Progress**: See detailed breakdown of objectives and steps

## Technical Implementation

### Frontend Components

- `MissionModal.js` - Main mission management interface
- `MissionModal.css` - Styling and responsive design
- `ChatSettingsMenu.js` - Updated to include mission button

### API Integration

- `POST /api/mission` - Create new missions
- `POST /api/mission/check` - Analyze and update mission status
- Uses centralized API service functions in `apiServices.js`

### Data Storage

- **Local Storage Key**: `mission-[botId]`
- **Data Format**: JSON serialized MissionData object
- **Automatic Sync**: Saves on every mission update

### Backend Processing

- AI-powered mission parsing and generation
- Conversation analysis for progress tracking
- Structured response with detailed step tracking

## Example Mission Structure

```json
{
  "title": "Learn English Conversation Skills",
  "description": "Improve English speaking through natural conversation practice",
  "objectives": [
    "Practice everyday conversation topics",
    "Improve pronunciation and fluency",
    "Learn common phrases and expressions"
  ],
  "steps": [
    {
      "action": "Introduce yourself and share background",
      "status": "completed"
    },
    {
      "action": "Discuss favorite hobbies and interests",
      "status": "in_progress"
    },
    {
      "action": "Practice describing daily routine",
      "status": "pending"
    }
  ]
}
```

## Benefits

- **Structured Learning**: Clear objectives and measurable progress
- **Conversation Focus**: Keeps chats on track toward specific goals
- **Progress Tracking**: Visual feedback on conversation effectiveness
- **Personalized Experience**: Custom missions for different learning needs
- **Data Persistence**: Never lose mission progress between sessions
