import asyncio
import csv
import os
import tempfile
import pandas as pd
from typing import List, Dict
from src.tools.tts import text_to_speech
from src.upload import upload
import base64
import logging

logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Define constants
CSV_PATHS = [
    # "/Users/<USER>/Downloads/App话题卡更新-中高级.csv",
    # "/Users/<USER>/Downloads/App话题卡更新-低级.csv",
    "chores/test.csv"
]
ENGLISH_VOICE = "en_female_amanda_mars_bigtts"  # English voice for TTS


async def process_csv_file():
    """
    Process the CSV file:
    1. Read the CSV file
    2. Convert each question to audio using TTS
    3. Upload audio files to Aliyun OSS
    4. Update the CSV with audio URLs
    """

    for CSV_PATH in CSV_PATHS:
        logging.info(f"Processing CSV file: {CSV_PATH}")
        OUTPUT_CSV_PATH = CSV_PATH.replace('.csv', '_updated.csv')
        try:
            # Read the CSV file
            df = pd.read_csv(CSV_PATH, encoding='utf-8')

            # Add a new column for audio URLs if it doesn't exist
            if 'AudioURL' not in df.columns:
                df['AudioURL'] = ''

            # Process each row where 是否已录入 is '否'
            for index, row in df.iterrows():
                if pd.isna(row['Question']) or pd.isna(row['是否已录入']) or row['是否已录入'] != '否':
                    continue

                try:
                    topic = row['Topic']
                    question = row['Question']

                    # Upload the audio file to Aliyun OSS
                    object_name = f"{topic}__{question}"
                    # replace non alpha-numeric characters with underscores
                    object_name = ''.join(
                        c if c.isalnum() else '_' for c in object_name)
                    if len(object_name) > 64:
                        # Truncate to 64 characters
                        object_name = object_name[:64]
                    logging.info(
                        f"Processing question: {question} (Topic: {topic}), object name: {object_name})")

                    # Create a temporary file for the audio
                    with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                        temp_path = temp_file.name

                    try:
                        # Convert text to speech
                        audio_base64 = await text_to_speech(
                            text=question,
                            voice=ENGLISH_VOICE,
                            audio_file_path=temp_path
                        )

                        logging.info(
                            f"Uploading audio for question '{question}' with object name '{object_name}'")
                        remote_url = upload(
                            temp_path, object_name=f"{object_name}.mp3")
                        logging.info(f"Uploaded to: {remote_url}")

                        # Update the DataFrame
                        df.at[index, 'AudioURL'] = remote_url
                        # df.at[index, '是否已录入'] = '是'

                    finally:
                        # Clean up the temporary file
                        if os.path.exists(temp_path):
                            os.unlink(temp_path)

                except Exception as e:
                    logging.error(
                        f"Error processing question '{question}': {str(e)}")

            # Save the updated CSV file
            df.to_csv(OUTPUT_CSV_PATH, index=False, encoding='utf-8')
            logging.info(f"Updated CSV saved to: {OUTPUT_CSV_PATH}")

        except Exception as e:
            logging.error(f"Failed to process CSV file: {str(e)}")
            raise

if __name__ == "__main__":
    asyncio.run(process_csv_file())
