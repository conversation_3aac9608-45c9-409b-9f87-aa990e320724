#!/usr/bin/env python3
"""
Environment file comparison tool.
Compares two .env files to check if the second one is missing keys from the template.
"""

import argparse
import sys
from pathlib import Path
from typing import Set, Tuple


def parse_env_file(file_path: Path) -> Set[str]:
    """
    Parse an environment file and return a set of keys.
    
    Args:
        file_path: Path to the environment file
        
    Returns:
        Set of environment variable keys
        
    Raises:
        FileNotFoundError: If the file doesn't exist
        IOError: If there's an error reading the file
    """
    if not file_path.exists():
        raise FileNotFoundError(f"Environment file not found: {file_path}")
    
    keys = set()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                # Strip whitespace and skip empty lines and comments
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                # Check if line contains an assignment
                if '=' in line:
                    key = line.split('=', 1)[0].strip()
                    if key:  # Ensure key is not empty
                        keys.add(key)
                else:
                    print(f"Warning: Line {line_num} in {file_path} doesn't contain '=': {line}")
    
    except IOError as e:
        raise IOError(f"Error reading file {file_path}: {e}") from e
    
    return keys


def compare_env_files(template_path: Path, target_path: Path) -> Tuple[Set[str], Set[str], Set[str]]:
    """
    Compare two environment files.
    
    Args:
        template_path: Path to the template environment file
        target_path: Path to the target environment file to check
        
    Returns:
        Tuple of (missing_keys, extra_keys, common_keys)
    """
    template_keys = parse_env_file(template_path)
    target_keys = parse_env_file(target_path)
    
    missing_keys = template_keys - target_keys
    extra_keys = target_keys - template_keys
    common_keys = template_keys & target_keys
    
    return missing_keys, extra_keys, common_keys


def main():
    """Main function to handle command line arguments and comparison logic."""
    parser = argparse.ArgumentParser(
        description="Compare two environment files to check for missing keys",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s template.env production.env
  %(prog)s .env.example .env --verbose
  %(prog)s template.env target.env --show-extra
        """
    )
    
    parser.add_argument(
        'template',
        type=Path,
        help='Path to the template environment file'
    )
    
    parser.add_argument(
        'target',
        type=Path,
        help='Path to the target environment file to check'
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Show detailed output including common keys'
    )
    
    parser.add_argument(
        '--show-extra',
        action='store_true',
        help='Show keys that exist in target but not in template'
    )
    
    parser.add_argument(
        '--exit-code',
        action='store_true',
        help='Exit with code 1 if there are missing keys, 0 otherwise'
    )
    
    args = parser.parse_args()
    
    try:
        missing_keys, extra_keys, common_keys = compare_env_files(args.template, args.target)
        
        # Print results
        print(f"Comparing template '{args.template}' with target '{args.target}'")
        print("=" * 60)
        
        if missing_keys:
            print(f"\n❌ Missing keys in '{args.target}' ({len(missing_keys)}):")
            for key in sorted(missing_keys):
                print(f"  - {key}")
        else:
            print("\n✅ No missing keys found!")
        
        if args.show_extra and extra_keys:
            print(f"\n📎 Extra keys in '{args.target}' ({len(extra_keys)}):")
            for key in sorted(extra_keys):
                print(f"  + {key}")
        
        if args.verbose:
            print(f"\n✓ Common keys ({len(common_keys)}):")
            for key in sorted(common_keys):
                print(f"  = {key}")
        
        # Summary
        print("\nSummary:")
        print(f"  Template keys: {len(parse_env_file(args.template))}")
        print(f"  Target keys: {len(parse_env_file(args.target))}")
        print(f"  Missing: {len(missing_keys)}")
        print(f"  Extra: {len(extra_keys)}")
        print(f"  Common: {len(common_keys)}")
        
        # Exit with appropriate code
        if args.exit_code and missing_keys:
            sys.exit(1)
        else:
            sys.exit(0)
            
    except (FileNotFoundError, IOError) as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()


