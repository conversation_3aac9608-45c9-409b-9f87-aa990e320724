[project]
name = "gusto-ai-base-service"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "aiofiles>=24.1.0",
    "aiohttp>=3.11.11",
    "alibabacloud-fc20230330==4.2.7",
    "alibabacloud-openapi-util>=0.2.2",
    "aliyun-log-python-sdk>=0.9.24",
    "azure-cognitiveservices-speech>=1.44.0",
    "certifi>=2025.4.26",
    "dotenv>=0.9.9",
    "fastapi>=0.115.12",
    "jinja2>=3.1.6",
    "langdetect>=1.0.9",
    "numpy>=2.2.6",
    "openai>=1.84.0",
    "oss2>=2.19.1",
    "pandas>=2.3.0",
    "psycopg2>=2.9.10",
    "pydub[mp3]>=0.25.1",
    "python-magic>=0.4.27",
    "python-multipart>=0.0.20",
    "pyyaml>=6.0.2",
    "requests>=2.32.3",
    "sentry-sdk>=2.29.1",
    "sqlalchemy>=2.0.41",
    "uvicorn>=0.34.2",
    "websocket-client>=1.8.0",
    "websockets>=15.0.1",
]

[dependency-groups]
test = [
    "pytest>=8.0.0",
    "pytest-asyncio>=0.23.0",
    "pytest-mock>=3.12.0",
    "pytest-cov>=4.0.0",
    "httpx>=0.27.0",
    "coverage>=7.4.0",
]

[[tool.uv.index]]
url = "https://mirrors.aliyun.com/pypi/simple/"
