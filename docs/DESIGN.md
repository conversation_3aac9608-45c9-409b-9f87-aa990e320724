# DESIGN


```mermaid
graph TD
    %% Client and Frontend
    client[Client] --> frontend

    subgraph frontend[Frontend]
        chatbot[Chatbot UI]
    end

    %% Main Backend Services
    frontend --> api

    subgraph backend[Backend Services]
        api[API Service]
        
        %% Core AI Services
        subgraph ai_services[AI Processing]
            work_flows[Work Flows]
            mcp[MCP]
        end
        
        %% Specialized Generation Services
        subgraph generation_services[Generation Services]
            img_gen[Image Generation]
            audio_gen[Audio Generation]
            tool_exec[Other Tools]
        end
        
        %% Data Sources
        subgraph data_sources[Data Sources]
            db[(Database)]
            vectordb[(Vector Database)]
        end
    end

    %% Core Flow
    api -->|High Frequency Requests| work_flows
    api -->|Long Tail Demands| mcp    
    
    %% MCP Connections
    ai_services -->|RAG| vectordb
    ai_services -->|User Data| db
    ai_services -->|Generate Image Request| img_gen
    ai_services -->|Generate Audio Request| audio_gen
    ai_services -->|Tool Usage Request| tool_exec

    %% Styling
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px
    classDef secondary fill:#bbf,stroke:#333
    class api,work_flows,mcp primary
    class db,vectordb,img_gen,audio_gen,tool_exec secondary
```