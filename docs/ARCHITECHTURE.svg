<svg id="mermaidChart1" width="100%" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" class="flowchart mermaid-svg" style="max-width: 1105.234375px; color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" viewBox="-8 -8 1105.234375 790.7067260742188" role="graphics-document document" aria-roledescription="flowchart-v2"><style style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">#mermaidChart1{font-family:sans-serif;font-size:16px;fill:#333;}#mermaidChart1 .error-icon{fill:#552222;}#mermaidChart1 .error-text{fill:#552222;stroke:#552222;}#mermaidChart1 .edge-thickness-normal{stroke-width:1px;}#mermaidChart1 .edge-thickness-thick{stroke-width:3.5px;}#mermaidChart1 .edge-pattern-solid{stroke-dasharray:0;}#mermaidChart1 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaidChart1 .edge-pattern-dashed{stroke-dasharray:3;}#mermaidChart1 .edge-pattern-dotted{stroke-dasharray:2;}#mermaidChart1 .marker{fill:#333333;stroke:#333333;}#mermaidChart1 .marker.cross{stroke:#333333;}#mermaidChart1 svg{font-family:sans-serif;font-size:16px;}#mermaidChart1 p{margin:0;}#mermaidChart1 .label{font-family:sans-serif;color:#333;}#mermaidChart1 .cluster-label text{fill:#333;}#mermaidChart1 .cluster-label span{color:#333;}#mermaidChart1 .cluster-label span p{background-color:transparent;}#mermaidChart1 .label text,#mermaidChart1 span{fill:#333;color:#333;}#mermaidChart1 .node rect,#mermaidChart1 .node circle,#mermaidChart1 .node ellipse,#mermaidChart1 .node polygon,#mermaidChart1 .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#mermaidChart1 .rough-node .label text,#mermaidChart1 .node .label text,#mermaidChart1 .image-shape .label,#mermaidChart1 .icon-shape .label{text-anchor:middle;}#mermaidChart1 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaidChart1 .rough-node .label,#mermaidChart1 .node .label,#mermaidChart1 .image-shape .label,#mermaidChart1 .icon-shape .label{text-align:center;}#mermaidChart1 .node.clickable{cursor:pointer;}#mermaidChart1 .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#mermaidChart1 .arrowheadPath{fill:#333333;}#mermaidChart1 .edgePath .path{stroke:#333333;stroke-width:2.0px;}#mermaidChart1 .flowchart-link{stroke:#333333;fill:none;}#mermaidChart1 .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaidChart1 .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#mermaidChart1 .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaidChart1 .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#mermaidChart1 .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#mermaidChart1 .cluster text{fill:#333;}#mermaidChart1 .cluster span{color:#333;}#mermaidChart1 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#mermaidChart1 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#mermaidChart1 rect.text{fill:none;stroke-width:0;}#mermaidChart1 .icon-shape,#mermaidChart1 .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#mermaidChart1 .icon-shape p,#mermaidChart1 .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#mermaidChart1 .icon-shape rect,#mermaidChart1 .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#mermaidChart1 :root{--mermaid-alt-font-family:sans-serif;}#mermaidChart1 .primary&gt;*{fill:#f9f!important;stroke:#333!important;stroke-width:2px!important;}#mermaidChart1 .primary span{fill:#f9f!important;stroke:#333!important;stroke-width:2px!important;}#mermaidChart1 .secondary&gt;*{fill:#bbf!important;stroke:#333!important;}#mermaidChart1 .secondary span{fill:#bbf!important;stroke:#333!important;}</style><g style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><marker id="mermaidChart1_flowchart-v2-pointEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 0 0 L 10 5 L 0 10 z" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart1_flowchart-v2-pointStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="4.5" refY="5" markerUnits="userSpaceOnUse" markerWidth="8" markerHeight="8" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 0 5 L 10 10 L 10 0 z" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart1_flowchart-v2-circleEnd" class="marker flowchart-v2" viewBox="0 0 10 10" refX="11" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></circle></marker><marker id="mermaidChart1_flowchart-v2-circleStart" class="marker flowchart-v2" viewBox="0 0 10 10" refX="-1" refY="5" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><circle cx="5" cy="5" r="5" class="arrowMarkerPath" style="stroke-width: 1px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></circle></marker><marker id="mermaidChart1_flowchart-v2-crossEnd" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="12" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><marker id="mermaidChart1_flowchart-v2-crossStart" class="marker cross flowchart-v2" viewBox="0 0 11 11" refX="-1" refY="5.2" markerUnits="userSpaceOnUse" markerWidth="11" markerHeight="11" orient="auto" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M 1,1 l 9,9 M 10,1 l -9,9" class="arrowMarkerPath" style="stroke-width: 2px; stroke-dasharray: 1px, 0px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: rgb(51, 51, 51); opacity: 1; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></path></marker><g class="root" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="clusters" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="cluster " id="backend" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="8" y="291" width="1081.234375" height="483.7067413330078"></rect><g class="cluster-label " transform="translate(484.5859375, 291)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="128.0625" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Backend Services</p></span></div></foreignObject></g></g><g class="cluster " id="data_sources" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="28" y="622" width="336.8125" height="127.70674133300781"></rect><g class="cluster-label " transform="translate(147.9375, 622)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="96.9375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Data Sources</p></span></div></foreignObject></g></g><g class="cluster " id="generation_services" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="384.8125" y="622" width="684.421875" height="127.70674133300781"></rect><g class="cluster-label " transform="translate(654.5390625, 622)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="144.96875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Generation Services</p></span></div></foreignObject></g></g><g class="cluster " id="ai_services" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="46.375" y="444" width="951.8984375" height="104"></rect><g class="cluster-label " transform="translate(472.96484375, 444)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="98.71875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">AI Processing</p></span></div></foreignObject></g></g></g><g class="edgePaths" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M566.165,370L557.446,376.167C548.727,382.333,531.289,394.667,522.57,407C513.852,419.333,513.852,431.667,513.852,441.333C513.852,451,513.852,458,513.852,461.5L513.852,465" id="L_api_work_flows_2" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M676.137,357.768L716.029,365.973C755.922,374.178,835.707,390.589,875.6,404.961C915.492,419.333,915.492,431.667,915.492,441.333C915.492,451,915.492,458,915.492,461.5L915.492,465" id="L_api_mcp_3" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M604.34,62L604.34,66.167C604.34,70.333,604.34,78.667,604.34,86.333C604.34,94,604.34,101,604.34,104.5L604.34,108" id="L_client_frontend_0" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M604.34,241L604.34,245.167C604.34,249.333,604.34,257.667,604.34,266C604.34,274.333,604.34,282.667,604.34,290.333C604.34,298,604.34,305,604.34,308.5L604.34,312" id="L_frontend_api_1" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M263.156,548L263.156,554.167C263.156,560.333,263.156,572.667,263.156,585C263.156,597.333,263.156,609.667,263.156,619.333C263.156,629,263.156,636,263.156,639.5L263.156,643" id="L_ai_services_vectordb_4" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M104.75,548L104.75,554.167C104.75,560.333,104.75,572.667,104.75,585C104.75,597.333,104.75,609.667,104.75,620.056C104.75,630.445,104.75,638.89,104.75,643.113L104.75,647.335" id="L_ai_services_db_5" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M513.852,548L513.852,554.167C513.852,560.333,513.852,572.667,513.852,585C513.852,597.333,513.852,609.667,513.852,621.309C513.852,632.951,513.852,643.902,513.852,649.378L513.852,654.853" id="L_ai_services_img_gen_6" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M750.156,548L750.156,554.167C750.156,560.333,750.156,572.667,750.156,585C750.156,597.333,750.156,609.667,750.156,621.309C750.156,632.951,750.156,643.902,750.156,649.378L750.156,654.853" id="L_ai_services_audio_gen_7" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path><path d="M963.328,548L963.328,554.167C963.328,560.333,963.328,572.667,963.328,585C963.328,597.333,963.328,609.667,963.328,621.309C963.328,632.951,963.328,643.902,963.328,649.378L963.328,654.853" id="L_ai_services_tool_exec_8" class=" edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: none; stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: 0px; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" marker-end="url(#mermaidChart1_flowchart-v2-pointEnd)"></path></g><g class="edgeLabels" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="edgeLabel" transform="translate(513.8515625, 407)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-92.4921875, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="184.984375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">High Frequency Requests</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(915.4921875, 407)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-68.484375, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="136.96875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Long Tail Demands</p></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(0, 0)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="0" height="0" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(263.15625, 597.5)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-17.3359375, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="34.671875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">RAG</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(104.75, 599.66769)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-36.015625, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="72.03125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">User Data</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(513.85156, 603.42669)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-89.8359375, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="179.671875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Generate Image Request</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(750.15625, 603.42669)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-88.0625, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="176.125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Generate Audio Request</p></span></div></foreignObject></g></g><g class="edgeLabel" transform="translate(963.32813, 603.42669)" style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="label" transform="translate(-72.046875, -12)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="144.09375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(232, 232, 232, 0.5); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="edgeLabel " style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(232, 232, 232, 0.8); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Tool Usage Request</p></span></div></foreignObject></g></g></g><g class="nodes" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="root" transform="translate(493.09765625, 104)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="clusters" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="cluster " id="frontend" data-look="classic" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 255, 222); stroke: rgb(170, 170, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="8" y="8" width="206.484375" height="129"></rect><g class="cluster-label " transform="translate(79.21875, 8)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><foreignObject width="64.046875" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Frontend</p></span></div></foreignObject></g></g></g><g class="edgePaths" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></g><g class="edgeLabels" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></g><g class="nodes" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><g class="node default  " id="flowchart-chatbot-29" transform="translate(111.2421875, 72.5)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-68.2421875" y="-27" width="136.484375" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-38.2421875, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="76.484375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Chatbot UI</p></span></div></foreignObject></g></g></g></g><g class="node default  " id="flowchart-client-27" transform="translate(604.33984375, 35)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-50.453125" y="-27" width="100.90625" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-20.453125, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="40.90625" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Client</p></span></div></foreignObject></g></g><g class="node default primary " id="flowchart-api-31" transform="translate(604.33984375, 343)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-71.796875" y="-27" width="143.59375" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-41.796875, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="83.59375" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">API Service</p></span></div></foreignObject></g></g><g class="node default primary " id="flowchart-work_flows-33" transform="translate(513.8515625, 496)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-71.640625" y="-27" width="143.28125" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-41.640625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="83.28125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Work Flows</p></span></div></foreignObject></g></g><g class="node default primary " id="flowchart-mcp-34" transform="translate(915.4921875, 496)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-47.78125" y="-27" width="95.5625" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-17.78125, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="35.5625" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(255, 153, 255); stroke: rgb(51, 51, 51); stroke-width: 2px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">MCP</p></span></div></foreignObject></g></g><g class="node default secondary " id="flowchart-img_gen-35" transform="translate(513.8515625, 685.8533706665039)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-94.0390625" y="-27" width="188.078125" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-64.0390625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="128.078125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Image Generation</p></span></div></foreignObject></g></g><g class="node default secondary " id="flowchart-audio_gen-36" transform="translate(750.15625, 685.8533706665039)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-92.265625" y="-27" width="184.53125" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-62.265625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="124.53125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Audio Generation</p></span></div></foreignObject></g></g><g class="node default secondary " id="flowchart-tool_exec-37" transform="translate(963.328125, 685.8533706665039)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><rect class="basic label-container" style="fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" x="-70.90625" y="-27" width="141.8125" height="54"></rect><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-40.90625, -12)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="81.8125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Other Tools</p></span></div></foreignObject></g></g><g class="node default secondary " id="flowchart-db-38" transform="translate(104.75, 685.8533706665039)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M0,10.011990407673862 a41.75,10.011990407673862 0,0,0 83.5,0 a41.75,10.011990407673862 0,0,0 -83.5,0 l0,49.01199040767386 a41.75,10.011990407673862 0,0,0 83.5,0 l0,-49.01199040767386" class="basic label-container" style="fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" label-offset-y="10.011990407673862" transform="translate(-41.75, -34.517985611510795)"></path><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-34.25, -2)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="68.5" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Database</p></span></div></foreignObject></g></g><g class="node default secondary " id="flowchart-vectordb-39" transform="translate(263.15625, 685.8533706665039)" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(51, 51, 51); stroke: none; stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><path d="M0,12.902250181466249 a66.65625,12.902250181466249 0,0,0 133.3125,0 a66.65625,12.902250181466249 0,0,0 -133.3125,0 l0,51.90225018146625 a66.65625,12.902250181466249 0,0,0 133.3125,0 l0,-51.90225018146625" class="basic label-container" style="fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" label-offset-y="12.902250181466249" transform="translate(-66.65625, -38.85337527219937)"></path><g class="label" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;" transform="translate(-59.15625, -2)"><rect style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(236, 236, 255); stroke: rgb(147, 112, 219); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"></rect><foreignObject width="118.3125" height="24" style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center; background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><span class="nodeLabel " style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;"><p style="background-color: rgba(0, 0, 0, 0); color: rgb(51, 51, 51); fill: rgb(187, 187, 255); stroke: rgb(51, 51, 51); stroke-width: 1px; opacity: 1; stroke-dasharray: none; font-size: 16px; font-family: sans-serif; text-anchor: start; padding: 0px; margin: 0px;">Vector Database</p></span></div></foreignObject></g></g></g></g></g></svg>