# Mission API Documentation

This document describes the Mission API endpoints that allow you to create missions and check their status based on conversation history.

## Endpoints

### 1. Create Mission

**POST** `/api/mission`

Creates a new mission based on the provided mission details.

**Headers:**
- `X-API-KEY`: Your API key
- `Content-Type`: application/json

**Request Body:**
```json
{
    "mission_details": "You are preparing for a job interview. Please create a mission plan about the interview process dialogue that includes the following: - A polite greeting - A brief introduction of your resume - Your strengths and weaknesses - Your goals in this position - Your expected salary range - Questions for the interviewer"
}
```

**Response:**
```json
{
    "task_id": "uuid-string",
    "status": "queued"
}
```

### 2. Get Mission Result

**GET** `/api/mission/{task_id}`

Retrieves the result of a mission creation task.

**Headers:**
- `X-API-KEY`: Your API key

**Response:**
```json
{
    "task_id": "uuid-string",
    "status": "completed",
    "mission": {
        "title": "Job Interview Process Mission",
        "description": "This mission is to guide you through a job interview process...",
        "objectives": [
            "Make a good first impression with a polite greeting",
            "Effectively introduce your resume",
            "..."
        ],
        "steps": [
            {
                "action": "Enter the interview room and greet the interviewer politely...",
                "status": "pending"
            }
        ]
    }
}
```

### 3. Check Mission Status

**POST** `/api/mission/check`

Checks the status of a mission based on conversation history.

**Headers:**
- `X-API-KEY`: Your API key
- `Content-Type`: application/json

**Request Body:**
```json
{
    "mission": {
        "title": "Job Interview Process Mission",
        "description": "This mission is to guide you through a job interview process...",
        "objectives": ["..."],
        "steps": [
            {
                "action": "Enter the interview room and greet the interviewer politely...",
                "status": "pending"
            }
        ]
    },
    "conversation_history": "Interviewer: Good morning, thank you for coming in today.\nCandidate: Good morning, thank you for having me. I'm excited to be here."
}
```

**Response:**
```json
{
    "task_id": "uuid-string",
    "status": "queued"
}
```

### 4. Get Mission Check Result

**GET** `/api/mission/check/{task_id}`

Retrieves the result of a mission status check task.

**Headers:**
- `X-API-KEY`: Your API key

**Response:**
```json
{
    "task_id": "uuid-string",
    "status": "completed",
    "updated_mission": {
        "title": "Job Interview Process Mission",
        "description": "This mission is to guide you through a job interview process...",
        "objectives": ["..."],
        "steps": [
            {
                "action": "Enter the interview room and greet the interviewer politely...",
                "status": "completed"
            },
            {
                "action": "Briefly introduce your resume...",
                "status": "in_progress"
            }
        ]
    }
}
```

## Status Values

- `queued`: Task has been submitted and is waiting to be processed
- `processing`: Task is currently being processed
- `completed`: Task has completed successfully
- `failed`: Task has failed with an error

## Step Status Values

- `pending`: Step has not been started
- `in_progress`: Step is currently being worked on
- `completed`: Step has been completed successfully
- `failed`: Step has failed

## Web Interface

You can test the Mission API using the web interface at `/mission` which provides a user-friendly form to create missions and check their status.

## Example Usage

1. **Create a mission** by calling `/api/mission` with mission details
2. **Poll for the result** using `/api/mission/{task_id}` until status is "completed"
3. **Check mission progress** by calling `/api/mission/check` with the mission data and conversation history
4. **Get the updated mission** using `/api/mission/check/{task_id}` to see which steps have been completed

## Error Handling

All endpoints return appropriate HTTP status codes:
- `200`: Success
- `401`: Missing or invalid API key
- `404`: Task not found
- `500`: Internal server error

Error responses include a `detail` field with more information about the error.
