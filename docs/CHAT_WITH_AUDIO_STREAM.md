

```mermaid
sequenceDiagram
    participant Client
    participant Server
    
    Note over Client, Server: Chat with TTS Streaming
    
    Client->>Server: Connect to /api/chat_with_tts_stream
    Server->>Client: Accept connection
    
    Client->>Server: Send init config<br/>{voice, speed, emotion}
    Server->>Client: {"status": "ready"}
    
    loop Chat Messages
        Client->>Server: Send message<br/>{messages: [...]}
        
        Note over Server: Process message & start TTS
        
        loop Streaming Response
            Server->>Client: {"type": "text", "content": "chunk"}
            Server->>Client: {"type": "audio", "data": "base64"}
        end
        
        Server->>Client: {"type": "complete", "full_response": "..."}
    end
    
    Client->>Server: Disconnect
    Note over Server: Cleanup resources
```

