# 测试失败后继续执行的配置选项

## 当前配置：使用 `allow_failure: true`

已经为测试作业添加了 `allow_failure: true`，这意味着：

✅ **优点：**
- 测试失败不会阻止后续的构建和部署
- 测试结果仍然会被记录和显示
- 可以看到测试覆盖率报告
- 流水线状态会显示为"通过但有警告"

⚠️ **注意：**
- 测试失败的代码仍然会被部署
- 需要手动检查测试结果

## 其他可选方案

### 方案1：完全移除测试依赖

```yaml
build:
  stage: build
  # 移除 needs: - test
  script:
    - docker build -t ${IMAGE} .
```

### 方案2：使用条件依赖

```yaml
build:
  stage: build
  needs:
    - job: test
      optional: true  # 测试作业可选
  script:
    - docker build -t ${IMAGE} .
```

### 方案3：在测试脚本中处理失败

```bash
# 在 run_tests.py 中
try:
    result = subprocess.run(cmd)
    return 0  # 总是返回成功
except:
    return 0  # 即使失败也返回成功
```

### 方案4：使用 `exit_codes` 配置

```yaml
test:
  stage: test
  script:
    - uv run python run_tests.py
  allow_failure:
    exit_codes: [1, 2]  # 允许特定退出码失败
```

## 推荐配置

当前使用的 `allow_failure: true` 是最佳选择，因为：

1. **保持测试可见性**：测试仍然运行并生成报告
2. **不阻止部署**：失败不会停止流水线
3. **提供警告**：流水线状态会显示有警告
4. **易于调试**：可以查看测试失败的详细信息

## 流水线行为

使用 `allow_failure: true` 后：

```
┌─────────┐    ┌─────────┐    ┌─────────┐
│  Test   │───▶│  Build  │───▶│ Deploy  │
│ ⚠️ 失败  │    │ ✅ 成功  │    │ ✅ 成功  │
└─────────┘    └─────────┘    └─────────┘
```

- 测试失败显示为警告（黄色）
- 构建和部署正常进行
- 整个流水线状态为"通过但有警告"

## 监控建议

1. **设置通知**：配置 GitLab 通知，当测试失败时发送警告
2. **定期检查**：定期查看测试报告，修复失败的测试
3. **覆盖率监控**：关注测试覆盖率变化
4. **质量门禁**：考虑在重要分支（如 main）上保持严格的测试要求

## 恢复严格测试

如果以后想要恢复严格的测试要求，只需移除 `allow_failure: true`：

```yaml
test:
  stage: test
  # 移除 allow_failure: true
  image: python:3.10-slim
```
