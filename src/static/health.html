<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gusto AI Base Service - Server Status</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }

    h1 {
      color: #2c3e50;
      border-bottom: 2px solid #eee;
      padding-bottom: 10px;
    }

    .status-card {
      background-color: #f9f9f9;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .status-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-online {
      background-color: #2ecc71;
    }

    .status-offline {
      background-color: #e74c3c;
    }

    .status-unknown {
      background-color: #f39c12;
    }

    .status-item {
      margin-bottom: 10px;
    }

    .label {
      font-weight: bold;
      margin-right: 8px;
    }

    .refresh-btn {
      background-color: #3498db;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .refresh-btn:hover {
      background-color: #2980b9;
    }

    .last-updated {
      font-size: 12px;
      color: #7f8c8d;
      margin-top: 20px;
    }
  </style>
</head>

<body>
  <h1>Gusto AI Base Service - Server Status</h1>

  <div class="status-card" id="status-container">
    <div class="status-item">
      <span class="status-indicator status-unknown" id="status-indicator"></span>
      <span class="label">Status:</span>
      <span id="status">Loading...</span>
    </div>
    <div class="status-item">
      <span class="label">Version:</span>
      <span id="version">Loading...</span>
    </div>
    <div class="status-item">
      <span class="label">Environment:</span>
      <span id="environment">Loading...</span>
    </div>
    <div class="status-item">
      <span class="label">Database:</span>
      <span id="database">Loading...</span>
    </div>
    <div class="status-item">
      <span class="label">Uptime:</span>
      <span id="uptime">Loading...</span>
    </div>
  </div>

  <button class="refresh-btn" onclick="fetchStatus()">Refresh Status</button>

  <p class="last-updated">Last updated: <span id="last-updated">Never</span></p>

  <script>
    // Function to format uptime in a human-readable format
    function formatUptime(seconds) {
      const days = Math.floor(seconds / (3600 * 24));
      const hours = Math.floor((seconds % (3600 * 24)) / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = Math.floor(seconds % 60);

      let result = '';
      if (days > 0) result += `${days} day${days > 1 ? 's' : ''} `;
      if (hours > 0 || days > 0) result += `${hours} hour${hours > 1 ? 's' : ''} `;
      if (minutes > 0 || hours > 0 || days > 0) result += `${minutes} minute${minutes > 1 ? 's' : ''} `;
      result += `${remainingSeconds} second${remainingSeconds > 1 ? 's' : ''}`;

      return result;
    }

    // Function to fetch server status
    async function fetchStatus() {
      try {
        const response = await fetch('/api/status');
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();

        // Update the status indicator
        const statusIndicator = document.getElementById('status-indicator');
        if (data.status === 'online') {
          statusIndicator.className = 'status-indicator status-online';
        } else {
          statusIndicator.className = 'status-indicator status-offline';
        }

        // Update the status information
        document.getElementById('status').textContent = data.status || 'Unknown';
        document.getElementById('version').textContent = data.version || 'Unknown';
        document.getElementById('environment').textContent = data.environment || 'Unknown';
        document.getElementById('database').textContent = data.database || 'Unknown';
        document.getElementById('uptime').textContent = formatUptime(data.uptime_seconds) || 'Unknown';

        // Update last updated timestamp
        document.getElementById('last-updated').textContent = new Date().toLocaleString();
      } catch (error) {
        console.error('Error fetching status:', error);
        document.getElementById('status-indicator').className = 'status-indicator status-offline';
        document.getElementById('status').textContent = 'Error connecting to server';
        document.getElementById('last-updated').textContent = new Date().toLocaleString() + ' (failed)';
      }
    }

    // Fetch status on page load
    document.addEventListener('DOMContentLoaded', fetchStatus);

    // Auto-refresh every 10 seconds
    setInterval(fetchStatus, 10000);
  </script>
</body>

</html>