<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Grammar Suggestion Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #text-input {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 15px;
      width: 100%;
      min-height: 200px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
    }

    #text-input:focus {
      outline: none;
      border-color: #0056b3;
      box-shadow: 0 0 5px rgba(0, 86, 179, 0.3);
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    .result-section {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 15px;
      border-left: 5px solid #28a745;
    }

    #errors-section {
      border-left: 5px solid #dc3545;
    }

    #improvements-section {
      border-left: 5px solid #fd7e14;
    }

    .error-item {
      color: #dc3545;
      margin-bottom: 8px;
    }

    .improvement-item {
      color: #fd7e14;
      margin-bottom: 8px;
    }

    .suggested-text {
      background-color: #e8f4ff;
      padding: 15px;
      border-radius: 5px;
      border-left: 5px solid #007bff;
      margin-top: 15px;
      white-space: pre-wrap;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Grammar Suggestion Service</h1>
      <p class="lead text-muted">Improve your writing with AI-powered grammar suggestions</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Enter Your Text</span>
      <p class="mb-4">Type or paste your text below for grammar checking and improvement suggestions.</p>

      <textarea id="text-input" placeholder="Enter your text here..."></textarea>

      <div class="mt-3">
        <button id="submit-button" class="btn btn-success">Get Suggestions</button>
        <button id="clear-button" class="btn btn-outline-secondary ms-2">Clear</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">View Suggestions</span>
      <p class="mb-4">Once processing is complete, your grammar suggestions will appear below.</p>

      <div id="task-status" class="d-none">
        <p>
          Task ID: <span id="task-id" class="fw-bold"></span>
          <span class="ms-3">Status: <span id="status-badge"
              class="badge rounded-pill status-badge bg-secondary">Unknown</span></span>
        </p>
        <div class="progress mb-3" style="height: 10px;">
          <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%">
          </div>
        </div>
      </div>

      <div id="result-panel" class="mt-4">
        <div id="errors-section" class="result-section">
          <h5>Errors Found</h5>
          <div id="errors-list">
            <!-- Errors will be inserted here -->
          </div>
        </div>

        <div id="improvements-section" class="result-section">
          <h5>Suggested Improvements</h5>
          <div id="improvements-list" class="warning-list">
            <!-- Improvements will be inserted here -->
          </div>
        </div>

        <div id="suggested-text-section" class="result-section">
          <h5>Suggested Text</h5>
          <div id="suggested-text" class="suggested-text">
            <!-- Suggested text will be inserted here -->
          </div>
        </div>

        <button id="copy-button" class="btn btn-outline-primary mt-3">Copy Suggested Text</button>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Grammar Suggestion Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const textInput = document.getElementById('text-input');
      const submitButton = document.getElementById('submit-button');
      const clearButton = document.getElementById('clear-button');
      const errorMessage = document.getElementById('error-message');
      const taskStatus = document.getElementById('task-status');
      const taskIdElement = document.getElementById('task-id');
      const statusBadge = document.getElementById('status-badge');
      const progressBar = document.getElementById('progress-bar');
      const resultPanel = document.getElementById('result-panel');
      const errorsList = document.getElementById('errors-list');
      const improvementsList = document.getElementById('improvements-list');
      const suggestedText = document.getElementById('suggested-text');
      const copyButton = document.getElementById('copy-button');

      // Variables
      let taskId = null;
      let statusCheckInterval = null;

      // Event Listeners
      submitButton.addEventListener('click', submitText);
      clearButton.addEventListener('click', clearText);

      copyButton.addEventListener('click', () => {
        navigator.clipboard.writeText(suggestedText.textContent.trim())
          .then(() => {
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
              copyButton.textContent = 'Copy Suggested Text';
            }, 2000);
          });
      });

      // Functions
      function submitText() {
        const text = textInput.value.trim();
        if (!text) {
          showError('Please enter some text for grammar checking');
          return;
        }

        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        hideError();

        // Reset any previous results
        if (statusCheckInterval) {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;
        }
        resultPanel.style.display = 'none';

        // Call the grammar suggestion API
        fetch('/api/grammar_suggestion', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({ text: text })
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          })
          .then(data => {
            taskId = data.task_id;
            taskIdElement.textContent = taskId;

            taskStatus.classList.remove('d-none');
            updateStatus(data.status);

            // Start checking status
            statusCheckInterval = setInterval(checkStatus, 3000);
          })
          .catch(error => {
            showError('Submission failed: ' + error.message);
            submitButton.disabled = false;
            submitButton.textContent = 'Get Suggestions';
          });
      }

      function checkStatus() {
        fetch(`/api/grammar_suggestion/${taskId}`, {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to get task status');
            }
            return response.json();
          })
          .then(data => {
            updateStatus(data.status);

            if (data.status === 'completed') {
              clearInterval(statusCheckInterval);
              displayResults(data.suggestions);
            } else if (data.status === 'failed') {
              clearInterval(statusCheckInterval);
              showError('Grammar checking failed');
            }
          })
          .catch(error => {
            showError('Status check failed: ' + error.message);
          });
      }

      function updateStatus(status) {
        statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);

        // Update badge color
        statusBadge.className = 'badge rounded-pill status-badge';

        if (status === 'queued') {
          statusBadge.classList.add('bg-secondary');
          progressBar.style.width = '25%';
        } else if (status === 'processing') {
          statusBadge.classList.add('bg-primary');
          progressBar.style.width = '75%';
        } else if (status === 'completed') {
          statusBadge.classList.add('bg-success');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
        } else if (status === 'failed') {
          statusBadge.classList.add('bg-danger');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
          progressBar.classList.add('bg-danger');
        }

        // Reset submit button
        submitButton.disabled = false;
        submitButton.textContent = 'Get Suggestions';
      }

      function displayResults(suggestions) {
        if (!suggestions) {
          showError('No suggestions returned');
          return;
        }

        // Clear previous results
        errorsList.innerHTML = '';
        improvementsList.innerHTML = '';
        suggestedText.textContent = '';

        // Display errors
        if (suggestions.errors && suggestions.errors.length > 0) {
          suggestions.errors.forEach(error => {
            const errorItem = document.createElement('div');
            errorItem.className = 'error-item';
            errorItem.innerHTML = `<i class="bi bi-exclamation-circle me-2"></i> ${error}`;
            errorsList.appendChild(errorItem);
          });
        } else {
          errorsList.innerHTML = '<p>No errors found.</p>';
        }

        // Display improvements
        if (suggestions.improvements && suggestions.improvements.length > 0) {
          suggestions.improvements.forEach(improvement => {
            const improvementItem = document.createElement('div');
            improvementItem.className = 'improvement-item';
            improvementItem.innerHTML = `<i class="bi bi-lightbulb me-2"></i> ${improvement}`;
            improvementsList.appendChild(improvementItem);
          });
        } else {
          improvementsList.innerHTML = '<p>No improvement suggestions.</p>';
        }

        // Display suggested text if available
        if (suggestions.suggested_text) {
          suggestedText.textContent = suggestions.suggested_text;
        } else {
          suggestedText.textContent = 'No suggested text provided.';
        }

        // Show the results panel
        resultPanel.style.display = 'block';
      }

      function clearText() {
        textInput.value = '';
        hideError();

        if (statusCheckInterval) {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;
        }

        taskStatus.classList.add('d-none');
        resultPanel.style.display = 'none';
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function getApiKey() {
        // You can implement this in different ways:
        // 1. Read from localStorage if you have a settings page
        // 2. Prompt the user
        // 3. Set it in a configuration

        let apiKey = localStorage.getItem('apiKey');

        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }

        return apiKey || '';
      }
    });
  </script>
</body>

</html>