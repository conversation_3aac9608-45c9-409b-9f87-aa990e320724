<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Translation Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #text-input {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 15px;
      width: 100%;
      min-height: 150px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
    }

    #text-input:focus {
      outline: none;
      border-color: #0056b3;
      box-shadow: 0 0 5px rgba(0, 86, 179, 0.3);
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    #translation-panel {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      border-left: 5px solid #28a745;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Translation Service</h1>
      <p class="lead text-muted">Translate your text between Chinese and English with our AI-powered service</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Enter Your Text</span>
      <p class="mb-4">Type or paste your text below for translation.</p>
      <textarea id="text-input" placeholder="Enter text to translate..."></textarea>
      <div class="mt-3">
        <button id="submit-button" class="btn btn-success">Translate</button>
        <button id="clear-button" class="btn btn-outline-secondary ms-2">Clear</button>
      </div>
      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">View Translation</span>
      <p class="mb-4">Once processing is complete, your translated text will be displayed below.</p>
      <div id="task-status" class="d-none">
        <p>
          Task ID: <span id="task-id" class="fw-bold"></span>
          <span class="ms-3">Status: <span id="status-badge"
              class="badge rounded-pill status-badge bg-secondary">Unknown</span></span>
        </p>
        <div class="progress mb-3" style="height: 10px;">
          <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%">
          </div>
        </div>
      </div>
      <div id="result-panel">
        <div id="translation-panel" class="text-center">
          <p id="translated-text" class="fs-5"></p>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Translation Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const textInput = document.getElementById('text-input');
      const submitButton = document.getElementById('submit-button');
      const clearButton = document.getElementById('clear-button');
      const errorMessage = document.getElementById('error-message');
      const taskStatus = document.getElementById('task-status');
      const taskIdElement = document.getElementById('task-id');
      const statusBadge = document.getElementById('status-badge');
      const progressBar = document.getElementById('progress-bar');
      const resultPanel = document.getElementById('result-panel');
      const translatedTextElem = document.getElementById('translated-text');

      let taskId = null;
      let statusCheckInterval = null;

      submitButton.addEventListener('click', submitText);
      clearButton.addEventListener('click', clearText);

      function submitText() {
        const text = textInput.value.trim();
        if (!text) {
          showError('Please enter some text for translation');
          return;
        }
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        hideError();
        if (statusCheckInterval) {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;
        }
        resultPanel.style.display = 'none';

        fetch('/api/translate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({ text: text })
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok.');
            }
            return response.json();
          })
          .then(data => {
            taskId = data.task_id;
            taskIdElement.textContent = taskId;
            taskStatus.classList.remove('d-none');
            updateStatus(data.status);
            statusCheckInterval = setInterval(checkStatus, 3000);
          })
          .catch(error => {
            showError('Submission failed: ' + error.message);
            submitButton.disabled = false;
            submitButton.textContent = 'Translate';
          });
      }

      function checkStatus() {
        fetch(`/api/translate/${taskId}`, {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to get task status.');
            }
            return response.json();
          })
          .then(data => {
            updateStatus(data.status);
            if (data.status === 'completed') {
              clearInterval(statusCheckInterval);
              if (data.translated_text) {
                translatedTextElem.textContent = data.translated_text;
                resultPanel.style.display = 'block';
              } else {
                showError('No translation received.');
              }
            }
            else if (data.status === 'failed') {
              clearInterval(statusCheckInterval);
              showError('Translation processing failed.');
            }
          })
          .catch(error => {
            showError('Status check failed: ' + error.message);
          });
      }

      function updateStatus(status) {
        statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        statusBadge.className = 'badge rounded-pill status-badge';
        if (status === 'queued') {
          statusBadge.classList.add('bg-secondary');
          progressBar.style.width = '25%';
        } else if (status === 'processing') {
          statusBadge.classList.add('bg-primary');
          progressBar.style.width = '75%';
        } else if (status === 'completed') {
          statusBadge.classList.add('bg-success');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
        } else if (status === 'failed') {
          statusBadge.classList.add('bg-danger');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
          progressBar.classList.add('bg-danger');
        }
        submitButton.disabled = false;
        submitButton.textContent = 'Translate';
      }

      function clearText() {
        textInput.value = '';
        hideError();
        if (statusCheckInterval) {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;
        }
        taskStatus.classList.add('d-none');
        resultPanel.style.display = 'none';
        translatedTextElem.textContent = '';
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }
        return apiKey || '';
      }
    });
  </script>
</body>

</html>