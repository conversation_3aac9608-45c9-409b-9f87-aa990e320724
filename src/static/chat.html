<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI Chat</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Fira+Code&display=swap"
    rel="stylesheet">
  <link rel="stylesheet" href="/static/css/chat.css">
  <style>
    /* Streaming message styles */
    .message.streaming .streaming-content {
      position: relative;
      min-height: 1.2em;
    }

    .message.streaming::after {
      content: '▍';
      color: #007bff;
      animation: blink 1s infinite;
      font-weight: bold;
    }

    @keyframes blink {

      0%,
      50% {
        opacity: 1;
      }

      51%,
      100% {
        opacity: 0;
      }
    }

    /* Typing indicator styles */
    .typing-dots {
      display: inline-flex;
      align-items: center;
      gap: 4px;
      padding: 8px 0;
    }

    .typing-dots span {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #999;
      animation: typing 1.4s infinite ease-in-out;
    }

    .typing-dots span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .typing-dots span:nth-child(2) {
      animation-delay: -0.16s;
    }

    @keyframes typing {

      0%,
      80%,
      100% {
        transform: scale(0.8);
        opacity: 0.5;
      }

      40% {
        transform: scale(1);
        opacity: 1;
      }
    }

    /* Smooth scrolling for streaming content */
    .chat-messages {
      scroll-behavior: smooth;
    }

    /* Streaming content animation */
    .streaming-content {
      transition: all 0.1s ease-out;
    }
  </style>
  <!-- Add after other style imports -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
  <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js"></script>
</head>

<body>
  <!-- Bot Panel -->
  <div class="bot-panel">
    <div class="bot-list" id="botList">
      <!-- Bots will be added here dynamically -->
    </div>
    <div class="add-bot-btn" id="addBotBtn">
      + Add New Bot
    </div>
    <div class="add-bot-btn" id="apiKeyBtn">
      ⚙️ API Key Settings
    </div>
  </div>

  <!-- Chat Panel -->
  <div class="chat-panel">
    <div class="chat-header">
      <h2 id="currentBotName">Select a bot to start chatting</h2>
    </div>
    <div class="chat-messages" id="chatMessages">
      <!-- Messages will be added here dynamically -->
    </div>

    <!-- New toolbar -->
    <div class="chat-toolbar">
      <button id="clearHistoryBtn" class="toolbar-button">
        <span class="toolbar-icon">🗑️</span> Clear History
      </button>
      <button id="exportChatBtn" class="toolbar-button">
        <span class="toolbar-icon">📤</span> Export Chat
      </button>
      <button id="importChatBtn" class="toolbar-button">
        <span class="toolbar-icon">📥</span> Import Chat
      </button>
    </div>

    <div class="chat-input">
      <textarea id="messageInput" placeholder="Type a message..."></textarea>
      <button id="sendButton">Send</button>
    </div>
  </div>

  <!-- Modal for Adding/Editing Bot -->
  <div class="modal" id="botModal">
    <div class="modal-content">
      <h2 id="modalTitle">Add New Bot</h2>
      <div class="form-group">
        <label for="botName">Bot Name</label>
        <input type="text" id="botName" placeholder="Enter bot name">
      </div>
      <div class="form-group">
        <label for="botPersona">Bot Persona</label>
        <textarea id="botPersona" placeholder="Describe the bot's personality and capabilities"></textarea>
      </div>
      <div class="form-group">
        <button id="improvePersonaBtn" class="toolbar-button">
          <span class="toolbar-icon">✨</span> Improve Persona with AI
        </button>
      </div>
      <input type="hidden" id="botId">
      <div class="modal-buttons">
        <button class="cancel" id="cancelModal">Cancel</button>
        <button class="save" id="saveBot">Save</button>
      </div>
    </div>
  </div>

  <!-- API Key Settings Modal -->
  <div class="modal" id="apiKeyModal">
    <div class="modal-content">
      <h2>API Key Settings</h2>
      <div class="form-group">
        <label for="apiKeyInput">API Key</label>
        <input type="password" id="apiKeyInput" placeholder="Enter your API key">
      </div>
      <div class="modal-buttons">
        <button class="cancel" id="cancelApiKeyModal">Cancel</button>
        <button class="save" id="saveApiKey">Save</button>
      </div>
    </div>
  </div>

  <!-- Add a hidden file input for import -->
  <input type="file" id="importFileInput" style="display: none;" accept=".json">

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const botList = document.getElementById('botList');
      const addBotBtn = document.getElementById('addBotBtn');
      const botModal = document.getElementById('botModal');
      const modalTitle = document.getElementById('modalTitle');
      const botNameInput = document.getElementById('botName');
      const botPersonaInput = document.getElementById('botPersona');
      const botIdInput = document.getElementById('botId');
      const cancelModal = document.getElementById('cancelModal');
      const saveBot = document.getElementById('saveBot');
      const improvePersonaBtn = document.getElementById('improvePersonaBtn'); // Add this line
      const currentBotName = document.getElementById('currentBotName');
      const chatMessages = document.getElementById('chatMessages');
      const messageInput = document.getElementById('messageInput');
      const sendButton = document.getElementById('sendButton');
      const apiKeyBtn = document.getElementById('apiKeyBtn');
      const apiKeyModal = document.getElementById('apiKeyModal');
      const apiKeyInput = document.getElementById('apiKeyInput');
      const saveApiKey = document.getElementById('saveApiKey');
      const cancelApiKeyModal = document.getElementById('cancelApiKeyModal');
      const clearHistoryBtn = document.getElementById('clearHistoryBtn');
      const exportChatBtn = document.getElementById('exportChatBtn');
      const importChatBtn = document.getElementById('importChatBtn');
      const importFileInput = document.getElementById('importFileInput');

      // State
      let bots = JSON.parse(localStorage.getItem('bots')) || [];
      let currentBotId = localStorage.getItem('currentBotId') || null;
      let chats = JSON.parse(localStorage.getItem('chats')) || {};
      let autoTTSEnabled = localStorage.getItem('autoTTSEnabled') === 'true'; // Default to false

      // Initialize
      renderBots();
      loadCurrentChat();

      // Add this after other DOM elements at the top
      const chatToolbar = document.querySelector('.chat-toolbar');
      const autoTTSToggle = document.createElement('button');
      autoTTSToggle.classList.add('toolbar-button');
      autoTTSToggle.innerHTML = autoTTSEnabled ? '🔊 Auto TTS: ON' : '🔈 Auto TTS: OFF';
      autoTTSToggle.title = 'Toggle automatic text-to-speech';
      autoTTSToggle.addEventListener('click', toggleAutoTTS);
      chatToolbar.appendChild(autoTTSToggle);

      // Event Listeners
      addBotBtn.addEventListener('click', () => openAddBotModal());
      cancelModal.addEventListener('click', () => closeModal());
      saveBot.addEventListener('click', () => saveBotData());
      clearHistoryBtn.addEventListener('click', () => clearChatHistory());
      exportChatBtn.addEventListener('click', () => exportChat());
      importChatBtn.addEventListener('click', () => importChat());

      sendButton.addEventListener('click', () => sendMessage());
      messageInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          sendMessage();
        }
      });

      // API Key Modal Events
      apiKeyBtn.addEventListener('click', openApiKeyModal);
      cancelApiKeyModal.addEventListener('click', () => closeApiKeyModal());
      saveApiKey.addEventListener('click', () => saveApiKeyValue());

      // Functions
      function renderBots() {
        botList.innerHTML = '';
        bots.forEach(bot => {
          const botItem = document.createElement('div');
          botItem.classList.add('bot-item');
          if (bot.id === currentBotId) {
            botItem.classList.add('active');
          }

          botItem.innerHTML = `
                        <div class="bot-avatar">${bot.name.charAt(0)}</div>
                        <div class="bot-info">
                            <div class="bot-name">${bot.name}</div>
                            <div class="bot-description">${bot.persona.substring(0, 30)}${bot.persona.length > 30 ? '...' : ''}</div>
                        </div>
                    `;

          botItem.addEventListener('click', () => selectBot(bot.id));
          botItem.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            openEditBotModal(bot.id);
          });

          botList.appendChild(botItem);
        });
      }

      function openAddBotModal() {
        modalTitle.textContent = 'Add New Bot';
        botNameInput.value = '';
        botPersonaInput.value = '';
        botIdInput.value = '';
        botModal.style.display = 'flex';
      }

      function openEditBotModal(botId) {
        const bot = bots.find(b => b.id === botId);
        if (bot) {
          modalTitle.textContent = 'Edit Bot';
          botNameInput.value = bot.name;
          botPersonaInput.value = bot.persona;
          botIdInput.value = bot.id;
          botModal.style.display = 'flex';
        }
      }

      function closeModal() {
        botModal.style.display = 'none';
      }

      function saveBotData() {
        const name = botNameInput.value.trim();
        const persona = botPersonaInput.value.trim();

        if (!name) {
          alert('Please enter a bot name');
          return;
        }

        const botId = botIdInput.value || Date.now().toString();

        if (botIdInput.value) {
          // Edit existing bot
          const index = bots.findIndex(b => b.id === botId);
          if (index !== -1) {
            bots[index] = { ...bots[index], name, persona };
          }
        } else {
          // Add new bot
          bots.push({
            id: botId,
            name,
            persona
          });

          // Initialize chat history for this bot
          if (!chats[botId]) {
            chats[botId] = [];
          }
        }

        localStorage.setItem('bots', JSON.stringify(bots));
        localStorage.setItem('chats', JSON.stringify(chats));

        renderBots();
        closeModal();

        // If we don't have a current bot selected, select this one
        if (!currentBotId) {
          selectBot(botId);
        }
      }

      function selectBot(botId) {
        currentBotId = botId;
        localStorage.setItem('currentBotId', botId);

        const selectedBot = bots.find(b => b.id === botId);
        if (selectedBot) {
          currentBotName.textContent = selectedBot.name;
        }

        renderBots();
        loadCurrentChat();
      }

      function loadCurrentChat() {
        if (!currentBotId) {
          chatMessages.innerHTML = '<div class="message bot">Please select or create a bot to start chatting.</div>';
          return;
        }

        const currentChat = chats[currentBotId] || [];

        chatMessages.innerHTML = '';
        currentChat.forEach(message => {
          addMessageToUI(message.sender, message.text);
        });

        scrollToBottom();
      }

      // Add this function for auto TTS playback
      async function autoPlayTTS(messageDiv, text, loadingMsgId) {
        try {
          // Show subtle indication that TTS is processing
          const indicator = document.createElement('div');
          indicator.classList.add('tts-indicator');
          indicator.innerHTML = '🔊 <small>Processing audio...</small>';
          indicator.style.fontSize = '0.8em';
          indicator.style.opacity = '0.7';
          indicator.style.marginTop = '5px';
          messageDiv.appendChild(indicator);

          // Submit TTS request
          const response = await fetch('/api/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({ text })
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }

          const data = await response.json();
          const taskId = data.task_id;

          // Poll for TTS result
          let result = null;
          let attempts = 0;

          while (attempts < 30) { // Reduced timeout for auto-play
            attempts++;

            const statusResponse = await fetch(`/api/tts/${taskId}`, {
              headers: { 'X-API-KEY': getApiKey() }
            });

            if (!statusResponse.ok) {
              throw new Error(`API error: ${statusResponse.status}`);
            }

            const statusData = await statusResponse.json();

            if (statusData.status === 'completed' && statusData.audio_base64) {
              result = statusData.audio_base64;
              break;
            } else if (statusData.status === 'failed') {
              throw new Error('TTS processing failed');
            }

            // Wait before checking again
            await new Promise(resolve => setTimeout(resolve, 1500));
          }

          if (!result) {
            throw new Error('TTS processing timed out');
          }

          // Create and play audio element
          const audio = new Audio(`data:audio/mp3;base64,${result}`);
          audio.play();

          // Remove the indicator when audio starts playing
          indicator.remove();

          // Cache the result for future use
          const panelId = md5(text);
          localStorage.setItem(`tts-${panelId}`, result);

        } catch (error) {
          console.error('Auto TTS error:', error);
          // Remove indicator if there was an error
          const indicator = messageDiv.querySelector('.tts-indicator');
          if (indicator) indicator.remove();
        }
      }

      function sendMessage() {
        const message = messageInput.value.trim();

        if (!message || !currentBotId) return;

        addMessageToCache('user', message);
        // Add user message
        addMessage('user', message);

        // Clear input
        messageInput.value = '';

        // Get the current bot persona for context
        const bot = bots.find(b => b.id === currentBotId);

        // Prepare messages array for the API
        // Include bot persona as system message for context
        const currentChat = chats[currentBotId] || [];

        // Get previous messages (up to 10) for context
        const recentMessages = currentChat.slice(-10).map(msg => ({
          role: msg.sender === 'user' ? 'user' : 'assistant',
          content: msg.text
        }));

        // Add system message with bot persona
        const messages = [
          { role: 'system', content: `You are ${bot.name}. ${bot.persona}` },
          ...recentMessages,
        ];

        // Add loading indication with typing animation
        const loadingMsgId = Date.now().toString();
        addStreamingMessageToUI('bot', '', loadingMsgId);
        showTypingIndicator(loadingMsgId);

        // Call the streaming API
        streamChatResponse(messages, loadingMsgId);
      }

      async function streamChatResponse(messages, loadingMsgId) {
        try {
          const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({
              messages: messages.map(msg => ({ role: msg.role, content: msg.content })),
              model: 'doubao-1-5-pro-32k-250115',
              stream: true // Enable streaming
            })
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }

          const reader = response.body.getReader();
          const decoder = new TextDecoder();
          let fullResponse = '';
          let buffer = '';

          // Remove typing indicator
          removeTypingIndicator(loadingMsgId);

          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep incomplete line in buffer

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data.trim() === '[DONE]') continue;

                try {
                  const chunk = JSON.parse(data);
                  const content = chunk.choices?.[0]?.delta?.content || '';

                  if (content) {
                    fullResponse += content;
                    updateStreamingMessage(loadingMsgId, fullResponse);
                  }
                } catch (e) {
                  // Skip invalid JSON chunks
                  console.warn('Failed to parse chunk:', data);
                }
              }
            }
          }

          // Finalize the streaming message
          finalizeStreamingMessage(loadingMsgId, fullResponse);
          addMessageToCache('bot', fullResponse);

          // Auto TTS for bot response if enabled
          if (autoTTSEnabled && fullResponse.trim()) {
            const botMessageDiv = document.getElementById(loadingMsgId);
            if (botMessageDiv) {
              autoPlayTTS(botMessageDiv, fullResponse, null);
            }
          }

        } catch (error) {
          // Remove loading message and show error
          removeMessage(loadingMsgId);
          addMessage('bot', `Sorry, there was an error: ${error.message}`);
          console.error('Error during streaming API call:', error);
        }
      }

      // Clear chat history for the current bot
      function clearChatHistory() {
        if (!currentBotId) {
          alert("Please select a bot first.");
          return;
        }

        if (confirm("Are you sure you want to clear the chat history? This cannot be undone.")) {
          // Clear the chat history for current bot
          chats[currentBotId] = [];

          // Save to localStorage
          localStorage.setItem('chats', JSON.stringify(chats));

          // Reload the chat view
          loadCurrentChat();

          // Add a system message
          addMessageToUI('bot', 'Chat history has been cleared.');
        }
      }

      // Replace the addMessageToUI function with this enhanced version
      function addMessageToUI(sender, text, id = null) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);
        if (id) messageDiv.id = id;

        // Add timestamp
        const timestampDiv = document.createElement('div');
        timestampDiv.classList.add('message-timestamp');
        const now = new Date();
        timestampDiv.textContent = now.toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        // Add message content
        let contentDiv;
        if (sender === 'bot' && !id) { // Only render markdown for final bot messages
          contentDiv = document.createElement('div');
          contentDiv.classList.add('bot-content');
          contentDiv.innerHTML = marked.parse(text);

          // Apply syntax highlighting to code blocks
          contentDiv.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
          });

          messageDiv.appendChild(contentDiv);
        } else {
          // For user messages or loading messages, use plain text
          contentDiv = document.createElement('div');
          contentDiv.appendChild(document.createTextNode(text));
          messageDiv.appendChild(contentDiv);
        }

        // Only add toolbar for final messages (not loading messages)
        if (!id) {
          // Add toolbar
          const toolbarDiv = document.createElement('div');
          toolbarDiv.classList.add('message-toolbar');

          // TTS button
          const ttsBtn = document.createElement('button');
          ttsBtn.classList.add('toolbar-btn');
          ttsBtn.innerHTML = '🔊';
          ttsBtn.title = 'Text to Speech';
          ttsBtn.addEventListener('click', () => handleTTS(messageDiv, text));

          // Grammar correction button
          const grammarBtn = document.createElement('button');
          grammarBtn.classList.add('toolbar-btn');
          grammarBtn.innerHTML = '✏️';
          grammarBtn.title = 'Grammar Correction';
          grammarBtn.addEventListener('click', () => handleGrammarCorrection(messageDiv, text));

          // Translation button
          const translateBtn = document.createElement('button');
          translateBtn.classList.add('toolbar-btn');
          translateBtn.innerHTML = '🌐';
          translateBtn.title = 'Translate';
          translateBtn.addEventListener('click', () => handleTranslation(messageDiv, text));

          toolbarDiv.appendChild(ttsBtn);
          if (sender === 'user') {
            toolbarDiv.appendChild(grammarBtn);
          }
          toolbarDiv.appendChild(translateBtn);
          messageDiv.appendChild(toolbarDiv);

          // Add result panel (initially hidden)
          const resultPanel = document.createElement('div');
          resultPanel.classList.add('message-result-panel');
          resultPanel.id = `result-panel-${Date.now()}`;
          messageDiv.appendChild(resultPanel);
        }

        chatMessages.appendChild(messageDiv);
        scrollToBottom();
      }

      // Remove a message by ID (for handling loading states)
      function removeMessage(id) {
        const element = document.getElementById(id);
        if (element) element.remove();
      }

      // Helper functions for streaming messages
      function addStreamingMessageToUI(sender, text, id) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender, 'streaming');
        if (id) messageDiv.id = id;

        // Add timestamp
        const timestampDiv = document.createElement('div');
        timestampDiv.classList.add('message-timestamp');
        const now = new Date();
        timestampDiv.textContent = now.toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        // Add message content for streaming
        const contentDiv = document.createElement('div');
        contentDiv.classList.add('bot-content', 'streaming-content');
        contentDiv.textContent = text;
        messageDiv.appendChild(contentDiv);

        chatMessages.appendChild(messageDiv);
        scrollToBottom();
      }

      function updateStreamingMessage(messageId, fullText) {
        const messageDiv = document.getElementById(messageId);
        if (!messageDiv) return;

        const contentDiv = messageDiv.querySelector('.streaming-content');
        if (contentDiv) {
          // For streaming, we'll update the text content directly to avoid markdown parsing mid-stream
          contentDiv.textContent = fullText;
          scrollToBottom();
        }
      }

      function finalizeStreamingMessage(messageId, fullText) {
        const messageDiv = document.getElementById(messageId);
        if (!messageDiv) return;

        // Remove streaming class
        messageDiv.classList.remove('streaming');

        // Update content with markdown rendering
        const contentDiv = messageDiv.querySelector('.streaming-content');
        if (contentDiv) {
          contentDiv.classList.remove('streaming-content');
          contentDiv.innerHTML = marked.parse(fullText);

          // Apply syntax highlighting to code blocks
          contentDiv.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
          });
        }

        // Add toolbar for final message
        const toolbarDiv = document.createElement('div');
        toolbarDiv.classList.add('message-toolbar');

        // TTS button
        const ttsBtn = document.createElement('button');
        ttsBtn.classList.add('toolbar-btn');
        ttsBtn.innerHTML = '🔊';
        ttsBtn.title = 'Text to Speech';
        ttsBtn.addEventListener('click', () => handleTTS(messageDiv, fullText));

        // Translation button
        const translateBtn = document.createElement('button');
        translateBtn.classList.add('toolbar-btn');
        translateBtn.innerHTML = '🌐';
        translateBtn.title = 'Translate';
        translateBtn.addEventListener('click', () => handleTranslation(messageDiv, fullText));

        toolbarDiv.appendChild(ttsBtn);
        toolbarDiv.appendChild(translateBtn);
        messageDiv.appendChild(toolbarDiv);

        // Add result panel (initially hidden)
        const resultPanel = document.createElement('div');
        resultPanel.classList.add('message-result-panel');
        resultPanel.id = `result-panel-${Date.now()}`;
        messageDiv.appendChild(resultPanel);

        scrollToBottom();
      }

      function showTypingIndicator(messageId) {
        const messageDiv = document.getElementById(messageId);
        if (!messageDiv) return;

        const contentDiv = messageDiv.querySelector('.streaming-content');
        if (contentDiv) {
          contentDiv.innerHTML = '<div class="typing-dots"><span></span><span></span><span></span></div>';
        }
      }

      function removeTypingIndicator(messageId) {
        const messageDiv = document.getElementById(messageId);
        if (!messageDiv) return;

        const contentDiv = messageDiv.querySelector('.streaming-content');
        if (contentDiv) {
          contentDiv.textContent = '';
        }
      }

      // Clear chat history for the current bot
      function clearChatHistory() {
        if (!currentBotId) {
          alert("Please select a bot first.");
          return;
        }

        if (confirm("Are you sure you want to clear the chat history? This cannot be undone.")) {
          // Clear the chat history for current bot
          chats[currentBotId] = [];

          // Save to localStorage
          localStorage.setItem('chats', JSON.stringify(chats));

          // Reload the chat view
          loadCurrentChat();

          // Add a system message
          addMessageToUI('bot', 'Chat history has been cleared.');
        }
      }

      // Replace the addMessageToUI function with this enhanced version
      function addMessageToUI(sender, text, id = null) {
        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', sender);
        if (id) messageDiv.id = id;

        // Add timestamp
        const timestampDiv = document.createElement('div');
        timestampDiv.classList.add('message-timestamp');
        const now = new Date();
        timestampDiv.textContent = now.toLocaleTimeString();
        messageDiv.appendChild(timestampDiv);

        // Add message content
        let contentDiv;
        if (sender === 'bot' && !id) { // Only render markdown for final bot messages
          contentDiv = document.createElement('div');
          contentDiv.classList.add('bot-content');
          contentDiv.innerHTML = marked.parse(text);

          // Apply syntax highlighting to code blocks
          contentDiv.querySelectorAll('pre code').forEach((block) => {
            hljs.highlightElement(block);
          });

          messageDiv.appendChild(contentDiv);
        } else {
          // For user messages or loading messages, use plain text
          contentDiv = document.createElement('div');
          contentDiv.appendChild(document.createTextNode(text));
          messageDiv.appendChild(contentDiv);
        }

        // Only add toolbar for final messages (not loading messages)
        if (!id) {
          // Add toolbar
          const toolbarDiv = document.createElement('div');
          toolbarDiv.classList.add('message-toolbar');

          // TTS button
          const ttsBtn = document.createElement('button');
          ttsBtn.classList.add('toolbar-btn');
          ttsBtn.innerHTML = '🔊';
          ttsBtn.title = 'Text to Speech';
          ttsBtn.addEventListener('click', () => handleTTS(messageDiv, text));

          // Grammar correction button
          const grammarBtn = document.createElement('button');
          grammarBtn.classList.add('toolbar-btn');
          grammarBtn.innerHTML = '✏️';
          grammarBtn.title = 'Grammar Correction';
          grammarBtn.addEventListener('click', () => handleGrammarCorrection(messageDiv, text));

          // Translation button
          const translateBtn = document.createElement('button');
          translateBtn.classList.add('toolbar-btn');
          translateBtn.innerHTML = '🌐';
          translateBtn.title = 'Translate';
          translateBtn.addEventListener('click', () => handleTranslation(messageDiv, text));

          toolbarDiv.appendChild(ttsBtn);
          if (sender === 'user') {
            toolbarDiv.appendChild(grammarBtn);
          }
          toolbarDiv.appendChild(translateBtn);
          messageDiv.appendChild(toolbarDiv);

          // Add result panel (initially hidden)
          const resultPanel = document.createElement('div');
          resultPanel.classList.add('message-result-panel');
          resultPanel.id = `result-panel-${Date.now()}`;
          messageDiv.appendChild(resultPanel);
        }

        chatMessages.appendChild(messageDiv);
        scrollToBottom();
      }

      // Remove a message by ID (for handling loading states)
      function removeMessage(id) {
        const element = document.getElementById(id);
        if (element) element.remove();
      }

      // Function to get API key from storage or prompt user
      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');

        if (!apiKey) {
          apiKey = prompt("Please enter your API key:");
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          } else {
            throw new Error("API key is required");
          }
        }

        return apiKey;
      }

      function openApiKeyModal() {
        apiKeyInput.value = localStorage.getItem('apiKey') || '';
        apiKeyModal.style.display = 'flex';
      }

      function closeApiKeyModal() {
        apiKeyModal.style.display = 'none';
      }

      function saveApiKeyValue() {
        const apiKey = apiKeyInput.value.trim();
        if (apiKey) {
          localStorage.setItem('apiKey', apiKey);
          closeApiKeyModal();
        } else {
          alert('Please enter a valid API key');
        }
      }

      function addMessageToCache(sender, text) {
        // Add message to storage
        if (!chats[currentBotId]) {
          chats[currentBotId] = [];
        }

        chats[currentBotId].push({
          sender,
          text,
          timestamp: new Date().toISOString()
        });

        // Save to localStorage
        localStorage.setItem('chats', JSON.stringify(chats));
      }

      function addMessage(sender, text) {
        // Add message to UI
        addMessageToUI(sender, text);
        // Scroll to the bottom
        scrollToBottom();
      }

      // Scroll chat container to the bottom
      function scrollToBottom() {
        chatMessages.scrollTop = chatMessages.scrollHeight;
      }

      // Add event listener for improve persona button
      improvePersonaBtn.addEventListener('click', () => improvePersonaWithAI());

      // Function to improve persona using AI
      function improvePersonaWithAI() {
        const botName = botNameInput.value.trim();
        const currentPersona = botPersonaInput.value.trim();

        if (!botName) {
          alert('Please enter a bot name first');
          return;
        }

        // Show loading state
        improvePersonaBtn.disabled = true;
        improvePersonaBtn.textContent = 'Improving...';

        // Prepare the message for the AI
        const messages = [
          {
            role: 'system',
            content: 'You are an expert AI assistant that specializes in writing compelling and detailed personas for chatbots.'
          },
          {
            role: 'user',
            content: `
I have a chatbot named "${botName}" with the following persona description: "${currentPersona || 'No persona defined yet.'}". 
Please improve and expand this persona to make it more detailed, specific, and engaging. 
The improved persona should clearly define the bot's personality, tone, expertise, knowledge boundaries, and how it should interact with users. 
Provide only the improved persona text without any explanations or additional commentary. 
Example: 
# Name
${botName}
# Instructions
您是一个友好且知识渊博的助手，帮助用户解答疑问。您应该始终保持礼貌、简洁且富有信息性。
# Personality
您是一个开朗、耐心且随时准备提供帮助的助手。您具有良好的幽默感，并且能够根据用户的情绪调整语气。
# Skills
- 您可以提供广泛主题的信息，包括技术、科学和一般知识。
- 您可以协助解决问题并提供逐步指导。
# Limitations
- 您没有访问实时数据或个人用户信息的权限。
- 您无法执行提供信息和指导以外的操作。
            `
          }
        ];

        // Call the API
        fetch('/api/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({
            messages: messages,
          })
        })
          .then(response => {
            if (!response.ok) {
              throw new Error(`API error: ${response.status}`);
            }
            return response.json();
          })
          .then(data => {
            // Extract the improved persona from the response
            const improvedPersona = data.choices[0].message.content;

            // Update the persona textarea with the improved version
            botPersonaInput.value = improvedPersona;

            // Reset button state
            improvePersonaBtn.disabled = false;
            improvePersonaBtn.innerHTML = '<span class="toolbar-icon">✨</span> Improve Persona with AI';
          })
          .catch(error => {
            console.error('Error improving persona:', error);
            alert(`Failed to improve persona: ${error.message}`);

            // Reset button state
            improvePersonaBtn.disabled = false;
            improvePersonaBtn.innerHTML = '<span class="toolbar-icon">✨</span> Improve Persona with AI';
          });
      }

      function setupTextareaAutoResize() {
        messageInput.addEventListener('input', function () {
          // Reset height to auto to get the correct scrollHeight
          this.style.height = 'auto';

          // Set new height based on scrollHeight, with a max height
          const newHeight = Math.min(this.scrollHeight, 120);
          this.style.height = newHeight + 'px';

          // Add subtle animation
          this.style.transition = 'height 0.1s ease';
        });

        // Initial setup to handle any pre-filled content
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
      }

      // Call this function after DOM is loaded
      document.addEventListener('DOMContentLoaded', () => {
        // ... existing code ...
        setupTextareaAutoResize();
      });

      // Export chat function
      function exportChat() {
        if (!currentBotId || !chats[currentBotId] || chats[currentBotId].length === 0) {
          alert('No chat history to export.');
          return;
        }

        const bot = bots.find(b => b.id === currentBotId);
        if (!bot) return;

        const chatData = {
          botName: bot.name,
          botPersona: bot.persona,
          messages: chats[currentBotId],
          exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(chatData, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `chat-with-${bot.name.replace(/\s+/g, '-')}-${new Date().toISOString().slice(0, 10)}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
      }

      // Import chat function
      function importChat() {
        importFileInput.click();
      }

      importFileInput.addEventListener('change', (event) => {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const importedChats = JSON.parse(e.target.result);
            if (Array.isArray(importedChats)) {
              // Merge imported chats with existing chats
              chats = { ...chats, [currentBotId]: [...(chats[currentBotId] || []), ...importedChats] };

              // Save to localStorage
              localStorage.setItem('chats', JSON.stringify(chats));

              // Reload the chat view
              loadCurrentChat();

              alert('Chat imported successfully!');
            } else {
              alert('Invalid chat data format. Please ensure the file contains an array of chat messages.');
            }
          } catch (error) {
            alert('Error importing chat: ' + error.message);
          }
        };

        reader.readAsText(file);
      });

      // Add these lines after other event listeners
      document.getElementById('exportChatBtn').addEventListener('click', exportChat);
      document.getElementById('importChatBtn').addEventListener('click', () => document.getElementById('importFileInput').click());
      document.getElementById('importFileInput').addEventListener('change', importChat);

      function exportChat() {
        if (!currentBotId || !chats[currentBotId] || chats[currentBotId].length === 0) {
          alert('No chat history to export.');
          return;
        }

        const bot = bots.find(b => b.id === currentBotId);
        if (!bot) return;

        const chatData = {
          botName: bot.name,
          botPersona: bot.persona,
          messages: chats[currentBotId],
          exportDate: new Date().toISOString()
        };

        const dataStr = JSON.stringify(chatData, null, 2);
        const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);

        const exportFileDefaultName = `chat-with-${bot.name.replace(/\s+/g, '-')}-${new Date().toISOString().slice(0, 10)}.json`;

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();
      }

      function importChat(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function (e) {
          try {
            const importedData = JSON.parse(e.target.result);

            if (!importedData.messages || !Array.isArray(importedData.messages)) {
              throw new Error('Invalid chat format');
            }

            // Ask for confirmation
            if (confirm(`Import chat with ${importedData.messages.length} messages?`)) {
              // Create a new bot if needed or use existing
              let botId = currentBotId;

              if (!botId || confirm('Import as a new bot?')) {
                // Create new bot
                const newBot = {
                  id: Date.now().toString(),
                  name: importedData.botName || 'Imported Bot',
                  persona: importedData.botPersona || 'A bot with imported chat history.'
                };

                bots.push(newBot);
                localStorage.setItem('bots', JSON.stringify(bots));
                botId = newBot.id;
              }

              // Import the messages
              chats[botId] = importedData.messages;
              localStorage.setItem('chats', JSON.stringify(chats));

              // Select the bot and load chat
              selectBot(botId);

              alert('Chat imported successfully!');
            }
          } catch (error) {
            alert(`Error importing chat: ${error.message}`);
          }

          // Reset the file input
          event.target.value = '';
        };

        reader.readAsText(file);
      }

      function md5(string) {
        // Simple MD5 hash function implementation
        return string.split('').reduce((hash, char) => {
          hash = ((hash << 5) - hash) + char.charCodeAt(0);
          return hash & hash; // Convert to 32bit integer
        }, 0).toString(16);
      }

      // Add these functions after the addMessageToUI function

      // Handle TTS button click
      async function handleTTS(messageDiv, text) {
        if (text.trim().length === 0) {
          text = messageDiv.innerText;
        }
        const resultPanel = messageDiv.querySelector('.message-result-panel');
        // const panelId = resultPanel.id;

        // encode text to md5 to create a unique panel ID
        const panelId = md5(text); // Uncomment if you want to use md5 for unique IDs

        // Check if we already have a cached result
        const cachedResult = localStorage.getItem(`tts-${panelId}`);

        // Toggle panel visibility
        if (resultPanel.classList.contains('active') &&
          resultPanel.getAttribute('data-type') === 'tts') {
          resultPanel.classList.remove('active');
          return;
        }

        // Set panel type and make it visible
        resultPanel.setAttribute('data-type', 'tts');
        resultPanel.classList.add('active');

        // If we have a cached result, use it
        if (cachedResult) {
          resultPanel.innerHTML = `
            <div class="result-content">
              <audio controls autoplay>
                <source src="data:audio/mp3;base64,${cachedResult}" type="audio/mp3">
                Your browser does not support audio playback.
              </audio>
            </div>
          `;
          return;
        }

        // Show loading indicator
        resultPanel.innerHTML = `
          <div class="result-loading">
            <div class="typing-dots">
              <span></span><span></span><span></span>
            </div>
          </div>
        `;

        try {
          // Step 1: Submit TTS request
          const response = await fetch('/api/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({
              text: text,
              // voice: 'en-US-AriaNeural' // Default voice, could be made configurable
            })
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }

          const data = await response.json();
          const taskId = data.task_id;

          // Step 2: Poll for TTS result
          let result = null;
          let attempts = 0;

          while (attempts < 30) { // Timeout after 30 attempts
            attempts++;

            const statusResponse = await fetch(`/api/tts/${taskId}`, {
              headers: {
                'X-API-KEY': getApiKey()
              }
            });

            if (!statusResponse.ok) {
              throw new Error(`API error: ${statusResponse.status}`);
            }

            const statusData = await statusResponse.json();

            if (statusData.status === 'completed' && statusData.audio_base64) {
              result = statusData.audio_base64;
              break;
            } else if (statusData.status === 'failed') {
              throw new Error('TTS processing failed');
            }

            // Wait before checking again
            await new Promise(resolve => setTimeout(resolve, 2000));
          }

          if (!result) {
            throw new Error('TTS processing timed out');
          }

          // Cache the result
          localStorage.setItem(`tts-${panelId}`, result);

          // Display the audio player
          resultPanel.innerHTML = `
            <div class="result-content">
              <audio controls autoplay>
                <source src="data:audio/mp3;base64,${result}" type="audio/mp3">
                Your browser does not support audio playback.
              </audio>
            </div>
          `;

        } catch (error) {
          console.error('TTS error:', error);
          resultPanel.innerHTML = `
            <div class="result-error">
              Error: ${error.message}
            </div>
          `;
        }
      }

      // Handle Grammar Correction button click
      async function handleGrammarCorrection(messageDiv, text) {

        if (text.trim().length === 0) {
          text = messageDiv.innerText;
        }

        const resultPanel = messageDiv.querySelector('.message-result-panel');
        const panelId = md5(text + '-grammar'); // Create unique ID for this grammar check

        // Toggle panel visibility
        if (resultPanel.classList.contains('active') &&
          resultPanel.getAttribute('data-type') === 'grammar') {
          resultPanel.classList.remove('active');
          return;
        }

        // Set panel type and make it visible
        resultPanel.setAttribute('data-type', 'grammar');
        resultPanel.classList.add('active');

        // Check if we already have a cached result
        const cachedResult = localStorage.getItem(`grammar-${panelId}`);

        if (cachedResult) {
          const parsedResult = JSON.parse(cachedResult);
          displayGrammarResult(resultPanel, parsedResult);
          return;
        }

        // Show loading indicator
        resultPanel.innerHTML = `
          <div class="result-loading">
            <div class="typing-dots">
              <span></span><span></span><span></span>
            </div>
          </div>
        `;

        try {
          // Step 1: Submit grammar suggestion request
          const response = await fetch('/api/grammar_suggestion', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({
              text: text
            })
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }

          const data = await response.json();
          const taskId = data.task_id;

          // Step 2: Poll for grammar suggestion result
          let result = null;
          let attempts = 0;

          while (attempts < 30) { // Timeout after 30 attempts
            attempts++;

            const statusResponse = await fetch(`/api/grammar_suggestion/${taskId}`, {
              headers: {
                'X-API-KEY': getApiKey()
              }
            });

            if (!statusResponse.ok) {
              throw new Error(`API error: ${statusResponse.status}`);
            }

            const statusData = await statusResponse.json();

            if (statusData.status === 'completed' && statusData.suggestions) {
              result = statusData.suggestions;
              break;
            } else if (statusData.status === 'failed') {
              throw new Error('Grammar suggestion processing failed');
            }

            // Wait before checking again
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          if (!result) {
            throw new Error('Grammar suggestion processing timed out');
          }

          // Cache the result
          localStorage.setItem(`grammar-${panelId}`, JSON.stringify(result));

          // Display the grammar result
          displayGrammarResult(resultPanel, result);

        } catch (error) {
          console.error('Grammar error:', error);
          resultPanel.innerHTML = `
            <div class="result-error">
              Error: ${error.message}
            </div>
          `;
        }
      }

      // Display grammar results in the panel
      function displayGrammarResult(resultPanel, result) {
        // Extract suggestions from the result object
        const suggestions = result.suggestions || result;
        const errorCount = (suggestions.errors ? suggestions.errors.length : 0);

        let html = `
          <div class="result-content">
            <div class="grammar-header">
              <span class="grammar-badge">${errorCount}</span>
              <h4>Grammar Suggestions</h4>
            </div>
        `;

        if (suggestions.errors && suggestions.errors.length > 0) {
          html += `<div class="grammar-errors">`;
          html += `<h5>Errors:</h5>`;
          suggestions.errors.forEach(error => {
            html += `
              <div class="grammar-error">
                <div class="error-text">${error}</div>
              </div>
            `;
          });
          html += `</div>`;
        }

        if (suggestions.improvements && suggestions.improvements.length > 0) {
          html += `<div class="grammar-warnings">`;
          html += `<h5>Style Improvements:</h5>`;
          suggestions.improvements.forEach(improvement => {
            html += `
              <div class="grammar-warning">
                <div class="warning-text">${improvement}</div>
              </div>
            `;
          });
          html += `</div>`;
        }

        if (suggestions.suggested_text) {
          html += `
            <h5>Improved Text:</h5>
            <div class="grammar-improved">
              <p>${suggestions.suggested_text}</p>
            </div>
          `;
        }

        if (errorCount === 0 && !suggestions.suggested_text) {
          html += `<p>No grammar issues found!</p>`;
        }

        html += `</div>`;
        resultPanel.innerHTML = html;
      }

      // Handle Translation button click
      async function handleTranslation(messageDiv, text) {
        if (text.trim().length === 0) {
          text = messageDiv.innerText;
        }
        const resultPanel = messageDiv.querySelector('.message-result-panel');
        const panelId = md5(text + '-translate'); // Create unique ID for this translation

        // Toggle panel visibility
        if (resultPanel.classList.contains('active') &&
          resultPanel.getAttribute('data-type') === 'translation') {
          resultPanel.classList.remove('active');
          return;
        }

        // Set panel type and make it visible
        resultPanel.setAttribute('data-type', 'translation');
        resultPanel.classList.add('active');

        // Check if we already have a cached result
        const cachedResult = localStorage.getItem(`translate-${panelId}`);

        if (cachedResult) {
          const parsedResult = JSON.parse(cachedResult);
          displayTranslationResult(resultPanel, parsedResult);
          return;
        }

        // Show loading indicator
        resultPanel.innerHTML = `
          <div class="result-loading">
            <div class="typing-dots">
              <span></span><span></span><span></span>
            </div>
          </div>
        `;

        // collect before text from chat history
        const chatHistory = chats[currentBotId] || [];
        let beforeText = chatHistory.map(msg => {
          if (msg.sender !== 'user' && msg.sender !== 'bot') return '';
          return msg.sender === 'user' ? `User: ${msg.text || ""}` : `Bot: ${msg.text || ""}`;
        }).join('\n');
        if (beforeText.length > 1000) {
          beforeText = "..." + beforeText.slice(-1000); // Limit to last 1000 characters
        }

        try {
          // Step 1: Submit translation request
          const response = await fetch('/api/translate', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({
              text: text,
              before_text: beforeText
            })
          });

          if (!response.ok) {
            throw new Error(`API error: ${response.status}`);
          }

          const data = await response.json();
          const taskId = data.task_id;

          // Step 2: Poll for translation result
          let result = null;
          let attempts = 0;

          while (attempts < 30) { // Timeout after 30 attempts
            attempts++;

            const statusResponse = await fetch(`/api/translate/${taskId}`, {
              headers: {
                'X-API-KEY': getApiKey()
              }
            });

            if (!statusResponse.ok) {
              throw new Error(`API error: ${statusResponse.status}`);
            }

            const statusData = await statusResponse.json();

            if (statusData.status === 'completed' && statusData.translated_text) {
              result = { translated_text: statusData.translated_text };
              break;
            } else if (statusData.status === 'failed') {
              throw new Error('Translation processing failed');
            }

            // Wait before checking again
            await new Promise(resolve => setTimeout(resolve, 1000));
          }

          if (!result) {
            throw new Error('Translation processing timed out');
          }

          // Cache the result
          localStorage.setItem(`translate-${panelId}`, JSON.stringify(result));

          // Display the translation result
          displayTranslationResult(resultPanel, result);

        } catch (error) {
          console.error('Translation error:', error);
          resultPanel.innerHTML = `
            <div class="result-error">
              Error: ${error.message}
            </div>
          `;
        }
      }

      // Display translation results in the panel
      function displayTranslationResult(resultPanel, result) {
        resultPanel.innerHTML = `
          <div class="result-content">
            <div class="translation-header">
              <h4>Translation</h4>
            </div>
            <div class="translated-text">
              ${result.translated_text}
            </div>
          </div>
        `;
      }

      function toggleAutoTTS() {
        autoTTSEnabled = !autoTTSEnabled;
        localStorage.setItem('autoTTSEnabled', autoTTSEnabled);
        autoTTSToggle.innerHTML = autoTTSEnabled ? '🔊 Auto TTS: ON' : '🔈 Auto TTS: OFF';
      }

      function animateBotMessage(messageDiv, text) {
        console.log(`Animating bot message: ${text}`);
        const contentDiv = messageDiv.querySelector('.bot-content');
        if (!contentDiv) return;
        // Clear current content
        contentDiv.innerHTML = "";
        const words = text.split(" ");
        let index = 0;
        const interval = 300; // Delay (in milliseconds) between words
        console.log(`Animating message: ${words}`);

        const timer = setInterval(() => {
          if (index < words.length) {
            // Append each word with a space if needed
            contentDiv.innerHTML += (index > 0 ? " " : "") + words[index];
            index++;
          } else {
            clearInterval(timer);
          }
        }, interval);
      }
    });
  </script>
</body>

</html>