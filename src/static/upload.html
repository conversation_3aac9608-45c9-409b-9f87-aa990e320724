<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>File Upload Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    .upload-area {
      border: 2px dashed #007bff;
      border-radius: 10px;
      padding: 30px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 20px;
    }

    .upload-area:hover {
      background-color: #f0f8ff;
      border-color: #0056b3;
    }

    .upload-icon {
      font-size: 3em;
      color: #007bff;
      margin-bottom: 15px;
    }

    .record-btn {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background-color: #dc3545;
      color: white;
      border: none;
      font-size: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      transition: all 0.3s ease;
    }

    .record-btn.recording {
      animation: pulse 1.5s infinite;
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
      }

      70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
      }

      100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
      }
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    #preview-panel {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      border-left: 5px solid #28a745;
    }

    #audio-preview {
      max-width: 100%;
    }

    #image-preview {
      max-width: 100%;
      max-height: 300px;
      border-radius: 5px;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">File Upload Service</h1>
      <p class="lead text-muted">Upload files or record audio for processing</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Upload or Record</span>
      <p class="mb-4">Choose a file from your device or record audio directly using your microphone.</p>

      <ul class="nav nav-tabs mb-4" id="uploadTab" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload-pane"
            type="button" role="tab" aria-controls="upload-pane" aria-selected="true">Upload File</button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="record-tab" data-bs-toggle="tab" data-bs-target="#record-pane" type="button"
            role="tab" aria-controls="record-pane" aria-selected="false">Record Audio</button>
        </li>
      </ul>

      <div class="tab-content" id="uploadTabContent">
        <div class="tab-pane fade show active" id="upload-pane" role="tabpanel" aria-labelledby="upload-tab">
          <div id="upload-area" class="upload-area">
            <div class="upload-icon">
              <i class="bi bi-cloud-arrow-up"></i>
              <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor"
                class="bi bi-cloud-arrow-up" viewBox="0 0 16 16">
                <path fill-rule="evenodd"
                  d="M7.646 5.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1-.708.708L8.5 6.707V10.5a.5.5 0 0 1-1 0V6.707L6.354 7.854a.5.5 0 1 1-.708-.708l2-2z" />
                <path
                  d="M4.406 3.342A5.53 5.53 0 0 1 8 2c2.69 0 4.923 2 5.166 4.579C14.758 6.804 16 8.137 16 9.773 16 11.569 14.502 13 12.687 13H3.781C1.708 13 0 11.366 0 9.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383zm.653.757c-.757.653-1.153 1.44-1.153 2.056v.448l-.445.049C2.064 6.805 1 7.952 1 9.318 1 10.785 2.23 12 3.781 12h8.906C13.98 12 15 10.988 15 9.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 4.825 10.328 3 8 3a4.53 4.53 0 0 0-2.941 1.1z" />
              </svg>
            </div>
            <h4>Drop your file here</h4>
            <p class="text-muted">or click to browse</p>
            <input type="file" id="file-input" style="display: none;" accept="audio/*,image/*,video/*,application/pdf">
          </div>

          <div class="text-center mb-3">
            <small class="text-muted">Supported formats: Audio (MP3, WAV, OGG), Images (JPEG, PNG), Video (MP4),
              Documents (PDF)</small>
          </div>
        </div>

        <div class="tab-pane fade" id="record-pane" role="tabpanel" aria-labelledby="record-tab">
          <div class="row">
            <div class="col-md-6 offset-md-3 text-center">
              <p class="mb-3">Click the button below to start recording audio</p>
              <button id="record-button" class="record-btn">
                <i class="bi bi-mic-fill"></i>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                  class="bi bi-mic-fill" viewBox="0 0 16 16">
                  <path d="M5 3a3 3 0 0 1 6 0v5a3 3 0 0 1-6 0V3z" />
                  <path
                    d="M3.5 6.5A.5.5 0 0 1 4 7v1a4 4 0 0 0 8 0V7a.5.5 0 0 1 1 0v1a5 5 0 0 1-4.5 4.975V15h3a.5.5 0 0 1 0 1h-7a.5.5 0 0 1 0-1h3v-2.025A5 5 0 0 1 3 8V7a.5.5 0 0 1 .5-.5z" />
                </svg>
              </button>
              <p id="recording-status" class="mt-3">Not recording</p>
              <div id="recording-controls" style="display: none;">
                <div class="d-flex justify-content-center mt-3">
                  <button id="stop-button" class="btn btn-danger me-2">Stop</button>
                  <button id="play-button" class="btn btn-secondary" disabled>Play</button>
                </div>
                <audio id="audio-playback" controls style="width: 100%; margin-top: 20px; display: none;"></audio>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-3">
        <button id="upload-button" class="btn btn-success" disabled>Upload File</button>
        <button id="clear-button" class="btn btn-outline-secondary ms-2">Clear</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">File Preview</span>
      <p class="mb-4">Once uploaded, your file will be available for preview.</p>

      <div id="upload-status" class="d-none">
        <p>
          <span class="ms-3">Status: <span id="status-badge"
              class="badge rounded-pill status-badge bg-secondary">Uploading...</span></span>
        </p>
        <div class="progress mb-3" style="height: 10px;">
          <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%">
          </div>
        </div>
      </div>

      <div id="result-panel">
        <div id="preview-panel" class="text-center">
          <h5>File successfully uploaded!</h5>
          <p>File URL: <a id="file-url" href="#" target="_blank"></a></p>

          <div id="preview-container" class="mt-3">
            <!-- Preview content will be inserted here based on file type -->
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 File Upload Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const fileInput = document.getElementById('file-input');
      const uploadArea = document.getElementById('upload-area');
      const uploadButton = document.getElementById('upload-button');
      const clearButton = document.getElementById('clear-button');
      const errorMessage = document.getElementById('error-message');
      const uploadStatus = document.getElementById('upload-status');
      const statusBadge = document.getElementById('status-badge');
      const progressBar = document.getElementById('progress-bar');
      const resultPanel = document.getElementById('result-panel');
      const fileUrl = document.getElementById('file-url');
      const previewContainer = document.getElementById('preview-container');
      const recordButton = document.getElementById('record-button');
      const recordingStatus = document.getElementById('recording-status');
      const recordingControls = document.getElementById('recording-controls');
      const stopButton = document.getElementById('stop-button');
      const playButton = document.getElementById('play-button');
      const audioPlayback = document.getElementById('audio-playback');

      // Variables for file handling
      let selectedFile = null;
      let mediaRecorder = null;
      let audioChunks = [];
      let recordedBlob = null;

      // Event Listeners for Upload
      uploadArea.addEventListener('click', () => fileInput.click());
      fileInput.addEventListener('change', handleFileSelection);
      uploadButton.addEventListener('click', uploadFile);
      clearButton.addEventListener('click', clearSelection);

      uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#0056b3';
        uploadArea.style.backgroundColor = '#f0f8ff';
      });

      uploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#007bff';
        uploadArea.style.backgroundColor = '';
      });

      uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#007bff';
        uploadArea.style.backgroundColor = '';

        if (e.dataTransfer.files.length) {
          fileInput.files = e.dataTransfer.files;
          handleFileSelection();
        }
      });

      // Event Listeners for Recording
      recordButton.addEventListener('click', toggleRecording);
      stopButton.addEventListener('click', stopRecording);
      playButton.addEventListener('click', playRecording);

      function handleFileSelection() {
        if (fileInput.files.length === 0) return;

        selectedFile = fileInput.files[0];
        uploadArea.innerHTML = `<p><strong>Selected file:</strong> ${selectedFile.name}</p>
                               <p><strong>Type:</strong> ${selectedFile.type}</p>
                               <p><strong>Size:</strong> ${formatFileSize(selectedFile.size)}</p>`;
        uploadButton.disabled = false;
        hideError();
      }

      function formatFileSize(bytes) {
        if (bytes < 1024) return bytes + ' bytes';
        else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
        else return (bytes / 1048576).toFixed(2) + ' MB';
      }

      function uploadFile() {
        // Determine which file to upload: selected file or recorded audio
        const fileToUpload = recordedBlob ? new File([recordedBlob], "recorded-audio.wav", { type: recordedBlob.type }) : selectedFile;

        if (!fileToUpload) {
          showError('Please select a file or record audio first');
          return;
        }

        hideError();
        uploadStatus.classList.remove('d-none');
        resultPanel.style.display = 'none';
        uploadButton.disabled = true;
        uploadButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Uploading...';

        const formData = new FormData();
        formData.append('file', fileToUpload);

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/upload', true);
        xhr.setRequestHeader('X-API-KEY', getApiKey());

        xhr.upload.onprogress = (e) => {
          if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
          }
        };

        xhr.onload = function () {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            displayResult(response.url, fileToUpload);
          } else {
            showError('Upload failed: ' + xhr.statusText);
            uploadButton.disabled = false;
            uploadButton.textContent = 'Upload File';
          }
        };

        xhr.onerror = function () {
          showError('Network error occurred during upload');
          uploadButton.disabled = false;
          uploadButton.textContent = 'Upload File';
        };

        xhr.send(formData);
      }

      function displayResult(url, file) {
        fileUrl.href = url;
        fileUrl.textContent = url;

        // Clear previous preview content
        previewContainer.innerHTML = '';

        // Create preview based on file type
        if (file.type.startsWith('audio/')) {
          const audio = document.createElement('audio');
          audio.controls = true;
          audio.id = 'audio-preview';
          audio.className = 'w-100';
          audio.src = url;
          previewContainer.appendChild(audio);
        } else if (file.type.startsWith('image/')) {
          const image = document.createElement('img');
          image.id = 'image-preview';
          image.src = url;
          previewContainer.appendChild(image);
        } else if (file.type.startsWith('video/')) {
          const video = document.createElement('video');
          video.controls = true;
          video.className = 'w-100';
          video.src = url;
          previewContainer.appendChild(video);
        } else if (file.type === 'application/pdf') {
          const iframe = document.createElement('iframe');
          iframe.src = url;
          iframe.width = '100%';
          iframe.height = '500px';
          previewContainer.appendChild(iframe);
        } else {
          previewContainer.innerHTML = '<p>Preview not available for this file type</p>';
        }

        // Update status
        statusBadge.textContent = 'Uploaded';
        statusBadge.className = 'badge rounded-pill status-badge bg-success';
        progressBar.style.width = '100%';
        progressBar.classList.remove('progress-bar-animated');

        // Show the result panel
        resultPanel.style.display = 'block';
        uploadButton.disabled = false;
        uploadButton.textContent = 'Upload File';
      }

      function clearSelection() {
        selectedFile = null;
        recordedBlob = null;
        audioChunks = [];

        // Reset file upload
        fileInput.value = '';
        uploadArea.innerHTML = `
          <div class="upload-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" fill="currentColor" class="bi bi-cloud-arrow-up" viewBox="0 0 16 16">
              <path fill-rule="evenodd" d="M7.646 5.146a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1-.708.708L8.5 6.707V10.5a.5.5 0 0 1-1 0V6.707L6.354 7.854a.5.5 0 1 1-.708-.708l2-2z"/>
              <path d="M4.406 3.342A5.53 5.53 0 0 1 8 2c2.69 0 4.923 2 5.166 4.579C14.758 6.804 16 8.137 16 9.773 16 11.569 14.502 13 12.687 13H3.781C1.708 13 0 11.366 0 9.318c0-1.763 1.266-3.223 2.942-3.593.143-.863.698-1.723 1.464-2.383zm.653.757c-.757.653-1.153 1.44-1.153 2.056v.448l-.445.049C2.064 6.805 1 7.952 1 9.318 1 10.785 2.23 12 3.781 12h8.906C13.98 12 15 10.988 15 9.773c0-1.216-1.02-2.228-2.313-2.228h-.5v-.5C12.188 4.825 10.328 3 8 3a4.53 4.53 0 0 0-2.941 1.1z"/>
            </svg>
          </div>
          <h4>Drop your file here</h4>
          <p class="text-muted">or click to browse</p>
        `;

        // Reset recording
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
          mediaRecorder.stop();
        }
        recordButton.classList.remove('recording');
        recordingStatus.textContent = 'Not recording';
        recordingControls.style.display = 'none';
        audioPlayback.style.display = 'none';
        audioPlayback.src = '';
        playButton.disabled = true;

        // Reset upload status
        uploadButton.disabled = true;
        hideError();
        uploadStatus.classList.add('d-none');
        resultPanel.style.display = 'none';
      }

      async function toggleRecording() {
        if (!mediaRecorder || mediaRecorder.state === 'inactive') {
          try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            recordButton.classList.add('recording');
            recordingStatus.textContent = 'Recording...';
            recordingControls.style.display = 'block';
            audioPlayback.style.display = 'none';

            mediaRecorder = new MediaRecorder(stream);
            audioChunks = [];

            mediaRecorder.ondataavailable = (e) => {
              if (e.data.size > 0) {
                audioChunks.push(e.data);
              }
            };

            mediaRecorder.onstop = () => {
              recordedBlob = new Blob(audioChunks, { type: 'audio/wav' });
              const audioURL = URL.createObjectURL(recordedBlob);
              audioPlayback.src = audioURL;
              audioPlayback.style.display = 'block';
              playButton.disabled = false;
              uploadButton.disabled = false;

              // Stop all tracks on the stream
              stream.getTracks().forEach(track => track.stop());
            };

            mediaRecorder.start();
          } catch (err) {
            showError(`Microphone access error: ${err.message}`);
          }
        }
      }

      function stopRecording() {
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
          mediaRecorder.stop();
          recordButton.classList.remove('recording');
          recordingStatus.textContent = 'Recording finished';
        }
      }

      function playRecording() {
        if (audioPlayback.src) {
          audioPlayback.play();
        }
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }
        return apiKey || '';
      }
    });
  </script>
</body>

</html>