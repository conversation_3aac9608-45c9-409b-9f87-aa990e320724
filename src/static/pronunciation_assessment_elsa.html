<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Elsa Pronunciation Assessment Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #drop-area {
      border: 3px dashed #007bff;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      cursor: pointer;
      margin: 20px 0;
      transition: all 0.3s ease;
    }

    #drop-area:hover,
    #drop-area.highlight {
      background-color: #e8f4ff;
      border-color: #0056b3;
    }

    .file-info {
      margin: 20px 0;
      display: none;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    .transcript {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
      border-left: 5px solid #007bff;
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }

    /* Custom styling for Elsa pronunciation assessment */
    .recorded-controls {
      display: none;
    }

    .recorder-vis {
      height: 60px;
      background: #e9ecef;
      border-radius: 10px;
      margin: 15px 0;
      overflow: hidden;
      position: relative;
    }

    .recorder-vis canvas {
      width: 100%;
      height: 100%;
    }

    .tab-content {
      padding: 20px 0;
    }

    .score-card {
      text-align: center;
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 20px;
      background-color: #e9ecef;
    }

    .score-value {
      font-size: 2rem;
      font-weight: bold;
    }

    .score-label {
      font-size: 1rem;
      color: #6c757d;
    }

    .metrics-card {
      background-color: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
    }

    .cefr-distribution {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }

    .cefr-item {
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 0.9rem;
      background-color: #e9ecef;
    }

    .utterance-item {
      background-color: #f8f9fa;
      border-left: 4px solid #007bff;
      padding: 10px;
      margin-bottom: 10px;
      border-radius: 5px;
    }

    .timeline-item {
      display: inline-block;
      background-color: #e9ecef;
      padding: 5px 10px;
      margin: 5px;
      border-radius: 15px;
      font-size: 0.9rem;
    }

    /* Word-level quality styling */
    .word-item {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 5px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .word-item:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .word-perfect {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
      color: #155724;
    }

    .word-good {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
      color: #856404;
    }

    .word-poor {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;
    }

    .word-omitted {
      background-color: #f1f3f4;
      border: 1px solid #dadce0;
      color: #5f6368;
      text-decoration: line-through;
    }

    .phoneme-container {
      margin-top: 5px;
      font-size: 0.8rem;
    }

    .phoneme-item {
      display: inline-block;
      margin-right: 5px;
      padding: 2px 5px;
      border-radius: 3px;
      background-color: rgba(0, 0, 0, 0.1);
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      cursor: help;
    }

    .phoneme-item.word-perfect {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
    }

    .phoneme-item.word-good {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
    }

    .phoneme-item.word-poor {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
    }

    .word-details {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 5px;
      border-left: 3px solid #007bff;
      display: none;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Elsa Pronunciation Assessment</h1>
      <p class="lead text-muted">Advanced unscripted pronunciation assessment powered by Elsa AI</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Audio Input</span>
      <p class="mb-4">Record your voice or upload an audio file for unscripted assessment.</p>

      <ul class="nav nav-tabs" id="audioInputTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="record-tab" data-bs-toggle="tab" data-bs-target="#record" type="button"
            role="tab" aria-controls="record" aria-selected="true">Record Audio</button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="audio-upload-tab" data-bs-toggle="tab" data-bs-target="#audio-upload"
            type="button" role="tab" aria-controls="audio-upload" aria-selected="false">Upload Audio</button>
        </li>
      </ul>
      <div class="tab-content" id="audioInputTabContent">
        <div class="tab-pane fade show active" id="record" role="tabpanel" aria-labelledby="record-tab">
          <div class="text-center py-4">
            <button id="record-button" class="btn btn-outline-danger btn-lg">
              <i class="bi bi-mic"></i> Start Recording
            </button>
            <div class="mt-3">
              <span id="time-display" class="badge bg-secondary">00:00</span>
            </div>
          </div>
          <div class="recorder-vis">
            <canvas id="visualizer" width="600" height="60"></canvas>
          </div>
          <div id="recorded-controls" class="recorded-controls text-center py-3">
            <button id="play-button" class="btn btn-outline-primary me-2">
              <i class="bi bi-play"></i> Play
            </button>
            <button id="discard-button" class="btn btn-outline-secondary">
              <i class="bi bi-trash"></i> Discard
            </button>
          </div>
        </div>
        <div class="tab-pane fade" id="audio-upload" role="tabpanel" aria-labelledby="audio-upload-tab">
          <div id="audio-drop-area" class="drop-area">
            <p class="mb-3"><i class="bi bi-cloud-upload" style="font-size: 2rem;"></i></p>
            <p>Drag and drop an audio file here, or click to select</p>
            <button id="audio-select-button" class="btn btn-outline-primary mt-2">Choose Audio File</button>
            <input type="file" id="audio-file-input" accept="audio/*" style="display: none;">
          </div>
          <div id="audio-file-info" class="file-info">
            <div class="alert alert-info">
              <strong>Selected:</strong> <span id="audio-file-name"></span><br>
              <strong>Size:</strong> <span id="audio-file-size"></span>
            </div>
            <audio id="audio-preview" controls class="w-100"></audio>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <h6>Assessment Options</h6>
        <div class="form-check">
          <input class="form-check-input" type="checkbox" id="force-grammar-vocab" value="">
          <label class="form-check-label" for="force-grammar-vocab">
            Force Grammar & Vocabulary Analysis
          </label>
        </div>
      </div>

      <div class="mt-4">
        <button id="assess-button" class="btn btn-success btn-lg w-100">Assess Pronunciation with Elsa</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">Assessment Results</span>
      <p class="mb-4">View your comprehensive pronunciation assessment results from Elsa AI.</p>

      <div id="loading" class="text-center py-4 d-none">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Analyzing your pronunciation with Elsa AI...</p>
      </div>

      <div id="result-panel">
        <div class="row mb-4">
          <div class="col-md-6">
            <div class="score-card">
              <div class="score-value text-primary" id="overall-quality">-</div>
              <div class="score-label">Assessment Quality</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="score-card">
              <div class="score-value text-info" id="recording-quality">-</div>
              <div class="score-label">Recording Quality</div>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h5>Transcript</h5>
          <div id="transcript-display" class="transcript">Your spoken text will appear here...</div>
        </div>

        <div class="mb-4">
          <h5>Speaker Analysis</h5>
          <div id="speaker-analysis"></div>
        </div>

        <div class="mb-4">
          <h5>Timeline</h5>
          <div id="timeline-display"></div>
        </div>

        <div class="mb-4">
          <h5>Detailed Metrics</h5>
          <div id="detailed-metrics"></div>
        </div>

        <div class="mb-4">
          <h5>Word Breakdown</h5>
          <p class="text-muted small">Click on words to see detailed pronunciation feedback</p>
          <div id="word-breakdown"></div>
          <div id="word-details"></div>
        </div>

        <button id="retry-button" class="btn btn-primary">Try Again</button>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Elsa Pronunciation Assessment Service | Powered by Gusto AI & Elsa</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements - Audio
      const recordButton = document.getElementById('record-button');
      const timeDisplay = document.getElementById('time-display');
      const playButton = document.getElementById('play-button');
      const discardButton = document.getElementById('discard-button');
      const recordedControls = document.getElementById('recorded-controls');
      const visualizer = document.getElementById('visualizer');
      const audioFileInput = document.getElementById('audio-file-input');
      const audioSelectButton = document.getElementById('audio-select-button');
      const audioFileInfo = document.getElementById('audio-file-info');
      const audioFileName = document.getElementById('audio-file-name');
      const audioFileSize = document.getElementById('audio-file-size');
      const audioDropArea = document.getElementById('audio-drop-area');
      const audioPreview = document.getElementById('audio-preview');
      const forceGrammarVocab = document.getElementById('force-grammar-vocab');

      // DOM Elements - Assessment
      const assessButton = document.getElementById('assess-button');
      const errorMessage = document.getElementById('error-message');
      const loading = document.getElementById('loading');
      const resultPanel = document.getElementById('result-panel');
      const overallQuality = document.getElementById('overall-quality');
      const recordingQuality = document.getElementById('recording-quality');
      const transcriptDisplay = document.getElementById('transcript-display');
      const speakerAnalysis = document.getElementById('speaker-analysis');
      const timelineDisplay = document.getElementById('timeline-display');
      const detailedMetrics = document.getElementById('detailed-metrics');
      const wordBreakdown = document.getElementById('word-breakdown');
      const wordDetails = document.getElementById('word-details');
      const retryButton = document.getElementById('retry-button');

      // Variables
      let mediaRecorder;
      let audioChunks = [];
      let audioBlob = null;
      let audioFile = null;
      let recordingStartTime;
      let recordingTimer;
      let audioContext;
      let analyser;
      let visualizationCanvas;
      let canvasContext;
      let isRecording = false;
      let recordedAudioUrl = null;

      // Initialize
      init();

      // Event listeners
      recordButton.addEventListener('click', toggleRecording);
      playButton.addEventListener('click', playRecording);
      discardButton.addEventListener('click', discardRecording);

      audioSelectButton.addEventListener('click', () => audioFileInput.click());

      audioFileInput.addEventListener('change', (e) => {
        if (e.target.files.length) {
          handleAudioFile(e.target.files[0]);
        }
      });

      assessButton.addEventListener('click', assessPronunciation);
      retryButton.addEventListener('click', resetAssessment);

      // Drag and drop for audio files
      setupDragDrop(audioDropArea, audioFileInput, handleAudioFile);

      // Functions
      function init() {
        setupAudioTabEvents();
        setupVisualizer();
      }

      function setupAudioTabEvents() {
        const recordTrigger = document.getElementById('record-tab');
        const uploadTrigger = document.getElementById('audio-upload-tab');

        recordTrigger.addEventListener('click', () => {
          hideError();
        });

        uploadTrigger.addEventListener('click', () => {
          hideError();
        });
      }

      function setupVisualizer() {
        visualizationCanvas = visualizer;
        canvasContext = visualizationCanvas.getContext('2d');

        // Set canvas dimensions to match its display size
        const rect = visualizer.getBoundingClientRect();
        visualizationCanvas.width = rect.width;
        visualizationCanvas.height = rect.height;

        // Draw initial empty state
        canvasContext.fillStyle = '#e9ecef';
        canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
      }

      function setupDragDrop(dropArea, fileInput, handleFileFunc) {
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
          dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
          e.preventDefault();
          e.stopPropagation();
        }

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
          dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
          dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
          dropArea.classList.add('highlight');
        }

        function unhighlight() {
          dropArea.classList.remove('highlight');
        }

        // Handle dropped files
        dropArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
          const dt = e.dataTransfer;
          const files = dt.files;
          if (files.length > 0) {
            handleFileFunc(files[0]);
          }
        }

        // Handle click to select (but not if clicking the button)
        dropArea.addEventListener('click', (e) => {
          // Don't trigger file input if clicking the select button
          if (e.target.id === 'audio-select-button' || e.target.closest('#audio-select-button')) {
            return;
          }
          fileInput.click();
        });
      }

      function handleAudioFile(file) {
        if (!file.type.startsWith('audio/')) {
          showError('Please select a valid audio file.');
          return;
        }

        audioFile = file;
        audioFileName.textContent = file.name;
        audioFileSize.textContent = formatFileSize(file.size);
        audioFileInfo.style.display = 'block';

        // Create preview
        const url = URL.createObjectURL(file);
        audioPreview.src = url;

        hideError();
      }

      async function toggleRecording() {
        if (isRecording) {
          stopRecording();
        } else {
          await startRecording();
        }
      }

      async function startRecording() {
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

          mediaRecorder = new MediaRecorder(stream, {
            mimeType: 'audio/webm'
          });

          audioChunks = [];

          mediaRecorder.ondataavailable = event => {
            audioChunks.push(event.data);
          };

          mediaRecorder.onstop = () => {
            audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
            recordedAudioUrl = URL.createObjectURL(audioBlob);
            recordedControls.style.display = 'block';
            stopMediaTracks();
          };

          // Setup audio visualization
          audioContext = new (window.AudioContext || window.webkitAudioContext)();
          analyser = audioContext.createAnalyser();
          const source = audioContext.createMediaStreamSource(stream);
          source.connect(analyser);

          analyser.fftSize = 256;
          const bufferLength = analyser.frequencyBinCount;
          const dataArray = new Uint8Array(bufferLength);

          function visualize() {
            if (!isRecording) return;

            requestAnimationFrame(visualize);

            analyser.getByteFrequencyData(dataArray);

            canvasContext.fillStyle = '#e9ecef';
            canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);

            const barWidth = (visualizationCanvas.width / bufferLength) * 2.5;
            let barHeight;
            let x = 0;

            for (let i = 0; i < bufferLength; i++) {
              barHeight = (dataArray[i] / 255) * visualizationCanvas.height;

              canvasContext.fillStyle = `rgb(${barHeight + 100}, 50, 50)`;
              canvasContext.fillRect(x, visualizationCanvas.height - barHeight, barWidth, barHeight);

              x += barWidth + 1;
            }
          }

          mediaRecorder.start();
          isRecording = true;
          recordButton.textContent = 'Stop Recording';
          recordButton.classList.remove('btn-outline-danger');
          recordButton.classList.add('btn-danger');

          recordingStartTime = Date.now();
          recordingTimer = setInterval(updateTimer, 100);

          visualize();
          hideError();

        } catch (error) {
          showError('Unable to access microphone. Please check permissions.');
        }
      }

      function stopRecording() {
        if (mediaRecorder && isRecording) {
          mediaRecorder.stop();
          isRecording = false;

          recordButton.textContent = 'Start Recording';
          recordButton.classList.remove('btn-danger');
          recordButton.classList.add('btn-outline-danger');

          clearInterval(recordingTimer);

          // Clear visualization
          canvasContext.fillStyle = '#e9ecef';
          canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
        }
      }

      function updateTimer() {
        if (recordingStartTime) {
          const elapsed = (Date.now() - recordingStartTime) / 1000;
          const minutes = Math.floor(elapsed / 60);
          const seconds = Math.floor(elapsed % 60);
          timeDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

          // Auto-stop at 30 seconds
          if (elapsed >= 30) {
            stopRecording();
            showError('Recording automatically stopped after 30 seconds.');
          }
        }
      }

      function playRecording() {
        if (recordedAudioUrl) {
          const audio = new Audio(recordedAudioUrl);
          audio.play();
        }
      }

      function discardRecording() {
        audioBlob = null;
        recordedAudioUrl = null;
        recordedControls.style.display = 'none';
        timeDisplay.textContent = '00:00';

        // Clear visualization
        canvasContext.fillStyle = '#e9ecef';
        canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
      }

      function stopMediaTracks() {
        if (mediaRecorder && mediaRecorder.stream) {
          mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }
      }

      async function assessPronunciation() {
        if (!audioBlob && !audioFile) {
          showError('Please record audio or upload an audio file.');
          return;
        }

        hideError();
        loading.classList.remove('d-none');
        resultPanel.style.display = 'none';

        const formData = new FormData();

        if (audioBlob) {
          const audioFile = new File([audioBlob], 'recording.webm', { type: 'audio/webm' });
          formData.append('audio_file', audioFile);
        } else if (audioFile) {
          formData.append('audio_file', audioFile);
        }

        formData.append('return_json', 'true');
        formData.append('sync', 'true');
        formData.append('force_grammar_vocab', forceGrammarVocab.checked ? 'true' : 'false');

        try {
          const response = await fetch('/api/pronunciation_assessment/elsa', {
            method: 'POST',
            headers: {
              'X-API-KEY': getApiKey()
            },
            body: formData
          });

          const data = await response.json();

          if (!response.ok) {
            throw new Error(data.error || 'Assessment failed');
          }

          displayResults(data);

        } catch (error) {
          showError(`Assessment failed: ${error.message}`);
        } finally {
          loading.classList.add('d-none');
        }
      }

      function displayResults(data) {
        // Display basic info
        overallQuality.textContent = data.assessment_quality || 'N/A';
        recordingQuality.textContent = data.recording_quality || 'N/A';
        transcriptDisplay.textContent = data.transcript || 'No transcript available';

        // Display speaker analysis
        if (data.speakers && data.speakers.length > 0) {
          let speakerHtml = '';
          data.speakers.forEach((speaker, index) => {
            speakerHtml += `
              <div class="metrics-card">
                <h6>Speaker ${speaker.speaker_id + 1}</h6>
                <p><strong>Total Time:</strong> ${speaker.total_time.toFixed(2)} seconds</p>
                
                <div class="row">
                  <div class="col-md-6">
                    <h6>CEFR Levels</h6>
                    <ul class="list-unstyled">
                      <li><strong>Overall:</strong> ${speaker.metrics.general_scores.cefr.overall_cefr || 'N/A'}</li>
                      <li><strong>Pronunciation:</strong> ${speaker.metrics.general_scores.cefr.pronunciation_cefr || 'N/A'}</li>
                      <li><strong>Fluency:</strong> ${speaker.metrics.general_scores.cefr.fluency_cefr || 'N/A'}</li>
                      <li><strong>Vocabulary:</strong> ${speaker.metrics.general_scores.cefr.vocabulary_cefr || 'N/A'}</li>
                    </ul>
                  </div>
                  <div class="col-md-6">
                    <h6>Vocabulary Analysis</h6>
                    <p><strong>Total Words:</strong> ${speaker.metrics.other_metrics.vocabulary.total_words_count}</p>
                    <p><strong>Unique Words:</strong> ${speaker.metrics.other_metrics.vocabulary.unique_words_count}</p>
                    <p><strong>Uncommon Words:</strong> ${speaker.metrics.other_metrics.vocabulary.uncommon_words_count}</p>
                  </div>
                </div>

                <h6>CEFR Distribution</h6>
                <div class="cefr-distribution">
                  ${speaker.metrics.other_metrics.vocabulary.cefr_distribution.map(dist =>
              `<span class="cefr-item">${dist.cefr_level}: ${dist.percentage.toFixed(1)}%</span>`
            ).join('')}
                </div>

                <h6>Utterances</h6>
                ${speaker.utterances.map(utterance => `
                  <div class="utterance-item">
                    <strong>Text:</strong> ${utterance.text}<br>
                    <small><strong>Time:</strong> ${utterance.start_time.toFixed(2)}s - ${utterance.end_time.toFixed(2)}s</small>
                  </div>
                `).join('')}
              </div>
            `;
          });
          speakerAnalysis.innerHTML = speakerHtml;
        } else {
          speakerAnalysis.innerHTML = '<p>No speaker analysis available.</p>';
        }

        // Display timeline
        if (data.timeline && data.timeline.length > 0) {
          let timelineHtml = '';
          data.timeline.forEach(item => {
            timelineHtml += `
              <span class="timeline-item">
                Speaker ${item.speaker_id + 1} | ${item.start_time.toFixed(2)}s - ${item.end_time.toFixed(2)}s
              </span>
            `;
          });
          timelineDisplay.innerHTML = timelineHtml;
        } else {
          timelineDisplay.innerHTML = '<p>No timeline data available.</p>';
        }

        // Display detailed metrics
        let metricsHtml = `
          <div class="metrics-card">
            <h6>Assessment Information</h6>
            <p><strong>API Version:</strong> ${data.api_version}</p>
            <p><strong>API Plan:</strong> ${data.api_plan}</p>
            <p><strong>Total Time:</strong> ${data.total_time.toFixed(2)} seconds</p>
            <p><strong>Success:</strong> ${data.success ? 'Yes' : 'No'}</p>
          </div>
        `;
        detailedMetrics.innerHTML = metricsHtml;

        // Display word breakdown if available
        displayWordBreakdown(data);

        resultPanel.style.display = 'block';
      } function displayWordBreakdown(data) {
        if (!data.speakers || data.speakers.length === 0) {
          wordBreakdown.innerHTML = '<p>No word breakdown available.</p>';
          return;
        }

        let allWords = [];

        // Extract actual word-level data from Elsa API response
        data.speakers.forEach(speaker => {
          speaker.utterances.forEach(utterance => {
            if (utterance.result && utterance.result.words) {
              utterance.result.words.forEach(word => {
                allWords.push({
                  text: word.text,
                  original: word.text,
                  startTime: word.start_time,
                  endTime: word.end_time,
                  quality: getElsaWordQuality(word.decision),
                  score: word.nativeness_score,
                  ipa: word.ipa,
                  phonemes: word.phonemes || [],
                  decision: word.decision
                });
              });
            } else {
              // Fallback: create basic word data from utterance text
              const words = utterance.text.split(/\s+/).filter(word => word.length > 0);
              words.forEach((word, index) => {
                allWords.push({
                  text: word.replace(/[^\w]/g, ''), // Remove punctuation
                  original: word,
                  startTime: utterance.start_time + (index * (utterance.end_time - utterance.start_time) / words.length),
                  endTime: utterance.start_time + ((index + 1) * (utterance.end_time - utterance.start_time) / words.length),
                  quality: 'good', // Default quality
                  score: 75, // Default score
                  ipa: '',
                  phonemes: [],
                  decision: 'unknown'
                });
              });
            }
          });
        });

        if (allWords.length === 0) {
          wordBreakdown.innerHTML = '<p>No words to analyze.</p>';
          return;
        }

        // Create word breakdown HTML
        let wordHtml = '';
        allWords.forEach((word, index) => {
          const qualityClass = getElsaQualityClass(word.quality);
          wordHtml += `
            <span class="word-item ${qualityClass}" 
                  data-word-index="${index}"
                  data-word-text="${word.text}"
                  data-word-score="${word.score}"
                  data-start-time="${word.startTime.toFixed(2)}"
                  data-end-time="${word.endTime.toFixed(2)}"
                  data-word-ipa="${word.ipa || ''}"
                  data-word-decision="${word.decision}"
                  data-phonemes='${JSON.stringify(word.phonemes)}'
                  onclick="showWordDetails(this)">
              ${word.original}
            </span>
          `;
        });

        wordBreakdown.innerHTML = wordHtml;
      }

      function getElsaWordQuality(decision) {
        // Map Elsa API decision to quality levels
        switch (decision?.toLowerCase()) {
          case 'correct':
            return 'perfect';
          case 'warning':
            return 'good';
          case 'incorrect':
            return 'poor';
          default:
            return 'good';
        }
      }

      function getElsaQualityClass(quality) {
        switch (quality) {
          case 'perfect':
            return 'word-perfect';
          case 'good':
            return 'word-good';
          case 'poor':
            return 'word-poor';
          case 'omitted':
            return 'word-omitted';
          default:
            return 'word-good';
        }
      }



      // Make showWordDetails global so it can be called from onclick
      window.showWordDetails = function (wordElement) {
        const wordText = wordElement.getAttribute('data-word-text');
        const wordScore = wordElement.getAttribute('data-word-score');
        const startTime = wordElement.getAttribute('data-start-time');
        const endTime = wordElement.getAttribute('data-end-time');
        const wordIpa = wordElement.getAttribute('data-word-ipa');
        const wordDecision = wordElement.getAttribute('data-word-decision');
        const phonemesJson = wordElement.getAttribute('data-phonemes');

        // Remove previous details
        const existingDetails = document.querySelector('.word-details');
        if (existingDetails) {
          existingDetails.remove();
        }

        let phonemes = [];
        try {
          phonemes = JSON.parse(phonemesJson || '[]');
        } catch (e) {
          console.warn('Could not parse phonemes data:', e);
        }

        // Create phoneme breakdown
        let phonemeHtml = '';
        if (phonemes.length > 0) {
          phonemeHtml = '<div class="phoneme-container">';
          phonemes.forEach(phoneme => {
            const phonemeClass = getPhonemeClass(phoneme.decision);
            const errorInfo = phoneme.phoneme_error && phoneme.phoneme_error !== 'normal'
              ? ` (${phoneme.phoneme_error})`
              : '';
            phonemeHtml += `
              <span class="phoneme-item ${phonemeClass}" 
                    title="Score: ${phoneme.nativeness_score}/100 - ${phoneme.decision}${errorInfo}">
                ${phoneme.ipa || phoneme.text}
              </span>
            `;
          });
          phonemeHtml += '</div>';
        }

        // Create detailed view
        const detailsHtml = `
          <div class="word-details">
            <h6>Word: "${wordText}"</h6>
            <p><strong>Pronunciation Score:</strong> ${Math.round(wordScore)}/100</p>
            <p><strong>Decision:</strong> <span class="badge ${getDecisionBadgeClass(wordDecision)}">${wordDecision}</span></p>
            <p><strong>IPA:</strong> ${wordIpa || 'N/A'}</p>
            <p><strong>Timing:</strong> ${startTime}s - ${endTime}s</p>
            ${phonemeHtml ? '<p><strong>Phoneme Breakdown:</strong></p>' + phonemeHtml : ''}
            <p class="text-muted small"><em>Hover over phonemes to see individual scores and errors.</em></p>
          </div>
        `;

        // Insert after the word breakdown
        const wordBreakdown = document.getElementById('word-breakdown');
        wordBreakdown.insertAdjacentHTML('afterend', detailsHtml);
      };

      function getPhonemeClass(decision) {
        switch (decision?.toLowerCase()) {
          case 'correct':
            return 'word-perfect';
          case 'warning':
            return 'word-good';
          case 'incorrect':
            return 'word-poor';
          default:
            return 'word-good';
        }
      }

      function getDecisionBadgeClass(decision) {
        switch (decision?.toLowerCase()) {
          case 'correct':
            return 'bg-success';
          case 'warning':
            return 'bg-warning';
          case 'incorrect':
            return 'bg-danger';
          default:
            return 'bg-secondary';
        }
      }

      function getQualityDescription(className) {
        if (className.includes('word-perfect')) {
          return 'Excellent pronunciation - very clear and accurate';
        } else if (className.includes('word-good')) {
          return 'Good pronunciation - mostly accurate with minor issues';
        } else if (className.includes('word-poor')) {
          return 'Needs improvement - pronunciation could be clearer';
        } else if (className.includes('word-omitted')) {
          return 'Word was omitted or not clearly pronounced';
        }
        return 'Unknown quality';
      }

      function resetAssessment() {
        resultPanel.style.display = 'none';
        discardRecording();
        audioFile = null;
        audioFileInfo.style.display = 'none';

        // Clear word breakdown and details
        wordBreakdown.innerHTML = '';
        const existingDetails = document.querySelector('.word-details');
        if (existingDetails) {
          existingDetails.remove();
        }

        hideError();
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      function getApiKey() {
        // Check localStorage first
        let apiKey = localStorage.getItem('apiKey');

        // If not found, prompt the user
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }

        return apiKey || '';
      }
    });
  </script>
</body>

</html>