<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Text-to-Speech Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #text-input {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 15px;
      width: 100%;
      min-height: 150px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
    }

    #text-input:focus {
      outline: none;
      border-color: #0056b3;
      box-shadow: 0 0 5px rgba(0, 86, 179, 0.3);
    }

    #voice-select {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 10px;
      width: 100%;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    #speed-input {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 10px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    #speed-slider {
      margin-right: 15px;
      flex: 1;
    }

    .input-group {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    #audio-panel {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      border-left: 5px solid #28a745;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Text-to-Speech Service</h1>
      <p class="lead text-muted">Convert your text into speech with AI-powered TTS technology</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Enter Your Text</span>
      <p class="mb-4">Type or paste your text below and choose a voice to generate speech.</p>

      <textarea id="text-input" placeholder="Enter your text here..."></textarea>
      <div class="mt-3">
        <label for="voice-select" class="form-label">Select Voice</label>
        <select id="voice-select" class="form-select">
          <option value="en-US_female">English (US) - Female</option>
          <option value="en-US_male">English (US) - Male</option>
          <!-- Add more voice options as needed -->
        </select>
      </div>

      <div class="mt-3">
        <label for="speed-input" class="form-label">Speech Speed</label>
        <div class="input-group">
          <input type="range" class="form-range" id="speed-slider" min="0.5" max="2.0" step="0.1" value="1.0">
          <input type="number" class="form-control" id="speed-input" min="0.5" max="2.0" step="0.1" value="1.0"
            style="max-width: 100px;">
        </div>
        <small class="form-text text-muted">Speed range: 0.5x to 2.0x (1.0 = normal speed)</small>
      </div>

      <div class="mt-3 form-check">
        <input type="checkbox" class="form-check-input" id="stream-switch">
        <label class="form-check-label" for="stream-switch">Enable Streaming TTS</label>
      </div>

      <div class="mt-3">
        <button id="submit-button" class="btn btn-success">Generate Speech</button>
        <button id="clear-button" class="btn btn-outline-secondary ms-2">Clear</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">Listen to Audio</span>
      <p class="mb-4">Once processing is complete, your generated speech will be available below.</p>

      <div id="task-status" class="d-none">
        <p>
          Task ID: <span id="task-id" class="fw-bold"></span>
          <span class="ms-3">Status: <span id="status-badge"
              class="badge rounded-pill status-badge bg-secondary">Unknown</span></span>
        </p>
        <div class="progress mb-3" style="height: 10px;">
          <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%">
          </div>
        </div>
      </div>

      <div id="result-panel">
        <div id="audio-panel" class="text-center">
          <audio id="audio-player" controls style="width: 100%;"></audio>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Text-to-Speech Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const textInput = document.getElementById('text-input');
      const voiceSelect = document.getElementById('voice-select');
      const speedSlider = document.getElementById('speed-slider');
      const speedInput = document.getElementById('speed-input');
      const submitButton = document.getElementById('submit-button');
      const clearButton = document.getElementById('clear-button');
      const errorMessage = document.getElementById('error-message');
      const taskStatus = document.getElementById('task-status');
      const taskIdElement = document.getElementById('task-id');
      const statusBadge = document.getElementById('status-badge');
      const progressBar = document.getElementById('progress-bar');
      const resultPanel = document.getElementById('result-panel');
      const audioPlayer = document.getElementById('audio-player');
      const streamSwitch = document.getElementById('stream-switch');

      let taskId = null;
      let statusCheckInterval = null;

      // Event Listeners
      submitButton.addEventListener('click', submitText);
      clearButton.addEventListener('click', clearText);

      // Sync speed slider and input
      speedSlider.addEventListener('input', () => {
        speedInput.value = speedSlider.value;
      });

      speedInput.addEventListener('input', () => {
        speedSlider.value = speedInput.value;
      });

      // Function to fetch available voices from API and populate the select element
      function fetchVoices() {
        fetch('/api/tts_voices', {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to fetch voices');
            }
            return response.json();
          })
          .then(data => {
            // Clear existing options
            voiceSelect.innerHTML = '';
            // Populate new options with voice_type as value and the display combining name and language
            data.voices.forEach(voice => {
              const option = document.createElement('option');
              option.value = voice.voice_type;
              option.textContent = `${voice.name} (${voice.language})`;
              voiceSelect.appendChild(option);
            });
          })
          .catch(error => {
            console.error('Error fetching voices:', error);
          });
      }

      // Initial call to fetch voices on page load
      fetchVoices();

      function submitText() {
        const text = textInput.value.trim();
        const voice = voiceSelect.value;
        const speed = parseFloat(speedInput.value);
        if (!text) {
          showError('Please enter some text for TTS processing');
          return;
        }
        hideError();
        resultPanel.style.display = 'none';

        // If streaming is enabled, call the streaming TTS endpoint
        if (streamSwitch.checked) {
          submitButton.disabled = true;
          submitButton.innerHTML =
            '<span class="spinner-border spinner-border-sm me-2"></span>Processing Stream...';

          fetch('/api/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({ text: text, voice: voice, speed: speed, stream: true })
          })
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok');
              }
              // Use MediaSource to stream and play audio as chunks arrive.
              const mediaSource = new MediaSource();
              audioPlayer.src = URL.createObjectURL(mediaSource);
              mediaSource.addEventListener('sourceopen', () => {
                const sourceBuffer = mediaSource.addSourceBuffer('audio/mpeg');
                // Attempt to auto play once the media source is ready.
                audioPlayer.play().catch(err => console.error('Auto-play failed:', err));
                const reader = response.body.getReader();

                function push() {
                  reader
                    .read()
                    .then(({ done, value }) => {
                      if (done) {
                        mediaSource.endOfStream();
                        resultPanel.style.display = 'block';
                        submitButton.disabled = false;
                        submitButton.textContent = 'Generate Speech';
                        return;
                      }
                      sourceBuffer.appendBuffer(value);
                      push();
                    })
                    .catch(error => {
                      console.error('Error reading stream:', error);
                      mediaSource.endOfStream('network');
                      submitButton.disabled = false;
                      submitButton.textContent = 'Generate Speech';
                    });
                }
                push();
              });
            })
            .catch(error => {
              showError('Streaming TTS failed: ' + error.message);
              submitButton.disabled = false;
              submitButton.textContent = 'Generate Speech';
            });
        } else {
          // Existing non-streaming logic using background task
          submitButton.disabled = true;
          submitButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
          if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
          }
          fetch('/api/tts', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': getApiKey()
            },
            body: JSON.stringify({ text: text, voice: voice, speed: speed, stream: false })
          })
            .then(response => {
              if (!response.ok) {
                throw new Error('Network response was not ok');
              }
              return response.json();
            })
            .then(data => {
              taskId = data.task_id;
              taskIdElement.textContent = taskId;
              taskStatus.classList.remove('d-none');
              updateStatus(data.status);
              statusCheckInterval = setInterval(checkStatus, 500);
            })
            .catch(error => {
              showError('Submission failed: ' + error.message);
              submitButton.disabled = false;
              submitButton.textContent = 'Generate Speech';
            });
        }
      }

      function checkStatus() {
        fetch(`/api/tts/${taskId}`, {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to get task status');
            }
            return response.json();
          })
          .then(data => {
            updateStatus(data.status);
            if (data.status === 'completed') {
              clearInterval(statusCheckInterval);
              if (data.audio_url) {
                // Set the audio source using the URL
                audioPlayer.src = data.audio_url;
                resultPanel.style.display = 'block';
              } else {
                showError('No audio received.');
              }
            } else if (data.status === 'failed') {
              clearInterval(statusCheckInterval);
              showError('TTS processing failed');
            }
          })
          .catch(error => {
            showError('Status check failed: ' + error.message);
          });
      }

      function updateStatus(status) {
        statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
        statusBadge.className = 'badge rounded-pill status-badge';

        if (status === 'queued') {
          statusBadge.classList.add('bg-secondary');
          progressBar.style.width = '25%';
        } else if (status === 'processing') {
          statusBadge.classList.add('bg-primary');
          progressBar.style.width = '75%';
        } else if (status === 'completed') {
          statusBadge.classList.add('bg-success');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
        } else if (status === 'failed') {
          statusBadge.classList.add('bg-danger');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
          progressBar.classList.add('bg-danger');
        }
        submitButton.disabled = false;
        submitButton.textContent = 'Generate Speech';
      }

      function clearText() {
        textInput.value = '';
        hideError();
        if (statusCheckInterval) {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;
        }
        taskStatus.classList.add('d-none');
        resultPanel.style.display = 'none';
        audioPlayer.src = '';
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }
        return apiKey || '';
      }
    });
  </script>
</body>

</html>