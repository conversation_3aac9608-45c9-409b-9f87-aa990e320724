<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Mission Management Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 1200px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #mission-details-input, #conversation-history-input {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 15px;
      width: 100%;
      min-height: 150px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
    }

    #mission-details-input:focus, #conversation-history-input:focus {
      outline: none;
      border-color: #0056b3;
      box-shadow: 0 0 5px rgba(0, 86, 179, 0.3);
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    #mission-result-panel, #check-result-panel {
      margin-top: 20px;
      display: none;
    }

    #mission-display, #updated-mission-display {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      border-left: 5px solid #28a745;
    }

    .mission-objective {
      background-color: #e9ecef;
      padding: 10px;
      margin: 5px 0;
      border-radius: 5px;
      border-left: 3px solid #007bff;
    }

    .mission-step {
      background-color: #f8f9fa;
      padding: 15px;
      margin: 10px 0;
      border-radius: 5px;
      border-left: 3px solid #6c757d;
    }

    .step-status {
      font-weight: bold;
      text-transform: uppercase;
    }

    .step-status.pending {
      color: #6c757d;
    }

    .step-status.in-progress {
      color: #007bff;
    }

    .step-status.completed {
      color: #28a745;
    }

    .step-status.failed {
      color: #dc3545;
    }

    .tab-pane {
      padding-top: 20px;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Mission Management Service</h1>
      <p class="lead text-muted">Create and track mission progress with AI-powered mission planning</p>
    </div>

    <!-- Navigation Tabs -->
    <ul class="nav nav-tabs" id="missionTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="create-tab" data-bs-toggle="tab" data-bs-target="#create-mission" 
          type="button" role="tab" aria-controls="create-mission" aria-selected="true">
          Create Mission
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="check-tab" data-bs-toggle="tab" data-bs-target="#check-mission" 
          type="button" role="tab" aria-controls="check-mission" aria-selected="false">
          Check Mission Status
        </button>
      </li>
    </ul>

    <div class="tab-content" id="missionTabContent">
      <!-- Create Mission Tab -->
      <div class="tab-pane fade show active" id="create-mission" role="tabpanel" aria-labelledby="create-tab">
        <div class="step">
          <span class="step-number">1</span>
          <span class="step-title">Enter Mission Details</span>
          <p class="mb-4">Describe your mission objectives, requirements, and any specific details you want to include.</p>

          <textarea id="mission-details-input" placeholder="Example: Create a job interview preparation mission that includes greeting, resume introduction, discussing strengths and weaknesses, salary expectations, and questions for the interviewer..."></textarea>

          <div class="mt-3">
            <button id="create-mission-button" class="btn btn-success">Create Mission</button>
            <button id="clear-create-button" class="btn btn-outline-secondary ms-2">Clear</button>
          </div>

          <div class="alert alert-danger mt-3 d-none" id="create-error-message"></div>
        </div>

        <div class="step">
          <span class="step-number">2</span>
          <span class="step-title">Mission Plan</span>
          <p class="mb-4">Your generated mission plan will appear below once processing is complete.</p>

          <div id="mission-result-panel">
            <div id="mission-display">
              <h4 id="mission-title"></h4>
              <p id="mission-description"></p>
              
              <h5 class="mt-4">Objectives:</h5>
              <div id="mission-objectives"></div>
              
              <h5 class="mt-4">Steps:</h5>
              <div id="mission-steps"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Check Mission Tab -->
      <div class="tab-pane fade" id="check-mission" role="tabpanel" aria-labelledby="check-tab">
        <div class="step">
          <span class="step-number">1</span>
          <span class="step-title">Enter Current Mission</span>
          <p class="mb-4">Paste your existing mission JSON data to check its status.</p>

          <textarea id="current-mission-input" class="form-control" style="border: 2px solid #007bff; border-radius: 10px; padding: 15px; min-height: 200px;" 
            placeholder='Paste your mission JSON here, for example:
{
  "title": "Job Interview Mission",
  "description": "Complete job interview process",
  "objectives": ["Make good impression", "Present skills effectively"],
  "steps": [
    {"action": "Greet interviewer politely", "status": "pending"},
    {"action": "Introduce resume", "status": "pending"}
  ]
}'></textarea>
        </div>

        <div class="step">
          <span class="step-number">2</span>
          <span class="step-title">Enter Conversation History</span>
          <p class="mb-4">Provide the conversation or activity history to analyze mission progress.</p>

          <textarea id="conversation-history-input" placeholder="Enter the conversation history or activity log that shows progress on the mission..."></textarea>

          <div class="mt-3">
            <button id="check-mission-button" class="btn btn-primary">Check Mission Status</button>
            <button id="clear-check-button" class="btn btn-outline-secondary ms-2">Clear</button>
          </div>

          <div class="alert alert-danger mt-3 d-none" id="check-error-message"></div>
        </div>

        <div class="step">
          <span class="step-number">3</span>
          <span class="step-title">Updated Mission Status</span>
          <p class="mb-4">The updated mission status will appear below after analysis.</p>

          <div id="check-result-panel">
            <div id="updated-mission-display">
              <h4 id="updated-mission-title"></h4>
              <p id="updated-mission-description"></p>
              
              <h5 class="mt-4">Objectives:</h5>
              <div id="updated-mission-objectives"></div>
              
              <h5 class="mt-4">Steps:</h5>
              <div id="updated-mission-steps"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Mission Management Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const missionDetailsInput = document.getElementById('mission-details-input');
      const createMissionButton = document.getElementById('create-mission-button');
      const clearCreateButton = document.getElementById('clear-create-button');
      const createErrorMessage = document.getElementById('create-error-message');
      const missionResultPanel = document.getElementById('mission-result-panel');

      // Check mission elements
      const currentMissionInput = document.getElementById('current-mission-input');
      const conversationHistoryInput = document.getElementById('conversation-history-input');
      const checkMissionButton = document.getElementById('check-mission-button');
      const clearCheckButton = document.getElementById('clear-check-button');
      const checkErrorMessage = document.getElementById('check-error-message');
      const checkResultPanel = document.getElementById('check-result-panel');

      // Event Listeners
      createMissionButton.addEventListener('click', createMission);
      clearCreateButton.addEventListener('click', clearCreateForm);
      checkMissionButton.addEventListener('click', checkMissionStatus);
      clearCheckButton.addEventListener('click', clearCheckForm);

      function createMission() {
        const missionDetails = missionDetailsInput.value.trim();
        
        if (!missionDetails) {
          showCreateError('Please enter mission details');
          return;
        }

        hideCreateError();
        missionResultPanel.style.display = 'none';

        createMissionButton.disabled = true;
        createMissionButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating Mission...';

        fetch('/api/mission', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({
            mission_details: missionDetails
          })
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.mission) {
            displayMission(data.mission);
            missionResultPanel.style.display = 'block';
          } else {
            throw new Error('No mission data returned');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          showCreateError('Mission creation failed: ' + error.message);
        })
        .finally(() => {
          createMissionButton.disabled = false;
          createMissionButton.textContent = 'Create Mission';
        });
      }

      function checkMissionStatus() {
        const currentMissionText = currentMissionInput.value.trim();
        const conversationHistory = conversationHistoryInput.value.trim();
        
        if (!currentMissionText) {
          showCheckError('Please enter current mission data');
          return;
        }

        if (!conversationHistory) {
          showCheckError('Please enter conversation history');
          return;
        }

        let currentMission;
        try {
          currentMission = JSON.parse(currentMissionText);
        } catch (e) {
          showCheckError('Invalid mission JSON format');
          return;
        }

        hideCheckError();
        checkResultPanel.style.display = 'none';

        checkMissionButton.disabled = true;
        checkMissionButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Checking Status...';

        fetch('/api/mission/check', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({
            mission: currentMission,
            conversation_history: conversationHistory
          })
        })
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          if (data.updated_mission) {
            displayUpdatedMission(data.updated_mission);
            checkResultPanel.style.display = 'block';
          } else {
            throw new Error('No updated mission data returned');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          showCheckError('Mission status check failed: ' + error.message);
        })
        .finally(() => {
          checkMissionButton.disabled = false;
          checkMissionButton.textContent = 'Check Mission Status';
        });
      }

      function displayMission(mission) {
        document.getElementById('mission-title').textContent = mission.title;
        document.getElementById('mission-description').textContent = mission.description;
        
        // Display objectives
        const objectivesContainer = document.getElementById('mission-objectives');
        objectivesContainer.innerHTML = '';
        mission.objectives.forEach(objective => {
          const objectiveDiv = document.createElement('div');
          objectiveDiv.className = 'mission-objective';
          objectiveDiv.textContent = objective;
          objectivesContainer.appendChild(objectiveDiv);
        });

        // Display steps
        const stepsContainer = document.getElementById('mission-steps');
        stepsContainer.innerHTML = '';
        mission.steps.forEach((step, index) => {
          const stepDiv = document.createElement('div');
          stepDiv.className = 'mission-step';
          stepDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
              <div class="flex-grow-1">
                <strong>Step ${index + 1}:</strong> ${step.action}
              </div>
              <span class="step-status ${step.status}">${step.status}</span>
            </div>
          `;
          stepsContainer.appendChild(stepDiv);
        });
      }

      function displayUpdatedMission(mission) {
        document.getElementById('updated-mission-title').textContent = mission.title;
        document.getElementById('updated-mission-description').textContent = mission.description;
        
        // Display objectives
        const objectivesContainer = document.getElementById('updated-mission-objectives');
        objectivesContainer.innerHTML = '';
        mission.objectives.forEach(objective => {
          const objectiveDiv = document.createElement('div');
          objectiveDiv.className = 'mission-objective';
          objectiveDiv.textContent = objective;
          objectivesContainer.appendChild(objectiveDiv);
        });

        // Display steps
        const stepsContainer = document.getElementById('updated-mission-steps');
        stepsContainer.innerHTML = '';
        mission.steps.forEach((step, index) => {
          const stepDiv = document.createElement('div');
          stepDiv.className = 'mission-step';
          stepDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
              <div class="flex-grow-1">
                <strong>Step ${index + 1}:</strong> ${step.action}
              </div>
              <span class="step-status ${step.status}">${step.status}</span>
            </div>
          `;
          stepsContainer.appendChild(stepDiv);
        });
      }

      function clearCreateForm() {
        missionDetailsInput.value = '';
        hideCreateError();
        missionResultPanel.style.display = 'none';
      }

      function clearCheckForm() {
        currentMissionInput.value = '';
        conversationHistoryInput.value = '';
        hideCheckError();
        checkResultPanel.style.display = 'none';
      }

      function showCreateError(message) {
        createErrorMessage.textContent = message;
        createErrorMessage.classList.remove('d-none');
      }

      function hideCreateError() {
        createErrorMessage.classList.add('d-none');
      }

      function showCheckError(message) {
        checkErrorMessage.textContent = message;
        checkErrorMessage.classList.remove('d-none');
      }

      function hideCheckError() {
        checkErrorMessage.classList.add('d-none');
      }

      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }
        return apiKey || '';
      }
    });
  </script>
</body>

</html>