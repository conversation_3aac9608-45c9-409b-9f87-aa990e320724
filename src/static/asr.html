<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Speech Transcription Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #drop-area {
      border: 3px dashed #007bff;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      cursor: pointer;
      margin: 20px 0;
      transition: all 0.3s ease;
    }

    #drop-area:hover,
    #drop-area.highlight {
      background-color: #e8f4ff;
      border-color: #0056b3;
    }

    .file-info {
      margin: 20px 0;
      display: none;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    .transcript {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
      border-left: 5px solid #007bff;
    }

    .transcript.srt-format {
      white-space: pre-line;
      line-height: 1.6;
      font-family: 'Courier New', monospace;
    }

    .srt-entry {
      margin-bottom: 20px;
      padding: 10px;
      background-color: #ffffff;
      border-radius: 5px;
      border: 1px solid #e9ecef;
    }

    .srt-number {
      font-weight: bold;
      color: #007bff;
      font-size: 0.9em;
    }

    .srt-timestamp {
      color: #6c757d;
      font-size: 0.85em;
      font-family: 'Courier New', monospace;
    }

    .srt-text {
      margin-top: 5px;
      color: #343a40;
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }

    .form-check {
      margin: 15px 0;
    }

    .form-check-label {
      font-size: 0.95em;
      color: #495057;
    }

    .btn-group {
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .btn-check:checked+.btn {
      background-color: #007bff;
      border-color: #007bff;
      color: white;
    }

    #url-section {
      border: 2px solid #e9ecef;
      border-radius: 10px;
      padding: 20px;
      background-color: #f8f9fa;
    }

    #audio-url {
      border-radius: 8px;
      border: 1px solid #ced4da;
      transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    #audio-url:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Speech Transcription Service</h1>
      <p class="lead text-muted">Upload audio files or provide URLs to get accurate transcriptions in minutes</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Upload Audio</span>
      <p class="mb-4">Upload your audio file (MP3, WAV, M4A, etc.) or provide a URL to begin the transcription process.
      </p>

      <!-- Input Method Toggle -->
      <div class="mb-4">
        <div class="btn-group" role="group" aria-label="Input method selection">
          <input type="radio" class="btn-check" name="input-method" id="file-method" autocomplete="off" checked>
          <label class="btn btn-outline-primary" for="file-method">Upload File</label>

          <input type="radio" class="btn-check" name="input-method" id="url-method" autocomplete="off">
          <label class="btn btn-outline-primary" for="url-method">Audio URL</label>
        </div>
      </div>

      <!-- File Upload Section -->
      <div id="file-section">
        <div id="drop-area">
          <form id="upload-form">
            <p><i class="bi bi-cloud-arrow-up fs-1"></i></p>
            <p>Drag & drop your audio file here or click to select a file</p>
            <input type="file" id="file-input" accept="audio/*" class="d-none">
            <button type="button" id="select-button" class="btn btn-primary">Select File</button>
          </form>
        </div>
      </div>

      <!-- URL Input Section -->
      <div id="url-section" style="display: none;">
        <div class="mb-3">
          <label for="audio-url" class="form-label">Audio File URL</label>
          <input type="url" class="form-control" id="audio-url" placeholder="https://example.com/audio.mp3"
            autocomplete="off">
          <div class="form-text">Enter a direct URL to an audio file (MP3, WAV, M4A, etc.)</div>
        </div>
        <button type="button" id="url-button" class="btn btn-primary">Use URL</button>
      </div>

      <div class="file-info" id="file-info">
        <div class="d-flex align-items-center">
          <div class="me-3">
            <i class="bi bi-file-earmark-music fs-1" id="file-icon"></i>
          </div>
          <div>
            <h5 id="file-name">filename.mp3</h5>
            <p class="text-muted mb-0" id="file-size">0 MB</p>
            <p class="text-muted mb-0" id="file-source">File upload</p>
          </div>
        </div>
        <div class="mt-3">
          <div class="form-check mb-3">
            <input class="form-check-input" type="checkbox" id="srt-format" value="" checked>
            <label class="form-check-label" for="srt-format">
              Generate SRT format (with timestamps)
            </label>
          </div>
          <button id="upload-button" class="btn btn-success">Start Transcription</button>
          <button id="clear-button" class="btn btn-outline-secondary ms-2">Clear</button>
        </div>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">Get Results</span>
      <p class="mb-4">Once processing is complete, your transcription will appear below.</p>

      <div id="task-status" class="d-none">
        <p>
          Task ID: <span id="task-id" class="fw-bold"></span>
          <span class="ms-3">Status: <span id="status-badge"
              class="badge rounded-pill status-badge bg-secondary">Unknown</span></span>
        </p>
        <div class="progress mb-3" style="height: 10px;">
          <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" style="width: 0%">
          </div>
        </div>
      </div>

      <div id="result-panel" class="mt-4">
        <h4 id="result-title">Transcription</h4>
        <div class="transcript" id="transcript-text"></div>
        <button id="copy-button" class="btn btn-outline-primary mt-3">Copy to Clipboard</button>
        <button id="download-button" class="btn btn-outline-secondary mt-3 ms-2">Download as Text</button>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Speech Transcription Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const dropArea = document.getElementById('drop-area');
      const fileInput = document.getElementById('file-input');
      const selectButton = document.getElementById('select-button');
      const fileInfo = document.getElementById('file-info');
      const fileName = document.getElementById('file-name');
      const fileSize = document.getElementById('file-size');
      const fileSource = document.getElementById('file-source');
      const fileIcon = document.getElementById('file-icon');
      const srtFormat = document.getElementById('srt-format');
      const uploadButton = document.getElementById('upload-button');
      const clearButton = document.getElementById('clear-button');
      const errorMessage = document.getElementById('error-message');
      const taskStatus = document.getElementById('task-status');
      const taskIdElement = document.getElementById('task-id');
      const statusBadge = document.getElementById('status-badge');
      const progressBar = document.getElementById('progress-bar');
      const resultPanel = document.getElementById('result-panel');
      const resultTitle = document.getElementById('result-title');
      const transcriptText = document.getElementById('transcript-text');
      const copyButton = document.getElementById('copy-button');
      const downloadButton = document.getElementById('download-button');

      // Input method elements
      const fileMethodRadio = document.getElementById('file-method');
      const urlMethodRadio = document.getElementById('url-method');
      const fileSection = document.getElementById('file-section');
      const urlSection = document.getElementById('url-section');
      const audioUrlInput = document.getElementById('audio-url');
      const urlButton = document.getElementById('url-button');

      // Variables
      let selectedFile = null;
      let audioUrl = null;
      let taskId = null;
      let statusCheckInterval = null;

      // Event Listeners
      selectButton.addEventListener('click', () => fileInput.click());

      fileInput.addEventListener('change', (e) => {
        if (e.target.files.length) {
          handleFile(e.target.files[0]);
        }
      });

      // Input method toggle
      fileMethodRadio.addEventListener('change', () => {
        if (fileMethodRadio.checked) {
          fileSection.style.display = 'block';
          urlSection.style.display = 'none';
          clearFile();
        }
      });

      urlMethodRadio.addEventListener('change', () => {
        if (urlMethodRadio.checked) {
          fileSection.style.display = 'none';
          urlSection.style.display = 'block';
          clearFile();
        }
      });

      urlButton.addEventListener('click', () => {
        const url = audioUrlInput.value.trim();
        if (url) {
          handleUrl(url);
        } else {
          showError('Please enter a valid audio URL');
        }
      });

      audioUrlInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          urlButton.click();
        }
      });

      // Drag and drop functionality
      ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
      });

      function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
      }

      ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => {
          dropArea.classList.add('highlight');
        }, false);
      });

      ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, () => {
          dropArea.classList.remove('highlight');
        }, false);
      });

      dropArea.addEventListener('drop', (e) => {
        const dt = e.dataTransfer;
        const files = dt.files;
        if (files.length) {
          handleFile(files[0]);
        }
      });

      clearButton.addEventListener('click', clearFile);

      uploadButton.addEventListener('click', uploadFile);

      copyButton.addEventListener('click', () => {
        let textToCopy;

        if (transcriptText.classList.contains('srt-format') && transcriptText.children.length > 0) {
          // If it's structured SRT content, reconstruct the original SRT format
          textToCopy = Array.from(transcriptText.children).map(entry => {
            const number = entry.querySelector('.srt-number').textContent;
            const timestamp = entry.querySelector('.srt-timestamp').textContent;
            const text = entry.querySelector('.srt-text').textContent;
            return `${number}\n${timestamp}\n${text}`;
          }).join('\n\n');
        } else {
          textToCopy = transcriptText.textContent;
        }

        navigator.clipboard.writeText(textToCopy)
          .then(() => {
            copyButton.textContent = 'Copied!';
            setTimeout(() => {
              copyButton.textContent = 'Copy to Clipboard';
            }, 2000);
          });
      });

      downloadButton.addEventListener('click', () => {
        let textToDownload;

        if (transcriptText.classList.contains('srt-format') && transcriptText.children.length > 0) {
          // If it's structured SRT content, reconstruct the original SRT format
          textToDownload = Array.from(transcriptText.children).map(entry => {
            const number = entry.querySelector('.srt-number').textContent;
            const timestamp = entry.querySelector('.srt-timestamp').textContent;
            const text = entry.querySelector('.srt-text').textContent;
            return `${number}\n${timestamp}\n${text}`;
          }).join('\n\n');
        } else {
          textToDownload = transcriptText.textContent;
        }

        const blob = new Blob([textToDownload], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // Use .srt extension if SRT format was selected
        const fileName = srtFormat.checked ? 'transcription.srt' : 'transcription.txt';
        a.download = fileName;

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      });

      // Functions
      function handleFile(file) {
        if (!file.type.startsWith('audio/')) {
          showError('Please select an audio file');
          return;
        }

        selectedFile = file;
        audioUrl = null;
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileSource.textContent = 'File upload';
        fileIcon.className = 'bi bi-file-earmark-music fs-1';
        fileInfo.style.display = 'block';
        dropArea.style.display = 'none';
        hideError();
      }

      function handleUrl(url) {
        // Basic URL validation
        try {
          new URL(url);
        } catch (e) {
          showError('Please enter a valid URL');
          return;
        }

        selectedFile = null;
        audioUrl = url;
        const urlParts = url.split('/');
        const filename = urlParts[urlParts.length - 1] || 'audio-file';
        fileName.textContent = filename;
        fileSize.textContent = 'URL source';
        fileSource.textContent = 'Audio URL';
        fileIcon.className = 'bi bi-link-45deg fs-1';
        fileInfo.style.display = 'block';
        hideError();
      }

      function clearFile() {
        selectedFile = null;
        audioUrl = null;
        fileInput.value = '';
        audioUrlInput.value = '';
        fileInfo.style.display = 'none';
        dropArea.style.display = 'block';
        hideError();

        if (statusCheckInterval) {
          clearInterval(statusCheckInterval);
          statusCheckInterval = null;
        }

        taskStatus.classList.add('d-none');
        resultPanel.style.display = 'none';
      }

      function uploadFile() {
        if (!selectedFile && !audioUrl) {
          showError('No file selected or URL provided');
          return;
        }

        const formData = new FormData();

        if (selectedFile) {
          formData.append('file', selectedFile);
        } else if (audioUrl) {
          formData.append('audio_url', audioUrl);
        }

        formData.append('srt', srtFormat.checked);

        uploadButton.disabled = true;
        uploadButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';

        fetch('/api/asr', {
          method: 'POST',
          headers: {
            'X-API-KEY': getApiKey()
          },
          body: formData
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          })
          .then(data => {
            taskId = data.task_id;
            taskIdElement.textContent = taskId;

            taskStatus.classList.remove('d-none');
            updateStatus(data.status);

            // Start checking status
            statusCheckInterval = setInterval(checkStatus, 3000);
          })
          .catch(error => {
            showError('Processing failed: ' + error.message);
            uploadButton.disabled = false;
            uploadButton.textContent = 'Start Transcription';
          });
      }

      function checkStatus() {
        fetch(`/api/asr/status?task_id=${taskId}`, {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => response.json())
          .then(data => {
            updateStatus(data.status);

            if (data.status === 'completed') {
              clearInterval(statusCheckInterval);
              displayResults(data.result);
            } else if (data.status === 'failed') {
              clearInterval(statusCheckInterval);
              showError('Transcription failed');
            }
          })
          .catch(error => {
            showError('Status check failed: ' + error.message);
          });
      }

      function updateStatus(status) {
        statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);

        // Update badge color
        statusBadge.className = 'badge rounded-pill status-badge';

        if (status === 'queued') {
          statusBadge.classList.add('bg-secondary');
          progressBar.style.width = '25%';
        } else if (status === 'processing') {
          statusBadge.classList.add('bg-primary');
          progressBar.style.width = '75%';
        } else if (status === 'completed') {
          statusBadge.classList.add('bg-success');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
        } else if (status === 'failed') {
          statusBadge.classList.add('bg-danger');
          progressBar.style.width = '100%';
          progressBar.classList.remove('progress-bar-animated');
          progressBar.classList.add('bg-danger');
        }

        // Reset upload button
        uploadButton.disabled = false;
        uploadButton.textContent = 'Start Transcription';
      }

      function displayResults(result) {
        resultPanel.style.display = 'block';

        // Update title and download button text based on format
        if (srtFormat.checked) {
          resultTitle.textContent = 'SRT Subtitles';
          downloadButton.textContent = 'Download as SRT';
          transcriptText.classList.add('srt-format');
        } else {
          resultTitle.textContent = 'Transcription';
          downloadButton.textContent = 'Download as Text';
          transcriptText.classList.remove('srt-format');
        }

        // Check if we have SRT format result
        if (result && srtFormat.checked) {
          displaySrtContent(result);
        } else if (result && result.transcription) {
          transcriptText.textContent = result.transcription;
        } else if (result && result.text) {
          transcriptText.textContent = result.text;
        } else if (result && result.result && result.result.text) {
          // Extract text from the specific JSON structure
          transcriptText.textContent = result.result.text;

          // Add audio duration info if available
          if (result.result.additions && result.result.additions.duration) {
            const durationSeconds = Math.round(parseFloat(result.result.additions.duration) / 1000);
            transcriptText.textContent += `\n\nAudio duration: ${durationSeconds} seconds`;
          }

          // Add individual utterances if available and different from main text
          if (result.result.utterances && result.result.utterances.length > 0) {
            const utteranceList = result.result.utterances.map(u => u.text).join("\n");
            if (utteranceList !== result.result.text) {
              transcriptText.textContent += `\n\nSegmented utterances:\n${utteranceList}`;
            }
          }
        } else if (result && result.data && result.data.text) {
          transcriptText.textContent = result.data.text;
        } else {
          transcriptText.textContent = JSON.stringify(result, null, 2);
        }
      }

      function displaySrtContent(result) {
        const srtText = result.srt || '';
        // Clear existing content
        transcriptText.innerHTML = '';

        // Split SRT content into entries
        const srtEntries = srtText.split('\n\n').filter(entry => entry.trim());
        console.log('SRT Entries:', srtEntries);

        srtEntries.forEach(entry => {
          const lines = entry.trim().split('\n');
          if (lines.length >= 3) {
            const entryDiv = document.createElement('div');
            entryDiv.className = 'srt-entry';

            // SRT number
            const numberDiv = document.createElement('div');
            numberDiv.className = 'srt-number';
            numberDiv.textContent = lines[0];

            // Timestamp
            const timestampDiv = document.createElement('div');
            timestampDiv.className = 'srt-timestamp';
            timestampDiv.textContent = lines[1];

            // Text content (can be multiple lines)
            const textDiv = document.createElement('div');
            textDiv.className = 'srt-text';
            textDiv.textContent = lines.slice(2).join('\n');

            entryDiv.appendChild(numberDiv);
            entryDiv.appendChild(timestampDiv);
            entryDiv.appendChild(textDiv);

            transcriptText.appendChild(entryDiv);
          }
        });

        // If no valid SRT entries found, display as plain text
        if (transcriptText.children.length === 0) {
          transcriptText.textContent = srtText;
        }
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      function getApiKey() {
        // You can implement this in different ways:
        // 1. Read from localStorage if you have a settings page
        // 2. Prompt the user
        // 3. Set it in a configuration
        // 4. For demo purposes, hard-code it (NOT recommended for production)

        // For now, let's provide a simple implementation that checks localStorage
        // or prompts the user if not found
        let apiKey = localStorage.getItem('apiKey');

        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }

        return apiKey || '';
      }
    });
  </script>
</body>

</html>