<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Batch Text-to-Speech Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 1000px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    .csv-upload-area {
      border: 2px dashed #007bff;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .csv-upload-area:hover {
      background-color: #f8f9fa;
      border-color: #0056b3;
    }

    .csv-upload-area.dragover {
      background-color: #e3f2fd;
      border-color: #0056b3;
    }

    #voice-select {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 10px;
      width: 100%;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .progress-container {
      margin-top: 20px;
      display: none;
    }

    .batch-progress {
      margin-bottom: 10px;
    }

    .item-progress {
      font-size: 0.9em;
      margin-bottom: 5px;
    }

    .results-table {
      max-height: 400px;
      overflow-y: auto;
    }

    .status-success {
      color: #28a745;
    }

    .status-failed {
      color: #dc3545;
    }

    .status-processing {
      color: #007bff;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Batch Text-to-Speech Service</h1>
      <p class="lead text-muted">Convert multiple texts to speech from CSV files with AI-powered TTS technology</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Upload CSV File</span>
      <p class="mb-4">Upload a CSV file with columns: Topic, Question, 是否已录入. Only rows with '是否已录入' = '否' will be
        processed.</p>

      <div class="csv-upload-area" id="csv-upload-area">
        <div class="mb-3">
          <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
          <h5>Drag & Drop CSV File Here</h5>
          <p class="text-muted mb-3">or click to browse</p>
          <input type="file" id="csv-file-input" accept=".csv" class="d-none">
          <button class="btn btn-outline-primary">
            Choose File
          </button>
        </div>
      </div>

      <div class="mt-3" id="file-info" style="display: none;">
        <div class="alert alert-info">
          <strong>File selected:</strong> <span id="file-name"></span>
          <br><strong>Size:</strong> <span id="file-size"></span>
        </div>
      </div>

      <div class="mt-3">
        <label for="voice-select" class="form-label">Select Voice</label>
        <select id="voice-select" class="form-select">
          <option value="en_female_amanda_mars_bigtts">English (US) - Amanda (Female)</option>
          <option value="en_male_jackson_mars_bigtts">English (US) - Jackson (Male)</option>
        </select>
      </div>

      <div class="mt-3">
        <button id="process-button" class="btn btn-success" disabled>Process Batch TTS</button>
        <button id="download-button" class="btn btn-primary ms-2" style="display: none;">Download Results CSV</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">Processing Progress</span>
      <p class="mb-4">Monitor the progress of your batch TTS processing.</p>

      <div class="progress-container" id="progress-container">
        <div class="batch-progress">
          <label for="overall-progress">Overall Progress:</label>
          <div class="progress" style="height: 20px;">
            <div id="overall-progress" class="progress-bar progress-bar-striped progress-bar-animated"
              style="width: 0%"></div>
          </div>
          <small class="text-muted">
            <span id="progress-text">0 / 0 items processed</span>
          </small>
        </div>

        <div class="item-progress">
          <strong>Current Item:</strong> <span id="current-item">None</span>
        </div>
      </div>
    </div>

    <div class="step">
      <span class="step-number">3</span>
      <span class="step-title">Results</span>
      <p class="mb-4">View the results of your batch processing.</p>

      <div id="results-container" style="display: none;">
        <div class="results-table">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>Topic</th>
                <th>Question</th>
                <th>Status</th>
                <th>Audio URL</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody id="results-tbody">
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Batch Text-to-Speech Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const csvUploadArea = document.getElementById('csv-upload-area');
      const csvFileInput = document.getElementById('csv-file-input');
      const fileInfo = document.getElementById('file-info');
      const fileName = document.getElementById('file-name');
      const fileSize = document.getElementById('file-size');
      const voiceSelect = document.getElementById('voice-select');
      const processButton = document.getElementById('process-button');
      const downloadButton = document.getElementById('download-button');
      const errorMessage = document.getElementById('error-message');
      const progressContainer = document.getElementById('progress-container');
      const overallProgress = document.getElementById('overall-progress');
      const progressText = document.getElementById('progress-text');
      const currentItem = document.getElementById('current-item');
      const resultsContainer = document.getElementById('results-container');
      const resultsTbody = document.getElementById('results-tbody');

      let selectedFile = null;
      let processedResults = [];
      let originalCsvData = []; // Store original CSV data
      let csvHeaders = []; // Store CSV headers

      // Event Listeners
      csvUploadArea.addEventListener('click', handleCsvUploadAreaClick);

      csvUploadArea.addEventListener('dragover', handleDragOver);
      csvUploadArea.addEventListener('drop', handleDrop);
      csvFileInput.addEventListener('change', handleFileSelect);
      processButton.addEventListener('click', processBatchTTS);
      downloadButton.addEventListener('click', downloadResults);

      // Fetch available voices
      fetchVoices();
      function handleCsvUploadAreaClick(e) {
        csvFileInput.click();
      }
      function handleDragOver(e) {
        e.preventDefault();
        csvUploadArea.classList.add('dragover');
      }

      function handleDrop(e) {
        e.preventDefault();
        csvUploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
          handleFileSelect({ target: { files } });
        }
      }

      function handleFileSelect(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (!file.name.toLowerCase().endsWith('.csv')) {
          showError('Please select a CSV file');
          return;
        }

        selectedFile = file;
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
        processButton.disabled = false;
        hideError();
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      function fetchVoices() {
        fetch('/api/tts_voices', {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to fetch voices');
            }
            return response.json();
          })
          .then(data => {
            voiceSelect.innerHTML = '';
            data.voices.forEach(voice => {
              const option = document.createElement('option');
              option.value = voice.voice_type;
              option.textContent = `${voice.name} (${voice.language})`;
              voiceSelect.appendChild(option);
            });
          })
          .catch(error => {
            console.error('Error fetching voices:', error);
          });
      }

      async function processBatchTTS() {
        if (!selectedFile) {
          showError('Please select a CSV file first');
          return;
        }

        processButton.disabled = true;
        processButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        progressContainer.style.display = 'block';
        resultsContainer.style.display = 'none';
        processedResults = [];

        try {
          // Parse CSV file and store original data
          const csvText = await readFileAsText(selectedFile);
          const { headers, data } = parseCSVWithHeaders(csvText);
          csvHeaders = headers;
          originalCsvData = data;

          // Add AudioURL column if it doesn't exist
          if (!csvHeaders.includes('AudioURL')) {
            csvHeaders.push('AudioURL');
            originalCsvData.forEach(row => {
              row['AudioURL'] = '';
            });
          }

          // Filter rows that need processing
          const itemsToProcess = originalCsvData.filter(row =>
            row.Question && row['是否已录入'] === '否'
          );

          if (itemsToProcess.length === 0) {
            showError('No items found to process (no rows with 是否已录入 = 否)');
            return;
          }

          updateProgress(0, itemsToProcess.length);

          // Process each item and update original data
          for (let i = 0; i < itemsToProcess.length; i++) {
            const item = itemsToProcess[i];
            currentItem.textContent = `${item.Topic}: ${item.Question.substring(0, 50)}...`;

            try {
              const audioUrl = await processSingleTTS(item.Question, voiceSelect.value, item.Topic);

              // Find the original row and update it
              const originalRowIndex = originalCsvData.findIndex(row =>
                row.Question === item.Question && row.Topic === item.Topic
              );

              if (originalRowIndex !== -1) {
                originalCsvData[originalRowIndex]['AudioURL'] = audioUrl;
                // originalCsvData[originalRowIndex]['是否已录入'] = '是';
              }

              processedResults.push({
                ...item,
                status: 'success',
                audioUrl: audioUrl
              });
            } catch (error) {
              console.error(`Error processing item ${i + 1}:`, error);
              processedResults.push({
                ...item,
                status: 'failed',
                error: error.message
              });
            }

            updateProgress(i + 1, itemsToProcess.length);
            displayResults();
          }

          currentItem.textContent = 'Completed';
          downloadButton.style.display = 'inline-block';
          resultsContainer.style.display = 'block';

        } catch (error) {
          showError('Failed to process batch TTS: ' + error.message);
        } finally {
          processButton.disabled = false;
          processButton.textContent = 'Process Batch TTS';
        }
      }

      async function processSingleTTS(question, voice, topic) {
        // Use the new batch TTS endpoint
        const response = await fetch('/api/batch_tts/process_item', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({
            question: question,
            voice: voice,
            topic: topic
          })
        });

        if (!response.ok) {
          throw new Error('TTS processing failed');
        }

        const data = await response.json();
        return data.audio_url;
      }

      function readFileAsText(file) {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = e => resolve(e.target.result);
          reader.onerror = e => reject(e);
          reader.readAsText(file);
        });
      }

      function parseCSVWithHeaders(text) {
        const lines = text.split('\n');
        if (lines.length < 2) return { headers: [], data: [] };

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const values = parseCSVLine(line);
          if (values.length >= headers.length) {
            const row = {};
            headers.forEach((header, index) => {
              row[header] = values[index] || '';
            });
            data.push(row);
          }
        }

        return { headers, data };
      }

      function parseCSV(text) {
        const lines = text.split('\n');
        if (lines.length < 2) return [];

        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
        const data = [];

        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue;

          const values = parseCSVLine(line);
          if (values.length >= headers.length) {
            const row = {};
            headers.forEach((header, index) => {
              row[header] = values[index] || '';
            });
            data.push(row);
          }
        }

        return data;
      }

      function parseCSVLine(line) {
        const result = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
          const char = line[i];

          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            result.push(current.trim().replace(/"/g, ''));
            current = '';
          } else {
            current += char;
          }
        }

        result.push(current.trim().replace(/"/g, ''));
        return result;
      }

      function updateProgress(completed, total) {
        const percentage = (completed / total) * 100;
        overallProgress.style.width = percentage + '%';
        progressText.textContent = `${completed} / ${total} items processed`;
      }

      function displayResults() {
        resultsTbody.innerHTML = '';

        processedResults.forEach(result => {
          const row = document.createElement('tr');
          row.innerHTML = `
            <td>${result.Topic || 'N/A'}</td>
            <td>${result.Question ? result.Question.substring(0, 50) + '...' : 'N/A'}</td>
            <td>
              <span class="status-${result.status}">
                ${result.status === 'success' ? '✓ Success' : '✗ Failed'}
              </span>
            </td>
            <td>
              ${result.audioUrl ? `<audio controls>
                <source src="${result.audioUrl}" type="audio/mpeg">
                Your browser does not support the audio element.
              </audio>` : 'N/A'}
            </td>
            <td>
              ${result.audioUrl ?
              `<button class="btn btn-sm btn-secondary" onclick="downloadAudio('${result.audioUrl}', '${result.Topic}__${result.Question}')">Download</button>` :
              'N/A'
            }
            </td>
          `;
          resultsTbody.appendChild(row);
        });
      }

      function downloadResults() {
        const csvContent = generateUpdatedCSV();
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // Generate filename with _updated suffix
        const originalName = selectedFile.name.replace('.csv', '');
        link.download = `${originalName}_updated.csv`;

        link.click();
        URL.revokeObjectURL(url);
      }

      function generateUpdatedCSV() {
        // Create CSV with original structure plus AudioURL
        const rows = [csvHeaders.join(',')];

        originalCsvData.forEach(row => {
          const csvRow = csvHeaders.map(header => {
            const value = row[header] || '';
            // Escape quotes and wrap in quotes if contains comma or quote
            if (value.includes(',') || value.includes('"') || value.includes('\n')) {
              return `"${value.replace(/"/g, '""')}"`;
            }
            return value;
          });
          rows.push(csvRow.join(','));
        });

        return rows.join('\n');
      }

      window.downloadAudio = function (url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename + '.mp3';
        link.click();
      };

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }
        return apiKey || '';
      }
    });
  </script>
</body>

</html>