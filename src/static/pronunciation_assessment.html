<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Pronunciation Assessment Service</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 800px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #drop-area {
      border: 3px dashed #007bff;
      border-radius: 10px;
      padding: 40px;
      text-align: center;
      cursor: pointer;
      margin: 20px 0;
      transition: all 0.3s ease;
    }

    #drop-area:hover,
    #drop-area.highlight {
      background-color: #e8f4ff;
      border-color: #0056b3;
    }

    .file-info {
      margin: 20px 0;
      display: none;
    }

    #result-panel {
      margin-top: 20px;
      display: none;
    }

    .transcript {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      white-space: pre-wrap;
      max-height: 300px;
      overflow-y: auto;
      border-left: 5px solid #007bff;
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }

    /* Custom styling for pronunciation assessment */
    .recorded-controls {
      display: none;
    }

    .recorder-vis {
      height: 60px;
      background: #e9ecef;
      border-radius: 10px;
      margin: 15px 0;
      overflow: hidden;
      position: relative;
    }

    .recorder-vis canvas {
      width: 100%;
      height: 100%;
    }

    .tab-content {
      padding: 20px 0;
    }

    .word-item {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      padding: 8px 12px;
      border-radius: 5px;
    }

    .word-perfect {
      background-color: #d4edda;
      border: 1px solid #c3e6cb;
    }

    .word-good {
      background-color: #fff3cd;
      border: 1px solid #ffeeba;
    }

    .word-poor {
      background-color: #f8d7da;
      border: 1px solid #f5c6cb;
    }

    .phoneme-container {
      margin-top: 5px;
      font-size: 0.8rem;
    }

    .phoneme-item {
      display: inline-block;
      margin-right: 5px;
      padding: 2px 5px;
      border-radius: 3px;
    }

    .score-card {
      text-align: center;
      padding: 15px;
      border-radius: 10px;
      margin-bottom: 20px;
      background-color: #e9ecef;
    }

    .score-value {
      font-size: 2rem;
      font-weight: bold;
    }

    .score-label {
      font-size: 1rem;
      color: #6c757d;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Pronunciation Assessment</h1>
      <p class="lead text-muted">Record or upload audio to assess your pronunciation accuracy</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Reference Text</span>
      <p class="mb-4">Enter the text you want to practice pronouncing or upload a text file.</p>

      <ul class="nav nav-tabs" id="textInputTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="type-tab" data-bs-toggle="tab" data-bs-target="#type" type="button"
            role="tab" aria-controls="type" aria-selected="true">Type Text</button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button"
            role="tab" aria-controls="upload" aria-selected="false">Upload Text File</button>
        </li>
      </ul>
      <div class="tab-content" id="textInputTabContent">
        <div class="tab-pane fade show active" id="type" role="tabpanel" aria-labelledby="type-tab">
          <div class="form-floating">
            <textarea class="form-control" id="reference-text" style="height: 120px"
              placeholder="Enter text to practice pronouncing"></textarea>
            <label for="reference-text">Reference Text (max 300 characters)</label>
          </div>
          <div class="d-flex justify-content-between mt-2">
            <small class="text-muted">Character Count: <span id="char-count">0</span>/300</small>
            <button id="sample-text-btn" class="btn btn-sm btn-outline-secondary">Use Sample Text</button>
          </div>
        </div>
        <div class="tab-pane fade" id="upload" role="tabpanel" aria-labelledby="upload-tab">
          <div id="text-drop-area">
            <form id="text-upload-form">
              <p><i class="bi bi-file-text fs-1"></i></p>
              <p>Drag & drop a text file here or click to select</p>
              <input type="file" id="text-file-input" accept=".txt" class="d-none">
              <button type="button" id="text-select-button" class="btn btn-primary">Select File</button>
            </form>
          </div>
          <div id="text-file-info" class="file-info">
            <div class="d-flex align-items-center">
              <div class="me-3">
                <i class="bi bi-file-text fs-1"></i>
              </div>
              <div>
                <h5 id="text-file-name">filename.txt</h5>
                <p class="text-muted mb-0" id="text-file-size">0 KB</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group mt-3">
        <label for="language-select">Language</label>
        <select class="form-select" id="language-select">
          <option value="en-US" selected>English (US)</option>
          <option value="en-GB">English (UK)</option>
          <option value="es-ES">Spanish (Spain)</option>
          <option value="fr-FR">French (France)</option>
          <option value="de-DE">German (Germany)</option>
          <option value="zh-CN">Chinese (Mandarin)</option>
          <option value="ja-JP">Japanese</option>
        </select>
      </div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">Audio Input</span>
      <p class="mb-4">Record your voice or upload an audio file (max 30 seconds).</p>

      <ul class="nav nav-tabs" id="audioInputTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="record-tab" data-bs-toggle="tab" data-bs-target="#record" type="button"
            role="tab" aria-controls="record" aria-selected="true">Record Audio</button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="audio-upload-tab" data-bs-toggle="tab" data-bs-target="#audio-upload"
            type="button" role="tab" aria-controls="audio-upload" aria-selected="false">Upload Audio</button>
        </li>
      </ul>
      <div class="tab-content" id="audioInputTabContent">
        <div class="tab-pane fade show active" id="record" role="tabpanel" aria-labelledby="record-tab">
          <div class="recorder-vis">
            <canvas id="visualizer"></canvas>
          </div>
          <div class="d-flex justify-content-between mt-3">
            <div class="recorder-time">
              <span id="time-display">00:00</span>
              <span class="text-muted"> / 00:30</span>
            </div>
            <div>
              <button id="record-button" class="btn btn-danger">Start Recording</button>
              <div class="recorded-controls" id="recorded-controls">
                <button id="play-button" class="btn btn-outline-primary me-2">Play</button>
                <button id="discard-button" class="btn btn-outline-secondary">Discard</button>
              </div>
            </div>
          </div>
        </div>
        <div class="tab-pane fade" id="audio-upload" role="tabpanel" aria-labelledby="audio-upload-tab">
          <div id="audio-drop-area">
            <form id="audio-upload-form">
              <p><i class="bi bi-file-earmark-music fs-1"></i></p>
              <p>Drag & drop an audio file here or click to select</p>
              <input type="file" id="audio-file-input" accept="audio/*" class="d-none">
              <button type="button" id="audio-select-button" class="btn btn-primary">Select File</button>
            </form>
          </div>
          <div id="audio-file-info" class="file-info">
            <div class="d-flex align-items-center">
              <div class="me-3">
                <i class="bi bi-file-earmark-music fs-1"></i>
              </div>
              <div>
                <h5 id="audio-file-name">filename.mp3</h5>
                <p class="text-muted mb-0" id="audio-file-size">0 MB</p>
              </div>
            </div>
            <div class="mt-3">
              <audio id="audio-preview" controls class="w-100"></audio>
            </div>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <button id="assess-button" class="btn btn-success btn-lg w-100">Assess Pronunciation</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">3</span>
      <span class="step-title">Assessment Results</span>
      <p class="mb-4">View your pronunciation assessment results and improve your skills.</p>

      <div id="loading" class="text-center py-4 d-none">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Analyzing your pronunciation...</p>
      </div>

      <div id="result-panel">
        <div class="row mb-4">
          <div class="col-md-3">
            <div class="score-card">
              <div class="score-value" id="overall-score">--</div>
              <div class="score-label">Overall</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="score-card">
              <div class="score-value" id="accuracy-score">--</div>
              <div class="score-label">Accuracy</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="score-card">
              <div class="score-value" id="fluency-score">--</div>
              <div class="score-label">Fluency</div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="score-card">
              <div class="score-value" id="completeness-score">--</div>
              <div class="score-label">Completeness</div>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <h5>Reference Text</h5>
          <div class="transcript" id="reference-text-display"></div>
        </div>

        <div class="mb-4">
          <h5>Recognized Text</h5>
          <div class="transcript" id="recognized-text-display"></div>
        </div>

        <h5>Word Breakdown</h5>
        <div id="word-breakdown" class="mb-4"></div>

        <button id="retry-button" class="btn btn-primary">Try Again</button>
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Pronunciation Assessment Service | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements - Reference Text
      const referenceText = document.getElementById('reference-text');
      const charCount = document.getElementById('char-count');
      const sampleTextBtn = document.getElementById('sample-text-btn');
      const textFileInput = document.getElementById('text-file-input');
      const textSelectButton = document.getElementById('text-select-button');
      const textFileInfo = document.getElementById('text-file-info');
      const textFileName = document.getElementById('text-file-name');
      const textFileSize = document.getElementById('text-file-size');
      const textDropArea = document.getElementById('text-drop-area');
      const languageSelect = document.getElementById('language-select');

      // DOM Elements - Audio
      const recordButton = document.getElementById('record-button');
      const timeDisplay = document.getElementById('time-display');
      const playButton = document.getElementById('play-button');
      const discardButton = document.getElementById('discard-button');
      const recordedControls = document.getElementById('recorded-controls');
      const visualizer = document.getElementById('visualizer');
      const audioFileInput = document.getElementById('audio-file-input');
      const audioSelectButton = document.getElementById('audio-select-button');
      const audioFileInfo = document.getElementById('audio-file-info');
      const audioFileName = document.getElementById('audio-file-name');
      const audioFileSize = document.getElementById('audio-file-size');
      const audioDropArea = document.getElementById('audio-drop-area');
      const audioPreview = document.getElementById('audio-preview');

      // DOM Elements - Assessment
      const assessButton = document.getElementById('assess-button');
      const errorMessage = document.getElementById('error-message');
      const loading = document.getElementById('loading');
      const resultPanel = document.getElementById('result-panel');
      const overallScore = document.getElementById('overall-score');
      const accuracyScore = document.getElementById('accuracy-score');
      const fluencyScore = document.getElementById('fluency-score');
      const completenessScore = document.getElementById('completeness-score');
      const referenceTextDisplay = document.getElementById('reference-text-display');
      const recognizedTextDisplay = document.getElementById('recognized-text-display');
      const wordBreakdown = document.getElementById('word-breakdown');
      const retryButton = document.getElementById('retry-button');

      // Variables
      let mediaRecorder;
      let audioChunks = [];
      let audioBlob = null;
      let audioFile = null;
      let recordingStartTime;
      let recordingTimer;
      let audioContext;
      let analyser;
      let visualizationCanvas;
      let canvasContext;
      let isRecording = false;
      let selectedTextFile = null;
      let recordedAudioUrl = null;

      // Sample texts for various languages
      const sampleTexts = {
        'en-US': 'The quick brown fox jumps over the lazy dog. How vexingly quick daft zebras jump!',
        'en-GB': 'The rain in Spain stays mainly in the plain. Proper pronunciation is essential for clear communication.',
        'es-ES': 'El veloz murciélago hindú comía feliz cardillo y kiwi. La cigüeña tocaba el saxofón detrás del palenque de paja.',
        'fr-FR': 'Portez ce vieux whisky au juge blond qui fume. Les naïfs ægithales hâtifs pondant à Noël où il gèle sont sûrs d\'être déçus.',
        'de-DE': 'Victor jagt zwölf Boxkämpfer quer über den großen Sylter Deich. Falsches Üben von Xylophonmusik quält jeden größeren Zwerg.',
        'zh-CN': '床前明月光，疑是地上霜。举头望明月，低头思故乡。',
        'ja-JP': 'いろはにほへと ちりぬるを わかよたれそ つねならむ うゐのおくやま けふこえて あさきゆめみし ゑひもせす'
      };

      // Initialize
      init();

      // Event listeners
      sampleTextBtn.addEventListener('click', () => {
        const language = languageSelect.value;
        referenceText.value = sampleTexts[language] || sampleTexts['en-US'];
        updateCharCount();
      });

      referenceText.addEventListener('input', updateCharCount);

      textSelectButton.addEventListener('click', () => textFileInput.click());

      textFileInput.addEventListener('change', (e) => {
        if (e.target.files.length) {
          handleTextFile(e.target.files[0]);
        }
      });

      recordButton.addEventListener('click', toggleRecording);
      playButton.addEventListener('click', playRecording);
      discardButton.addEventListener('click', discardRecording);

      audioSelectButton.addEventListener('click', () => audioFileInput.click());

      audioFileInput.addEventListener('change', (e) => {
        if (e.target.files.length) {
          handleAudioFile(e.target.files[0]);
        }
      });

      assessButton.addEventListener('click', assessPronunciation);
      retryButton.addEventListener('click', resetAssessment);

      // Drag and drop for text files
      setupDragDrop(textDropArea, textFileInput, handleTextFile);

      // Drag and drop for audio files
      setupDragDrop(audioDropArea, audioFileInput, handleAudioFile);

      // Functions
      function init() {
        setupTextTabEvents();
        setupAudioTabEvents();
        setupVisualizer();
        updateCharCount();
      }

      function setupTextTabEvents() {
        const typeTrigger = document.getElementById('type-tab');
        const uploadTrigger = document.getElementById('upload-tab');

        typeTrigger.addEventListener('click', () => {
          if (selectedTextFile) {
            // Transfer text from file to textarea
            referenceText.value = referenceText.value || '';
          }
        });
      }

      function setupAudioTabEvents() {
        const recordTrigger = document.getElementById('record-tab');
        const uploadTrigger = document.getElementById('audio-upload-tab');

        recordTrigger.addEventListener('click', () => {
          if (recordedAudioUrl) {
            recordedControls.style.display = 'block';
          } else {
            recordedControls.style.display = 'none';
          }
          if (audioFile) {
            audioFile = null;
          }
        });

        uploadTrigger.addEventListener('click', () => {
          if (recordedAudioUrl) {
            stopMediaTracks();
            audioBlob = null;
            recordedAudioUrl = null;
            isRecording = false;
            recordButton.textContent = 'Start Recording';
            recordedControls.style.display = 'none';
          }
        });
      }

      function setupVisualizer() {
        visualizationCanvas = visualizer;
        canvasContext = visualizationCanvas.getContext('2d');

        // Set canvas dimensions to match its display size
        const rect = visualizer.getBoundingClientRect();
        visualizationCanvas.width = rect.width;
        visualizationCanvas.height = rect.height;

        // Draw initial empty state
        canvasContext.fillStyle = '#e9ecef';
        canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
      }

      function updateCharCount() {
        const text = referenceText.value;
        const count = text.length;
        charCount.textContent = count;

        // Visual feedback if over limit
        if (count > 300) {
          charCount.classList.add('text-danger');
          referenceText.classList.add('is-invalid');
        } else {
          charCount.classList.remove('text-danger');
          referenceText.classList.remove('is-invalid');
        }
      }

      function setupDragDrop(dropArea, fileInput, handleFileFunc) {
        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
          dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
          e.preventDefault();
          e.stopPropagation();
        }

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
          dropArea.addEventListener(eventName, () => {
            dropArea.classList.add('highlight');
          }, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
          dropArea.addEventListener(eventName, () => {
            dropArea.classList.remove('highlight');
          }, false);
        });

        // Handle dropped files
        dropArea.addEventListener('drop', (e) => {
          const dt = e.dataTransfer;
          const files = dt.files;
          if (files.length) {
            handleFileFunc(files[0]);
          }
        });
      }

      function handleTextFile(file) {
        if (file.type !== 'text/plain') {
          showError('Please select a text (.txt) file');
          return;
        }

        selectedTextFile = file;
        textFileName.textContent = file.name;
        textFileSize.textContent = formatFileSize(file.size);
        textFileInfo.style.display = 'block';
        textDropArea.style.display = 'none';

        // Read file contents
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target.result;
          referenceText.value = content.substring(0, 300); // Limit to 300 chars
          updateCharCount();
        };
        reader.readAsText(file);
      }

      function handleAudioFile(file) {
        if (!file.type.startsWith('audio/')) {
          showError('Please select an audio file');
          return;
        }

        audioFile = file;
        audioFileName.textContent = file.name;
        audioFileSize.textContent = formatFileSize(file.size);
        audioFileInfo.style.display = 'block';
        audioDropArea.style.display = 'none';

        // Create preview URL
        const url = URL.createObjectURL(file);
        audioPreview.src = url;

        hideError();
      }

      async function toggleRecording() {
        if (!isRecording) {
          await startRecording();
        } else {
          stopRecording();
        }
      }

      async function startRecording() {
        try {
          // Clear previous recording
          if (recordedAudioUrl) {
            URL.revokeObjectURL(recordedAudioUrl);
            recordedAudioUrl = null;
          }

          audioChunks = [];
          recordedControls.style.display = 'none';

          // Initialize audio context if not already done
          if (!audioContext) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;
          }

          // Request microphone access
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

          // Connect to visualizer
          const source = audioContext.createMediaStreamSource(stream);
          source.connect(analyser);

          // Start visualization
          visualize();

          // Create media recorder
          mediaRecorder = new MediaRecorder(stream);

          mediaRecorder.ondataavailable = (e) => {
            if (e.data.size > 0) {
              audioChunks.push(e.data);
            }
          };

          mediaRecorder.onstop = () => {
            audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
            recordedAudioUrl = URL.createObjectURL(audioBlob);
            recordedControls.style.display = 'block';
            clearInterval(recordingTimer);
          };

          // Start recording
          mediaRecorder.start();
          isRecording = true;
          recordButton.textContent = 'Stop Recording';
          recordButton.classList.remove('btn-danger');
          recordButton.classList.add('btn-warning');

          // Start timer
          recordingStartTime = Date.now();
          updateTimer();
          recordingTimer = setInterval(updateTimer, 1000);

          // Auto-stop after 30 seconds
          setTimeout(() => {
            if (isRecording) {
              stopRecording();
            }
          }, 30000);

        } catch (error) {
          showError('Could not access microphone: ' + error.message);
        }
      }

      function stopRecording() {
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
          mediaRecorder.stop();
          isRecording = false;
          recordButton.textContent = 'Start Recording';
          recordButton.classList.remove('btn-warning');
          recordButton.classList.add('btn-danger');
          stopVisualizer();
        }
      }

      function updateTimer() {
        const elapsed = Math.floor((Date.now() - recordingStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
        const seconds = (elapsed % 60).toString().padStart(2, '0');
        timeDisplay.textContent = `${minutes}:${seconds}`;

        // Visual cue when approaching limit
        if (elapsed >= 25) {
          timeDisplay.classList.add('text-danger');
        }
      }

      function playRecording() {
        if (recordedAudioUrl) {
          const audio = new Audio(recordedAudioUrl);
          audio.play();
        }
      }

      function discardRecording() {
        if (recordedAudioUrl) {
          URL.revokeObjectURL(recordedAudioUrl);
          recordedAudioUrl = null;
          audioChunks = [];
          audioBlob = null;
          recordedControls.style.display = 'none';
          timeDisplay.textContent = '00:00';
          timeDisplay.classList.remove('text-danger');
        }
      }

      function visualize() {
        if (!analyser) return;

        const bufferLength = analyser.frequencyBinCount;
        const dataArray = new Uint8Array(bufferLength);

        canvasContext.clearRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);

        function draw() {
          if (!isRecording) return;

          requestAnimationFrame(draw);

          analyser.getByteFrequencyData(dataArray);

          canvasContext.fillStyle = '#e9ecef';
          canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);

          const barWidth = (visualizationCanvas.width / bufferLength) * 2.5;
          let x = 0;

          for (let i = 0; i < bufferLength; i++) {
            const barHeight = dataArray[i] / 255 * visualizationCanvas.height;

            canvasContext.fillStyle = `rgb(${dataArray[i]}, 121, 255)`;
            canvasContext.fillRect(x, visualizationCanvas.height - barHeight, barWidth, barHeight);

            x += barWidth + 1;
          }
        }

        draw();
      }

      function stopVisualizer() {
        canvasContext.fillStyle = '#e9ecef';
        canvasContext.fillRect(0, 0, visualizationCanvas.width, visualizationCanvas.height);
      }

      function stopMediaTracks() {
        if (mediaRecorder && mediaRecorder.stream) {
          mediaRecorder.stream.getTracks().forEach(track => track.stop());
        }
      }

      function assessPronunciation() {
        // Get the reference text
        const text = referenceText.value.trim();
        if (!text || text.length === 0) {
          showError('Please enter or upload reference text');
          return;
        }

        if (text.length > 300) {
          showError('Reference text is too long. Maximum 300 characters allowed.');
          return;
        }

        // Check if we have audio
        let audioSource = null;

        if (audioBlob) {
          // Use recorded audio
          audioSource = audioBlob;
        } else if (audioFile) {
          // Use uploaded audio
          audioSource = audioFile;
        } else {
          showError('Please record or upload audio');
          return;
        }

        // Show loading state
        hideError();
        loading.classList.remove('d-none');
        assessButton.disabled = true;
        resultPanel.style.display = 'none';

        const language = languageSelect.value;

        // Generate appropriate filename based on source
        let filename;
        if (audioBlob) {
          // For recorded audio, use a WAV filename
          filename = `recorded_audio.${mediaRecorder.mimeType.split('/')[1] || 'webm'}`;
        } else if (audioFile) {
          // For uploaded audio, preserve the original filename
          filename = audioFile.name;
        }
        // Create form data for API request
        const formData = new FormData();
        formData.append('reference_text', text);
        formData.append('language', language);
        formData.append('audio_file', audioSource, filename);

        // Make API request
        fetch('/api/pronunciation_assessment', {
          method: 'POST',
          headers: {
            'X-API-KEY': getApiKey()
          },
          body: formData
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Server returned status ' + response.status);
            }
            return response.json();
          })
          .then(data => {
            displayResults(data);
          })
          .catch(error => {
            showError('Assessment failed: ' + error.message);
            loading.classList.add('d-none');
            assessButton.disabled = false;
          });
      }

      function displayResults(data) {
        // Hide loading, enable button
        loading.classList.add('d-none');
        assessButton.disabled = false;

        // Show result panel
        resultPanel.style.display = 'block';

        if (data.status !== 'success' || !data.result) {
          const errorMsg = data.error_message || 'Assessment failed';
          showError(errorMsg);
          return;
        }

        // Display scores
        const result = data.result;
        overallScore.textContent = Math.round(result.pronunciation_score);
        accuracyScore.textContent = Math.round(result.accuracy_score);
        fluencyScore.textContent = Math.round(result.fluency_score);
        completenessScore.textContent = Math.round(result.completeness_score);

        // Display texts
        referenceTextDisplay.textContent = result.reference_text;
        recognizedTextDisplay.textContent = result.recognized_text;

        // Build word breakdown
        wordBreakdown.innerHTML = '';

        if (result.words && result.words.length > 0) {
          result.words.forEach(word => {
            const wordDiv = document.createElement('div');
            wordDiv.className = 'word-item';

            let scoreClass = '';
            if (word.accuracy_score >= 80) {
              scoreClass = 'word-perfect';
            } else if (word.accuracy_score >= 60) {
              scoreClass = 'word-good';
            } else {
              scoreClass = 'word-poor';
            }

            wordDiv.classList.add(scoreClass);

            const wordContent = document.createElement('div');
            wordContent.innerHTML = `<strong>${word.word}</strong> <span class="badge bg-secondary">${Math.round(word.accuracy_score)}</span>`;

            const phonemeContainer = document.createElement('div');
            phonemeContainer.className = 'phoneme-container';

            if (word.phonemes && word.phonemes.length > 0) {
              word.phonemes.forEach(phoneme => {
                const phonemeItem = document.createElement('span');
                phonemeItem.className = 'phoneme-item';

                if (phoneme.accuracy_score >= 80) {
                  phonemeItem.style.backgroundColor = '#d4edda';
                } else if (phoneme.accuracy_score >= 60) {
                  phonemeItem.style.backgroundColor = '#fff3cd';
                } else {
                  phonemeItem.style.backgroundColor = '#f8d7da';
                }

                phonemeItem.textContent = phoneme.phoneme;
                phonemeContainer.appendChild(phonemeItem);
              });
            }

            wordDiv.appendChild(wordContent);
            wordDiv.appendChild(phonemeContainer);
            wordBreakdown.appendChild(wordDiv);
          });
        } else {
          wordBreakdown.innerHTML = '<p>No word breakdown available</p>';
        }
      }

      function resetAssessment() {
        // Clear results
        resultPanel.style.display = 'none';

        // Scroll back to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
      }

      function getApiKey() {
        // Check localStorage first
        let apiKey = localStorage.getItem('apiKey');

        // If not found, prompt the user
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }

        return apiKey || '';
      }
    });
  </script>
</body>

</html>