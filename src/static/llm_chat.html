<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>LLM Chat API Tester - With Image Support</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.95);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 30px;
    }

    h1 {
      text-align: center;
      color: #2d3748;
      margin-bottom: 30px;
      font-size: 2.5rem;
      font-weight: 700;
    }

    .api-section {
      background: #f8fafc;
      border-radius: 15px;
      padding: 25px;
      margin-bottom: 30px;
      border: 1px solid #e2e8f0;
    }

    .api-section h2 {
      color: #4a5568;
      margin-bottom: 20px;
      font-size: 1.5rem;
      font-weight: 600;
      display: flex;
      align-items: center;
    }

    .api-section h2::before {
      content: "🚀";
      margin-right: 10px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #4a5568;
    }

    input,
    textarea,
    select {
      width: 100%;
      padding: 12px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    input[type="file"] {
      padding: 8px;
      background: #f8fafc;
      border: 2px dashed #cbd5e0;
      cursor: pointer;
    }

    input[type="file"]:hover {
      border-color: #667eea;
      background: #edf2f7;
    }

    input:focus,
    textarea:focus,
    select:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    textarea {
      min-height: 120px;
      resize: vertical;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }

    .button {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-right: 10px;
    }

    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .clear-button {
      background: linear-gradient(135deg, #fc7a57 0%, #f093fb 100%);
    }

    .response-section {
      margin-top: 20px;
    }

    .response-box {
      background: #1a202c;
      color: #e2e8f0;
      padding: 20px;
      border-radius: 10px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      max-height: 400px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #667eea;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 10px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .status-indicator {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .status-success {
      background-color: #48bb78;
    }

    .status-error {
      background-color: #f56565;
    }

    .status-loading {
      background-color: #ed8936;
    }

    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    @media (max-width: 768px) {
      .grid {
        grid-template-columns: 1fr;
      }
    }

    .json-key {
      color: #e6db74;
    }

    .json-string {
      color: #a6e22e;
    }

    .json-number {
      color: #ae81ff;
    }

    .json-boolean {
      color: #fd971f;
    }

    .json-null {
      color: #f92672;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>🤖 LLM Chat API Tester 🖼️</h1>

    <div
      style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 10px; padding: 15px; margin-bottom: 20px; text-align: center;">
      <p style="margin: 0; color: #0c4a6e; font-weight: 500;">
        ✨ Now supports <strong>multimodal input</strong> - upload images along with your text messages!
        <br>
        <span style="font-size: 14px; opacity: 0.8;">Compatible with OpenAI's vision API format</span>
      </p>
    </div>

    <!-- API Key Section -->
    <div class="api-section">
      <h2>🔑 API Configuration</h2>
      <div class="form-group">
        <label for="apiKey">API Key:</label>
        <input type="password" id="apiKey" placeholder="Enter your API key">
      </div>
      <button class="button" onclick="saveApiKey()">Save API Key</button>
      <span id="apiKeyStatus"></span>
    </div>

    <div class="grid">
      <!-- Models Endpoint Section -->
      <div class="api-section">
        <h2>📋 Available Models</h2>
        <div class="form-group">
          <button class="button" onclick="testModelsEndpoint()">
            <span id="modelsLoading"></span>
            Get Available Models
          </button>
          <button class="button clear-button" onclick="clearModelsResponse()">Clear</button>
        </div>
        <div class="response-section">
          <label>Response from /api/llm_chat/models:</label>
          <div id="modelsResponse" class="response-box">Click "Get Available Models" to test the endpoint...</div>
        </div>
      </div>

      <!-- Chat Endpoint Section -->
      <div class="api-section">
        <h2>💬 Chat Completion</h2>
        <div class="form-group">
          <label for="chatModel">Model:</label>
          <div style="display: flex; gap: 10px; align-items: flex-end;">
            <select id="chatModel" style="flex: 1;">
              <option value="doubao-1-5-pro-32k-250115">doubao-1-5-pro-32k-250115</option>
              <option value="doubao-1-5-lite-32k-250115">doubao-1-5-lite-32k-250115</option>
              <option value="deepseek-v3-250324">deepseek-v3-250324</option>
              <option value="doubao-seed-1-6-250615">doubao-seed-1-6-250615</option>
              <option value="doubao-seed-1-6-thinking-250715">doubao-seed-1-6-thinking-250715</option>
              <option value="doubao-seed-1-6-flash-250615">doubao-seed-1-6-flash-250615</option>
            </select>
            <button class="button" onclick="refreshModels()" style="white-space: nowrap; padding: 12px 16px;">🔄
              Refresh</button>
          </div>
        </div>
        <div class="form-group">
          <label for="systemPrompt">System Prompt:</label>
          <textarea id="systemPrompt" placeholder="You are a helpful assistant.">You are a helpful assistant.</textarea>
        </div>
        <div class="form-group">
          <label for="userMessage">User Message:</label>
          <textarea id="userMessage" placeholder="Enter your message here...">Hello! How are you today?</textarea>
        </div>
        <div class="form-group">
          <label for="imageInput">Upload Image (optional):</label>
          <input type="file" id="imageInput" accept="image/*" onchange="handleImageUpload(this)">
          <div style="font-size: 12px; color: #666; margin-top: 4px;">
            Supports JPG, PNG, GIF, and other common image formats. Images are sent as base64 data URLs.
          </div>
          <div id="imagePreview" style="margin-top: 10px;"></div>
        </div>
        <div class="form-group">
          <label for="temperature">Temperature (0-2):</label>
          <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.7">
        </div>
        <div class="form-group">
          <label for="maxTokens">Max Tokens:</label>
          <input type="number" id="maxTokens" min="1" max="4000" value="1000">
        </div>
        <div class="form-group">
          <label>
            <input type="checkbox" id="streamMode"> Enable Streaming
          </label>
        </div>
        <div class="form-group">
          <button class="button" onclick="testChatEndpoint()">
            <span id="chatLoading"></span>
            Send Chat Request
          </button>
          <button class="button clear-button" onclick="clearChatResponse()">Clear</button>
        </div>
        <div class="response-section">
          <label>Response from /api/llm_chat:</label>
          <div id="chatResponse" class="response-box">Configure your message and click "Send Chat Request" to test...
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    let selectedImageData = null;

    // Load saved API key
    document.addEventListener('DOMContentLoaded', function () {
      const savedApiKey = localStorage.getItem('apiKey');
      if (savedApiKey) {
        document.getElementById('apiKey').value = savedApiKey;
        updateApiKeyStatus('✓ API Key loaded', 'success');

        // Try to load models automatically
        setTimeout(() => {
          refreshModels();
        }, 500);
      }
    });

    function handleImageUpload(input) {
      const file = input.files[0];
      const previewDiv = document.getElementById('imagePreview');

      if (file) {
        const reader = new FileReader();
        reader.onload = function (e) {
          selectedImageData = e.target.result;
          previewDiv.innerHTML = `
            <div style="border: 1px solid #e2e8f0; border-radius: 8px; padding: 10px; background: #f8fafc;">
              <img src="${e.target.result}" style="max-width: 200px; max-height: 200px; border-radius: 4px;" />
              <div style="margin-top: 8px; font-size: 12px; color: #666;">
                ${file.name} (${(file.size / 1024).toFixed(1)} KB)
                <button onclick="clearImage()" style="margin-left: 10px; padding: 2px 8px; font-size: 11px; background: #f56565; color: white; border: none; border-radius: 4px; cursor: pointer;">Remove</button>
              </div>
            </div>
          `;
        };
        reader.readAsDataURL(file);
      } else {
        clearImage();
      }
    }

    function clearImage() {
      selectedImageData = null;
      document.getElementById('imageInput').value = '';
      document.getElementById('imagePreview').innerHTML = '';
    }

    function saveApiKey() {
      const apiKey = document.getElementById('apiKey').value.trim();
      if (apiKey) {
        localStorage.setItem('apiKey', apiKey);
        updateApiKeyStatus('✓ API Key saved', 'success');

        // Automatically try to load models when API key is saved
        refreshModels();
      } else {
        updateApiKeyStatus('✗ Please enter an API key', 'error');
      }
    }

    function updateApiKeyStatus(message, type) {
      const statusElement = document.getElementById('apiKeyStatus');
      statusElement.innerHTML = `<span class="status-indicator status-${type}"></span>${message}`;
    }

    function getApiKey() {
      const apiKey = document.getElementById('apiKey').value.trim();
      if (!apiKey) {
        alert('Please enter an API key first');
        return null;
      }
      return apiKey;
    }

    function populateChatModels(models) {
      const chatModelSelect = document.getElementById('chatModel');

      // Store current selection
      const currentSelection = chatModelSelect.value;

      // Clear existing options
      chatModelSelect.innerHTML = '';

      // Add models from API response
      if (Array.isArray(models)) {
        models.forEach(model => {
          const option = document.createElement('option');
          if (typeof model === 'string') {
            option.value = model;
            option.textContent = model;
          } else if (model.id) {
            option.value = model.id;
            option.textContent = model.id; // Use id for display name since API doesn't have name field
          }
          chatModelSelect.appendChild(option);
        });
      }

      // If no models were added, add default options
      if (chatModelSelect.options.length === 0) {
        const defaultModels = [
          'doubao-1-5-pro-32k-250115',
          'doubao-1-5-lite-32k-250115',
          'deepseek-v3-250324',
          'doubao-seed-1-6-250615',
          'doubao-seed-1-6-thinking-250715',
          'doubao-seed-1-6-flash-250615'
        ];

        defaultModels.forEach(model => {
          const option = document.createElement('option');
          option.value = model;
          option.textContent = model;
          chatModelSelect.appendChild(option);
        });
      }

      // Try to restore previous selection, otherwise select first option
      if (currentSelection && Array.from(chatModelSelect.options).some(opt => opt.value === currentSelection)) {
        chatModelSelect.value = currentSelection;
      } else if (chatModelSelect.options.length > 0) {
        chatModelSelect.selectedIndex = 0;
      }

      // Show visual feedback that models were loaded
      const chatSection = chatModelSelect.closest('.api-section');
      const modelLabel = chatSection.querySelector('label[for="chatModel"]');
      if (modelLabel && !modelLabel.textContent.includes('✓')) {
        modelLabel.textContent = 'Model: ✓ Updated from API';
        setTimeout(() => {
          modelLabel.textContent = 'Model:';
        }, 3000);
      }
    }

    async function refreshModels() {
      const apiKey = getApiKey();
      if (!apiKey) return;

      // Find the refresh button
      const button = document.querySelector('button[onclick="refreshModels()"]') || event?.target;
      const originalText = button ? button.innerHTML : '';

      if (button) {
        button.innerHTML = '⏳ Loading...';
        button.disabled = true;
      }

      try {
        const response = await fetch('/api/llm_chat/models', {
          method: 'GET',
          headers: {
            'X-API-KEY': apiKey,
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();
        console.log('Models API response:', data); // Debug logging

        if (response.ok && data && data.data) {
          populateChatModels(data.data);
          // Show success feedback
          if (button) {
            button.innerHTML = '✅ Updated';
            setTimeout(() => {
              button.innerHTML = originalText;
            }, 2000);
          }
        } else {
          console.error('Failed to fetch models:', data);
          throw new Error(`API returned ${response.status}: ${data.error || 'Unknown error'}`);
        }

      } catch (error) {
        console.error('Error fetching models:', error);
        // On error, ensure default models are available
        populateChatModels([]);
        if (button) {
          button.innerHTML = '❌ Failed';
          setTimeout(() => {
            button.innerHTML = originalText;
          }, 2000);
        }
      } finally {
        if (button) {
          button.disabled = false;
        }
      }
    }

    async function testModelsEndpoint() {
      const apiKey = getApiKey();
      if (!apiKey) return;

      const loadingElement = document.getElementById('modelsLoading');
      const responseElement = document.getElementById('modelsResponse');

      loadingElement.innerHTML = '<div class="loading"></div>';
      responseElement.textContent = 'Loading...';

      try {
        const response = await fetch('/api/llm_chat/models', {
          method: 'GET',
          headers: {
            'X-API-KEY': apiKey,
            'Content-Type': 'application/json'
          }
        });

        const data = await response.json();
        console.log('Test Models API response:', data); // Debug logging

        // Populate chat model dropdown if models are available
        if (response.ok && data && data.data) {
          populateChatModels(data.data);
        }

        responseElement.innerHTML = formatJSON({
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          body: data
        });

      } catch (error) {
        console.error('Error in testModelsEndpoint:', error);
        responseElement.innerHTML = formatJSON({
          error: error.message,
          note: "Failed to fetch models from /api/llm_chat/models endpoint"
        });

        // On error, ensure default models are available
        populateChatModels([]);
      }

      loadingElement.innerHTML = '';
    }

    async function testChatEndpoint() {
      const apiKey = getApiKey();
      if (!apiKey) return;

      const loadingElement = document.getElementById('chatLoading');
      const responseElement = document.getElementById('chatResponse');

      loadingElement.innerHTML = '<div class="loading"></div>';
      responseElement.textContent = 'Loading...';

      const systemPrompt = document.getElementById('systemPrompt').value.trim();
      const userMessage = document.getElementById('userMessage').value.trim();
      const model = document.getElementById('chatModel').value;
      const temperature = parseFloat(document.getElementById('temperature').value);
      const maxTokens = parseInt(document.getElementById('maxTokens').value);
      const streamMode = document.getElementById('streamMode').checked;

      if (!userMessage) {
        responseElement.textContent = 'Please enter a user message';
        loadingElement.innerHTML = '';
        return;
      }

      const messages = [];
      if (systemPrompt) {
        messages.push({ role: 'system', content: systemPrompt });
      }

      // Create user message with multimodal content if image is present
      if (selectedImageData) {
        messages.push({
          role: 'user',
          content: [
            {
              type: 'text',
              text: userMessage
            },
            {
              type: 'image_url',
              image_url: {
                url: selectedImageData,
                detail: 'auto'
              }
            }
          ]
        });
      } else {
        messages.push({ role: 'user', content: userMessage });
      }

      const requestBody = {
        messages: messages,
        model: model,
        temperature: temperature,
        max_tokens: maxTokens,
        stream: streamMode
      };

      try {
        // Try /api/llm_chat first, fallback to /api/chat
        let response;
        try {
          response = await fetch('/api/llm_chat', {
            method: 'POST',
            headers: {
              'X-API-KEY': apiKey,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });
        } catch (error) {
          // Fallback to existing /api/chat endpoint
          response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
              'X-API-KEY': apiKey,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody)
          });
        }

        if (streamMode && response.headers.get('content-type')?.includes('text/event-stream')) {
          await handleStreamingResponse(response, responseElement);
        } else {
          const data = await response.json();
          responseElement.innerHTML = formatJSON({
            status: response.status,
            statusText: response.statusText,
            endpoint: response.url.includes('llm_chat') ? '/api/llm_chat' : '/api/chat (fallback)',
            body: data
          });
        }

      } catch (error) {
        responseElement.innerHTML = formatJSON({
          error: error.message,
          note: "Tried both /api/llm_chat and /api/chat endpoints"
        });
      }

      loadingElement.innerHTML = '';
    }

    async function handleStreamingResponse(response, responseElement) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let fullResponse = '';
      let chunks = [];

      responseElement.innerHTML = '<div style="color: #48bb78;">🌊 Streaming response...</div>\n\n';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.substring(6);
              if (data.trim() === '[DONE]') continue;

              try {
                const parsed = JSON.parse(data);
                chunks.push(parsed);

                if (parsed.choices?.[0]?.delta?.content) {
                  fullResponse += parsed.choices[0].delta.content;
                }
              } catch (e) {
                console.warn('Failed to parse chunk:', data);
              }
            }
          }
        }

        responseElement.innerHTML = formatJSON({
          status: 'streaming_complete',
          fullResponse: fullResponse,
          totalChunks: chunks.length,
          sampleChunks: chunks.slice(0, 3)
        });

      } catch (error) {
        responseElement.innerHTML = formatJSON({
          error: 'Streaming error: ' + error.message
        });
      }
    }

    function clearModelsResponse() {
      document.getElementById('modelsResponse').textContent = 'Click "Get Available Models" to test the endpoint...';
    }

    function clearChatResponse() {
      document.getElementById('chatResponse').textContent = 'Configure your message and click "Send Chat Request" to test...';
    }

    function formatJSON(obj) {
      return syntaxHighlight(JSON.stringify(obj, null, 2));
    }

    function syntaxHighlight(json) {
      json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
      return json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g, function (match) {
        var cls = 'json-number';
        if (/^"/.test(match)) {
          if (/:$/.test(match)) {
            cls = 'json-key';
          } else {
            cls = 'json-string';
          }
        } else if (/true|false/.test(match)) {
          cls = 'json-boolean';
        } else if (/null/.test(match)) {
          cls = 'json-null';
        }
        return '<span class="' + cls + '">' + match + '</span>';
      });
    }
  </script>
</body>

</html>