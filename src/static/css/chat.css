* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

body {
  display: flex;
  height: 100vh;
  background-color: #f7f9fc;
  color: #333;
}

/* Bot Panel - Left Side */
.bot-panel {
  width: 25%;
  background-color: #2d3748;
  display: flex;
  flex-direction: column;
  color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.bot-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px 10px;
}

.bot-item {
  display: flex;
  align-items: center;
  padding: 14px;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.bot-item:hover {
  background-color: #3a4556;
  transform: translateX(2px);
}

.bot-item.active {
  background-color: #3a4556;
  border-left: 3px solid #38b2ac;
}

.bot-avatar {
  width: 42px;
  height: 42px;
  background-color: #38b2ac;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.bot-info {
  flex-grow: 1;
}

.bot-name {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 0.95rem;
}

.bot-description {
  font-size: 0.8em;
  color: #cbd5e0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.add-bot-btn {
  padding: 16px;
  text-align: center;
  background-color: #1a202c;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
}

.add-bot-btn:hover {
  background-color: #2d3748;
}

/* Chat Panel - Right Side */
.chat-panel {
  width: 75%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.chat-header {
  padding: 18px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
  z-index: 10;
}

.chat-header h2 {
  font-size: 1.2em;
  font-weight: 600;
  color: #2d3748;
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 25px;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  background-image: radial-gradient(circle at 25px 25px, rgba(0, 0, 0, 0.01) 2%, transparent 0%), radial-gradient(circle at 75px 75px, rgba(0, 0, 0, 0.01) 2%, transparent 0%);
  background-size: 100px 100px;
}

.message {
  max-width: 70%;
  margin-bottom: 20px;
  padding: 14px 18px;
  border-radius: 18px;
  position: relative;
  line-height: 1.5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.bot {
  align-self: flex-start;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-bottom-left-radius: 4px;
  padding: 16px 18px;
}

.message.user {
  align-self: flex-end;
  background-color: #38b2ac;
  border-bottom-right-radius: 4px;
}

.chat-input {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background-color: #fff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
}

.chat-input textarea {
  flex-grow: 1;
  padding: 12px 15px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  resize: none;
  height: 46px;
  margin-right: 12px;
  font-size: 0.95em;
  transition: border 0.2s, box-shadow 0.2s;
}

.chat-input textarea:focus {
  outline: none;
  border-color: #38b2ac;
  box-shadow: 0 0 0 2px rgba(56, 178, 172, 0.2);
}

.chat-input button {
  padding: 12px 22px;
  background-color: #38b2ac;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.chat-input button:hover {
  background-color: #319795;
  transform: translateY(-1px);
}

.chat-input button:active {
  transform: translateY(1px);
}

@keyframes pulseButton {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(0.97);
  }

  100% {
    transform: scale(1);
  }
}

.chat-input button.sending {
  background-color: #2c7a7b;
  animation: pulseButton 1.5s infinite;
  pointer-events: none;
}

/* Chat Toolbar Styles */
.chat-toolbar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #f8fafc;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
  gap: 10px;
}

/* Message timestamp - Improved contrast */
.message-timestamp {
  display: none;
  position: absolute;
  bottom: -18px;
  font-size: 0.8em;
  color: #4a5568;
  right: 10px;
  font-weight: 500;
}

/* Message toolbar and result panel styles */
.message-toolbar {
  margin-top: 8px;
  display: flex;
  gap: 8px;
  opacity: 0.6;
  transition: opacity 0.2s;
}

.message:hover .message-toolbar {
  opacity: 1;
}

.toolbar-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 4px 6px;
  border-radius: 4px;
  color: #4a5568;
  transition: all 0.2s;
}

.toolbar-btn:hover {
  background-color: #edf2f7;
  color: #2d3748;
}

.message-result-panel {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  background-color: #f8fafc;
  border-radius: 6px;
  margin-top: 4px;
}

.message-result-panel.active {
  max-height: 300px;
  border: 1px solid #e2e8f0;
  margin-top: 8px;
  overflow-y: auto;
}

.result-content {
  padding: 10px;
}

.result-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
}

.result-loading .typing-dots {
  margin: 0 auto;
}

.result-error {
  color: #e53e3e;
  padding: 10px;
  font-size: 0.9em;
}

/* Modal styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modalFade 0.3s ease-out;
}

@keyframes modalFade {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.modal-content {
  background-color: white;
  padding: 25px;
  border-radius: 10px;
  width: 80%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  animation: modalSlide 0.3s ease-out;
}

@keyframes modalSlide {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-content h2 {
  margin-bottom: 20px;
  color: #2d3748;
  font-weight: 600;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #4a5568;
  font-size: 0.95em;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95em;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #38b2ac;
  box-shadow: 0 0 0 2px rgba(56, 178, 172, 0.2);
}

.form-group textarea {
  height: 120px;
  resize: vertical;
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modal-buttons button {
  padding: 10px 18px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.modal-buttons .cancel {
  background-color: #edf2f7;
  color: #4a5568;
}

.modal-buttons .cancel:hover {
  background-color: #e2e8f0;
}

.modal-buttons .save {
  background-color: #38b2ac;
  color: white;
}

.modal-buttons .save:hover {
  background-color: #319795;
  transform: translateY(-1px);
}

/* Improve Persona Button */
#improvePersonaBtn {
  margin-top: 5px;
  background-color: #805ad5;
  color: white;
  border: none;
  width: 100%;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s;
  border-radius: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
}

#improvePersonaBtn:hover {
  background-color: #6b46c1;
  transform: translateY(-1px);
}

#improvePersonaBtn:disabled {
  background-color: #cbd5e0;
  cursor: not-allowed;
  transform: none;
}

/* Bot content styles */
.bot-content {
  overflow-x: auto;
}

.bot-content pre {
  background-color: #f8fafc;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  border: 1px solid #e2e8f0;
}

.bot-content code {
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 0.9em;
}

.bot-content img {
  max-width: 100%;
  border-radius: 6px;
  margin: 10px 0;
}

.bot-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
  border-radius: 6px;
  overflow: hidden;
}

.bot-content th,
.bot-content td {
  border: 1px solid #e2e8f0;
  padding: 10px;
}

.bot-content th {
  background-color: #f8fafc;
}

/* Typing Indicator Styles */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
}

.typing-dots {
  display: flex;
  align-items: center;
}

.typing-dots span {
  height: 8px;
  width: 8px;
  margin: 0 2px;
  background-color: #38b2ac;
  border-radius: 50%;
  display: inline-block;
  opacity: 0.7;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
  animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-dot {

  0%,
  80%,
  100% {
    transform: scale(0.7);
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Message timestamp */
.message-timestamp {
  position: absolute;
  bottom: -18px;
  font-size: 0.8em;
  color: #4a5568;
  right: 10px;
  font-weight: 500;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
  body {
    flex-direction: column;
  }

  .bot-panel {
    width: 100%;
    height: 60px;
    flex-direction: row;
    overflow-x: auto;
    overflow-y: hidden;
  }

  .bot-list {
    display: flex;
    flex-direction: row;
    padding: 10px;
    overflow-x: auto;
    flex-grow: 1;
  }

  .bot-item {
    margin-right: 10px;
    margin-bottom: 0;
    flex-shrink: 0;
  }

  .add-bot-btn,
  #apiKeyBtn {
    padding: 10px;
    flex-shrink: 0;
    width: auto;
  }

  .chat-panel {
    width: 100%;
    height: calc(100vh - 60px);
  }

  .message {
    max-width: 85%;
  }
}

/* Add these styles in the style section */
.grammar-header,
.translation-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.grammar-badge {
  background-color: #e53e3e;
  color: white;
  border-radius: 50%;
  min-width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8em;
  font-weight: bold;
  margin-right: 8px;
}

.grammar-errors {
  margin-bottom: 12px;
}

.grammar-error {
  background-color: #fff8f8;
  border-left: 3px solid red;
  padding: 8px;
  margin-bottom: 8px;
}

.error-text {
  color: red;
  font-size: 0.9em;
  margin-bottom: 4px;
}

.grammar-improved {
  background-color: #f0fff4;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid green;
}

.grammar-improved h5 {
  margin-top: 0;
  margin-bottom: 5px;
  color: green;
}

.grammar-improved p {
  color: green;
}

.grammar-warnings {
  margin-bottom: 12px;
}

.grammar-warning {
  background-color: #fff8f8;
  border-left: 3px solid orange;
  padding: 8px;
  margin-bottom: 8px;
}

.warning-text {
  color: orange;
  font-size: 0.9em;
}

.translated-text {
  background-color: #ebf8ff;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #3182ce;
}