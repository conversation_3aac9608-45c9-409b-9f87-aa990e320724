<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Multi-Voice TTS Test</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #f8f9fa;
      color: #343a40;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .container {
      max-width: 900px;
    }

    .header {
      margin: 40px 0;
      text-align: center;
    }

    .step {
      background-color: white;
      border-radius: 10px;
      padding: 30px;
      margin-bottom: 30px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .step:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
    }

    .step-number {
      display: inline-block;
      width: 40px;
      height: 40px;
      background-color: #007bff;
      color: white;
      border-radius: 50%;
      text-align: center;
      line-height: 40px;
      font-size: 1.5em;
      font-weight: bold;
      margin-right: 15px;
    }

    .step-title {
      display: inline-block;
      font-size: 1.8em;
      font-weight: 600;
      vertical-align: middle;
      margin-bottom: 20px;
    }

    #text-input {
      border: 2px solid #007bff;
      border-radius: 10px;
      padding: 15px;
      width: 100%;
      min-height: 150px;
      transition: all 0.3s ease;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      resize: vertical;
    }

    #text-input:focus {
      outline: none;
      border-color: #0056b3;
      box-shadow: 0 0 5px rgba(0, 86, 179, 0.3);
    }

    .voice-checkbox-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 15px;
      margin-top: 20px;
    }

    .voice-checkbox {
      background-color: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;
    }

    .voice-checkbox:hover {
      border-color: #007bff;
      background-color: #e7f3ff;
    }

    .voice-checkbox input[type="checkbox"]:checked+label {
      color: #007bff;
      font-weight: 600;
    }

    .voice-checkbox.filtered-out {
      display: none;
    }

    .spinner-border {
      width: 1rem;
      height: 1rem;
    }

    .status-badge {
      font-size: 0.9em;
      padding: 5px 10px;
    }

    .voice-result {
      background-color: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-left: 5px solid #28a745;
    }

    .voice-result.processing {
      border-left-color: #007bff;
    }

    .voice-result.failed {
      border-left-color: #dc3545;
    }

    .voice-result h5 {
      margin-bottom: 15px;
      color: #495057;
    }

    .progress-container {
      margin: 15px 0;
    }

    footer {
      margin: 50px 0 20px;
      text-align: center;
      color: #6c757d;
      font-size: 0.9em;
    }
  </style>
</head>

<body>
  <div class="container">
    <div class="header">
      <h1 class="display-4">Multi-Voice TTS Test</h1>
      <p class="lead text-muted">Generate speech with multiple voices simultaneously</p>
    </div>

    <div class="step">
      <span class="step-number">1</span>
      <span class="step-title">Enter Text & Select Voices</span>
      <p class="mb-4">Type your text and choose which voices to generate speech with.</p>

      <textarea id="text-input" placeholder="Enter your text here..."></textarea>

      <div class="mt-4">
        <label class="form-label">Select Voices to Test</label>

        <!-- Filter Section -->
        <div class="mb-3">
          <div class="row align-items-center">
            <div class="col-md-6">
              <div class="input-group">
                <span class="input-group-text">🔍</span>
                <input type="text" id="voice-filter" class="form-control"
                  placeholder="Filter voices by name or language...">
                <button id="clear-filter-btn" class="btn btn-outline-secondary" type="button">Clear</button>
              </div>
            </div>
            <div class="col-md-6">
              <div class="btn-group" role="group">
                <button id="select-all-btn" class="btn btn-outline-primary btn-sm">Select All</button>
                <button id="select-filtered-btn" class="btn btn-outline-info btn-sm">Select Filtered</button>
                <button id="deselect-all-btn" class="btn btn-outline-secondary btn-sm">Deselect All</button>
              </div>
            </div>
          </div>
        </div>

        <div id="voice-checkboxes" class="voice-checkbox-container">
          <!-- Voice checkboxes will be populated dynamically -->
        </div>
      </div>

      <div class="mt-4">
        <button id="generate-button" class="btn btn-success btn-lg" hidden>Generate All Voices</button>
        <button id="generate-sequential-button" class="btn btn-warning btn-lg ms-2">Generate One by One</button>
        <button id="clear-button" class="btn btn-outline-secondary btn-lg ms-2">Clear</button>
        <button id="download-all-button" class="btn btn-info btn-lg ms-2 d-none">Download All</button>
      </div>

      <div class="alert alert-danger mt-3 d-none" id="error-message"></div>
    </div>

    <div class="step">
      <span class="step-number">2</span>
      <span class="step-title">Generated Audio Results</span>
      <p class="mb-4">Audio results will appear below as they are generated.</p>

      <div id="overall-progress" class="d-none">
        <div class="progress mb-3" style="height: 15px;">
          <div id="overall-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated bg-success"
            style="width: 0%"></div>
        </div>
        <p class="text-center">
          <span id="progress-text">0 of 0 voices completed</span>
        </p>
      </div>

      <div id="results-container">
        <!-- Voice results will be populated here -->
      </div>
    </div>
  </div>

  <footer>
    <p>© 2025 Multi-Voice TTS Test | Powered by Gusto AI</p>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      // DOM Elements
      const textInput = document.getElementById('text-input');
      const voiceCheckboxes = document.getElementById('voice-checkboxes');
      const generateButton = document.getElementById('generate-button');
      const generateSequentialButton = document.getElementById('generate-sequential-button');
      const clearButton = document.getElementById('clear-button');
      const selectAllBtn = document.getElementById('select-all-btn');
      const selectFilteredBtn = document.getElementById('select-filtered-btn');
      const deselectAllBtn = document.getElementById('deselect-all-btn');
      const voiceFilter = document.getElementById('voice-filter');
      const clearFilterBtn = document.getElementById('clear-filter-btn');
      const errorMessage = document.getElementById('error-message');
      const overallProgress = document.getElementById('overall-progress');
      const overallProgressBar = document.getElementById('overall-progress-bar');
      const progressText = document.getElementById('progress-text');
      const resultsContainer = document.getElementById('results-container');
      const downloadAllButton = document.getElementById('download-all-button');

      let voices = [];
      let filteredVoices = [];
      let activeGenerations = new Map();
      let completedAudios = new Map(); // Store completed audio URLs with voice types
      let sequentialQueue = []; // Queue for sequential generation
      let isSequentialGenerating = false;

      // Event Listeners
      generateButton.addEventListener('click', generateMultipleVoices);
      generateSequentialButton.addEventListener('click', generateSequentialVoices);
      clearButton.addEventListener('click', clearAll);
      selectAllBtn.addEventListener('click', selectAllVoices);
      selectFilteredBtn.addEventListener('click', selectFilteredVoices);
      deselectAllBtn.addEventListener('click', deselectAllVoices);
      voiceFilter.addEventListener('input', filterVoices);
      clearFilterBtn.addEventListener('click', clearFilter);
      downloadAllButton.addEventListener('click', downloadAllAudios);

      // Fetch available voices and populate checkboxes
      function fetchVoices() {
        fetch('/api/tts_voices', {
          headers: {
            'X-API-KEY': getApiKey()
          }
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Failed to fetch voices');
            }
            return response.json();
          })
          .then(data => {
            voices = data.voices;
            filteredVoices = [...voices]; // Initialize filtered voices
            populateVoiceCheckboxes();
          })
          .catch(error => {
            console.error('Error fetching voices:', error);
            showError('Failed to load voices: ' + error.message);
          });
      }

      function populateVoiceCheckboxes() {
        voiceCheckboxes.innerHTML = '';
        voices.forEach((voice, index) => {
          const checkboxDiv = document.createElement('div');
          checkboxDiv.className = 'voice-checkbox';
          checkboxDiv.dataset.voiceType = voice.voice_type;

          const checkbox = document.createElement('input');
          checkbox.type = 'checkbox';
          checkbox.id = `voice-${index}`;
          checkbox.value = voice.voice_type;
          checkbox.className = 'form-check-input me-2';

          const label = document.createElement('label');
          label.htmlFor = `voice-${index}`;
          label.className = 'form-check-label';
          label.textContent = `${voice.name} (${voice.language})`;

          checkboxDiv.appendChild(checkbox);
          checkboxDiv.appendChild(label);
          voiceCheckboxes.appendChild(checkboxDiv);
        });

        // Apply current filter
        filterVoices();
      }

      function selectAllVoices() {
        const checkboxes = voiceCheckboxes.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = true);
      }

      function selectFilteredVoices() {
        const visibleCheckboxes = voiceCheckboxes.querySelectorAll('.voice-checkbox:not(.filtered-out) input[type="checkbox"]');
        visibleCheckboxes.forEach(checkbox => checkbox.checked = true);
      }

      function deselectAllVoices() {
        const checkboxes = voiceCheckboxes.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => checkbox.checked = false);
      }

      function filterVoices() {
        const filterText = voiceFilter.value.toLowerCase().trim();
        const voiceElements = voiceCheckboxes.querySelectorAll('.voice-checkbox');

        filteredVoices = [];

        voiceElements.forEach(element => {
          const voiceType = element.dataset.voiceType;
          const voice = voices.find(v => v.voice_type === voiceType);

          if (!voice) return;

          const matchesFilter = filterText === '' ||
            voice.name.toLowerCase().includes(filterText) ||
            voice.language.toLowerCase().includes(filterText) ||
            voice.voice_type.toLowerCase().includes(filterText);

          if (matchesFilter) {
            element.classList.remove('filtered-out');
            filteredVoices.push(voice);
          } else {
            element.classList.add('filtered-out');
          }
        });

        // Update select filtered button text
        selectFilteredBtn.textContent = `Select Filtered (${filteredVoices.length})`;
      }

      function clearFilter() {
        voiceFilter.value = '';
        filterVoices();
      }

      function generateMultipleVoices() {
        const text = textInput.value.trim();
        if (!text) {
          showError('Please enter some text for TTS processing');
          return;
        }

        const selectedVoices = getSelectedVoices();
        if (selectedVoices.length === 0) {
          showError('Please select at least one voice');
          return;
        }

        hideError();
        clearResults();

        generateButton.disabled = true;
        generateButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Generating...';

        overallProgress.classList.remove('d-none');
        updateOverallProgress(0, selectedVoices.length);

        // Generate speech for each selected voice
        selectedVoices.forEach(voice => {
          generateSingleVoice(text, voice);
        });
      }

      function generateSequentialVoices() {
        const text = textInput.value.trim();
        if (!text) {
          showError('Please enter some text for TTS processing');
          return;
        }

        const selectedVoices = getSelectedVoices();
        if (selectedVoices.length === 0) {
          showError('Please select at least one voice');
          return;
        }

        hideError();
        clearResults();

        // Disable both buttons during sequential generation
        generateButton.disabled = true;
        generateSequentialButton.disabled = true;
        generateSequentialButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Generating One by One...';

        overallProgress.classList.remove('d-none');
        updateOverallProgress(0, selectedVoices.length);

        // Set up sequential queue
        sequentialQueue = [...selectedVoices];
        isSequentialGenerating = true;

        // Start sequential generation
        processNextInQueue(text);
      }

      function processNextInQueue(text) {
        if (sequentialQueue.length === 0 || !isSequentialGenerating) {
          // All done
          isSequentialGenerating = false;
          generateButton.disabled = false;
          generateSequentialButton.disabled = false;
          generateSequentialButton.textContent = 'Generate One by One';

          if (completedAudios.size > 0) {
            downloadAllButton.classList.remove('d-none');
          }
          return;
        }

        const voice = sequentialQueue.shift();

        // Create result container for this voice
        const resultDiv = createVoiceResultDiv(voice);
        resultsContainer.appendChild(resultDiv);

        // Start TTS generation for this voice
        fetch('/api/tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({
            text: text,
            voice: voice.voice_type,
            speed: 1.0,
            stream: false
          })
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          })
          .then(data => {
            const taskId = data.task_id;
            updateVoiceStatus(voice.voice_type, 'processing', taskId);

            // Start polling for this voice
            pollVoiceStatusSequential(voice, taskId, text);
          })
          .catch(error => {
            updateVoiceStatus(voice.voice_type, 'failed', null, error.message);
            updateSequentialProgress();
            // Continue with next voice even if this one failed
            setTimeout(() => processNextInQueue(text), 1000);
          });
      }

      function pollVoiceStatusSequential(voice, taskId, text) {
        const interval = setInterval(() => {
          fetch(`/api/tts/${taskId}`, {
            headers: {
              'X-API-KEY': getApiKey()
            }
          })
            .then(response => {
              if (!response.ok) {
                throw new Error('Failed to get task status');
              }
              return response.json();
            })
            .then(data => {
              if (data.status === 'completed') {
                clearInterval(interval);
                updateVoiceStatus(voice.voice_type, 'completed');

                if (data.audio_url) {
                  const audioElement = document.querySelector(`#result-${voice.voice_type} audio`);
                  audioElement.src = data.audio_url;

                  // Enable download button for this voice
                  const downloadBtn = document.querySelector(`#result-${voice.voice_type} .download-single-btn`);
                  downloadBtn.dataset.voiceType = voice.voice_type;
                  downloadBtn.addEventListener('click', () => downloadSingleAudio(voice.voice_type));

                  // Store the completed audio for download
                  completedAudios.set(voice.voice_type, {
                    url: data.audio_url,
                    name: voice.name,
                    language: voice.language
                  });
                }

                updateSequentialProgress();

                // Continue with next voice after a small delay
                setTimeout(() => processNextInQueue(text), 500);
              } else if (data.status === 'failed') {
                clearInterval(interval);
                updateVoiceStatus(voice.voice_type, 'failed', null, 'TTS processing failed');
                updateSequentialProgress();

                // Continue with next voice after a small delay
                setTimeout(() => processNextInQueue(text), 500);
              }
            })
            .catch(error => {
              clearInterval(interval);
              updateVoiceStatus(voice.voice_type, 'failed', null, error.message);
              updateSequentialProgress();

              // Continue with next voice after a small delay
              setTimeout(() => processNextInQueue(text), 500);
            });
        }, 3000);
      }

      function updateSequentialProgress() {
        const totalVoices = getSelectedVoices().length;
        const remainingVoices = sequentialQueue.length;
        const completedVoices = totalVoices - remainingVoices;

        updateOverallProgress(completedVoices, totalVoices);
      }

      function getSelectedVoices() {
        const checkboxes = voiceCheckboxes.querySelectorAll('input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(checkbox => {
          const voiceType = checkbox.value;
          return voices.find(v => v.voice_type === voiceType);
        });
      }

      function generateSingleVoice(text, voice) {
        // Create result container for this voice
        const resultDiv = createVoiceResultDiv(voice);
        resultsContainer.appendChild(resultDiv);

        // Start TTS generation
        fetch('/api/tts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-API-KEY': getApiKey()
          },
          body: JSON.stringify({
            text: text,
            voice: voice.voice_type,
            speed: 1.0,
            stream: false
          })
        })
          .then(response => {
            if (!response.ok) {
              throw new Error('Network response was not ok');
            }
            return response.json();
          })
          .then(data => {
            const taskId = data.task_id;
            activeGenerations.set(voice.voice_type, taskId);
            updateVoiceStatus(voice.voice_type, 'processing', taskId);

            // Start polling for this voice
            pollVoiceStatus(voice, taskId);
          })
          .catch(error => {
            updateVoiceStatus(voice.voice_type, 'failed', null, error.message);
            checkAllCompleted();
          });
      }

      function createVoiceResultDiv(voice) {
        const div = document.createElement('div');
        div.className = 'voice-result processing';
        div.id = `result-${voice.voice_type}`;

        div.innerHTML = `
          <h5>${voice.name} (${voice.language})</h5>
          <div class="progress-container">
            <div class="progress" style="height: 8px;">
              <div class="progress-bar progress-bar-striped progress-bar-animated" 
                   style="width: 25%"></div>
            </div>
          </div>
          <p class="status-text">Initializing...</p>
          <div class="audio-container d-none">
            <audio controls style="width: 100%;"></audio>
            <div class="mt-2">
              <button class="btn btn-sm btn-primary download-single-btn" data-voice-type="">
                <i class="bi bi-download"></i> Download Audio
              </button>
            </div>
          </div>
        `;

        return div;
      }

      function updateVoiceStatus(voiceType, status, taskId = null, errorMsg = null) {
        const resultDiv = document.getElementById(`result-${voiceType}`);
        if (!resultDiv) return;

        const progressBar = resultDiv.querySelector('.progress-bar');
        const statusText = resultDiv.querySelector('.status-text');
        const audioContainer = resultDiv.querySelector('.audio-container');

        resultDiv.className = `voice-result ${status}`;

        switch (status) {
          case 'processing':
            progressBar.style.width = '50%';
            statusText.textContent = `Processing... (Task ID: ${taskId})`;
            break;
          case 'completed':
            progressBar.style.width = '100%';
            progressBar.classList.remove('progress-bar-animated');
            progressBar.classList.add('bg-success');
            statusText.textContent = 'Completed successfully!';
            audioContainer.classList.remove('d-none');
            break;
          case 'failed':
            progressBar.style.width = '100%';
            progressBar.classList.remove('progress-bar-animated');
            progressBar.classList.add('bg-danger');
            statusText.textContent = `Failed: ${errorMsg || 'Unknown error'}`;
            break;
        }
      }

      function pollVoiceStatus(voice, taskId) {
        const interval = setInterval(() => {
          fetch(`/api/tts/${taskId}`, {
            headers: {
              'X-API-KEY': getApiKey()
            }
          })
            .then(response => {
              if (!response.ok) {
                throw new Error('Failed to get task status');
              }
              return response.json();
            })
            .then(data => {
              if (data.status === 'completed') {
                clearInterval(interval);
                updateVoiceStatus(voice.voice_type, 'completed');

                if (data.audio_url) {
                  const audioElement = document.querySelector(`#result-${voice.voice_type} audio`);
                  audioElement.src = data.audio_url;

                  // Enable download button for this voice
                  const downloadBtn = document.querySelector(`#result-${voice.voice_type} .download-single-btn`);
                  downloadBtn.dataset.voiceType = voice.voice_type;
                  downloadBtn.addEventListener('click', () => downloadSingleAudio(voice.voice_type));

                  // Store the completed audio for download
                  completedAudios.set(voice.voice_type, {
                    url: data.audio_url,
                    name: voice.name,
                    language: voice.language
                  });
                }

                activeGenerations.delete(voice.voice_type);
                checkAllCompleted();
              } else if (data.status === 'failed') {
                clearInterval(interval);
                updateVoiceStatus(voice.voice_type, 'failed', null, 'TTS processing failed');
                activeGenerations.delete(voice.voice_type);
                checkAllCompleted();
              }
            })
            .catch(error => {
              clearInterval(interval);
              updateVoiceStatus(voice.voice_type, 'failed', null, error.message);
              activeGenerations.delete(voice.voice_type);
              checkAllCompleted();
            });
        }, 3000);
      }

      function checkAllCompleted() {
        // Don't update progress for sequential mode, it has its own progress tracking
        if (isSequentialGenerating) return;

        const totalVoices = getSelectedVoices().length;
        const completedVoices = totalVoices - activeGenerations.size;

        updateOverallProgress(completedVoices, totalVoices);

        if (activeGenerations.size === 0) {
          generateButton.disabled = false;
          generateButton.textContent = 'Generate All Voices';

          // Show download button if there are completed audios
          if (completedAudios.size > 0) {
            downloadAllButton.classList.remove('d-none');
          }
        }
      }

      function updateOverallProgress(completed, total) {
        if (total === 0) return;

        const percentage = (completed / total) * 100;
        overallProgressBar.style.width = percentage + '%';
        progressText.textContent = `${completed} of ${total} voices completed`;

        if (completed === total) {
          overallProgressBar.classList.remove('progress-bar-animated');
        }
      }

      function clearAll() {
        textInput.value = '';
        deselectAllVoices();
        clearResults();
        hideError();

        // Stop all active generations and sequential processing
        activeGenerations.clear();
        completedAudios.clear();
        sequentialQueue = [];
        isSequentialGenerating = false;

        generateButton.disabled = false;
        generateButton.textContent = 'Generate All Voices';
        generateSequentialButton.disabled = false;
        generateSequentialButton.textContent = 'Generate One by One';
        downloadAllButton.classList.add('d-none');
      }

      function clearResults() {
        resultsContainer.innerHTML = '';
        overallProgress.classList.add('d-none');
        overallProgressBar.style.width = '0%';
        overallProgressBar.classList.add('progress-bar-animated');
      }

      function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
      }

      function hideError() {
        errorMessage.classList.add('d-none');
      }

      function downloadAllAudios() {
        if (completedAudios.size === 0) {
          showError('No completed audios to download');
          return;
        }

        downloadAllButton.disabled = true;
        downloadAllButton.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Downloading...';

        // Download each audio file using the audio element
        completedAudios.forEach(async (audioData, voiceType) => {
          try {
            console.log("Downloading:", voiceType);

            // Get the audio element for this voice
            const audioElement = document.querySelector(`#result-${voiceType} audio`);
            if (!audioElement || !audioElement.src) {
              console.error(`Audio element not found for ${voiceType}`);
              return;
            }

            // Use audio element's currentSrc or src to download
            await downloadAudioFromElement(audioElement, voiceType, audioData);

            // Small delay between downloads to avoid browser issues
            await new Promise(resolve => setTimeout(resolve, 100));
          } catch (error) {
            console.error(`Failed to download ${voiceType}:`, error);
          }
        });

        // Reset button after a short delay
        setTimeout(() => {
          downloadAllButton.disabled = false;
          downloadAllButton.textContent = 'Download All';
        }, 1000);
      }

      function downloadSingleAudio(voiceType) {
        const audioData = completedAudios.get(voiceType);
        if (!audioData) {
          showError('Audio not available for download');
          return;
        }

        const downloadBtn = document.querySelector(`#result-${voiceType} .download-single-btn`);
        downloadBtn.disabled = true;
        downloadBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1"></span>Downloading...';

        try {
          // Get the audio element for this voice
          const audioElement = document.querySelector(`#result-${voiceType} audio`);
          if (!audioElement || !audioElement.src) {
            throw new Error('Audio element not found');
          }

          // Download using the audio element
          downloadAudioFromElement(audioElement, voiceType, audioData)
            .then(() => {
              // Reset button
              downloadBtn.disabled = false;
              downloadBtn.innerHTML = '<i class="bi bi-download"></i> Download Audio';
            })
            .catch(error => {
              console.error(`Failed to download ${voiceType}:`, error);
              showError(`Failed to download audio: ${error.message}`);

              // Reset button
              downloadBtn.disabled = false;
              downloadBtn.innerHTML = '<i class="bi bi-download"></i> Download Audio';
            });
        } catch (error) {
          console.error(`Failed to download ${voiceType}:`, error);
          showError(`Failed to download audio: ${error.message}`);

          // Reset button
          downloadBtn.disabled = false;
          downloadBtn.innerHTML = '<i class="bi bi-download"></i> Download Audio';
        }
      }

      // Helper function to download audio from an audio element
      async function downloadAudioFromElement(audioElement, voiceType, audioData) {
        return new Promise((resolve, reject) => {
          try {
            // Method 1: Try to use the audio element's blob URL directly if available
            const audioSrc = audioElement.currentSrc || audioElement.src;

            // Create download link directly from the audio source
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = audioSrc;

            // Create filename
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '');
            const cleanVoiceName = audioData.name ? audioData.name.replace(/[^a-zA-Z0-9]/g, '_') : voiceType;
            a.download = `${cleanVoiceName}_${timestamp}.mp3`;

            // Force download attribute
            a.setAttribute('download', a.download);
            a.target = '_blank';

            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            resolve();
          } catch (error) {
            // Fallback: Try canvas/audio conversion method
            try {
              convertAudioElementToBlob(audioElement)
                .then(blob => {
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.style.display = 'none';
                  a.href = url;

                  const timestamp = new Date().toISOString().slice(0, 19).replace(/[-:]/g, '');
                  const cleanVoiceName = audioData.name ? audioData.name.replace(/[^a-zA-Z0-9]/g, '_') : voiceType;
                  a.download = `${cleanVoiceName}_${timestamp}.mp3`;

                  document.body.appendChild(a);
                  a.click();

                  // Clean up
                  window.URL.revokeObjectURL(url);
                  document.body.removeChild(a);

                  resolve();
                })
                .catch(reject);
            } catch (fallbackError) {
              reject(fallbackError);
            }
          }
        });
      }

      // Fallback method to convert audio element to blob
      async function convertAudioElementToBlob(audioElement) {
        return new Promise((resolve, reject) => {
          try {
            // Create a canvas and audio context for conversion
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Simple fallback - just try to fetch the src again with no-cors mode
            const audioSrc = audioElement.currentSrc || audioElement.src;

            fetch(audioSrc, {
              mode: 'no-cors',
              credentials: 'omit'
            })
              .then(response => {
                if (response.type === 'opaque') {
                  // If we get an opaque response, we can't access the blob
                  // So we'll create a simple audio blob as fallback
                  throw new Error('Cannot access audio data due to CORS');
                }
                return response.blob();
              })
              .then(resolve)
              .catch(() => {
                // Last resort: create a minimal audio file reference
                reject(new Error('Unable to download audio - CORS restrictions apply'));
              });
          } catch (error) {
            reject(error);
          }
        });
      }

      function getApiKey() {
        let apiKey = localStorage.getItem('apiKey');
        if (!apiKey) {
          apiKey = prompt('Please enter your API key:');
          if (apiKey) {
            localStorage.setItem('apiKey', apiKey);
          }
        }
        return apiKey || '';
      }

      // Initial setup
      fetchVoices();
    });
  </script>
</body>

</html>