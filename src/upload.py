# -*- coding: utf-8 -*-
import uuid
import oss2
import os
import logging
from dotenv import load_dotenv
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


ALIYUN_OSS_ACCESS_KEY_ID = os.getenv("ALIYUN_OSS_ACCESS_KEY_ID", "")
ALIYUN_OSS_ACCESS_KEY_SECRET = os.getenv(
    "ALIYUN_OSS_ACCESS_KEY_SECRET", "")
ALIYUN_OSS_ENDPOINT = os.getenv(
    "ALIYUN_OSS_ENDPOINT", "https://oss-cn-beijing.aliyuncs.com")
ALIYUN_OSS_BUCKET_NAME = os.getenv(
    "ALIYUN_OSS_BUCKET_NAME", "gusto-ai-base-service")
ALIYUN_OSS_REGION = os.getenv("ALIYUN_OSS_REGION", "cn-beijing")
ALIYUN_OSS_BUCKET_URL = os.getenv(
    "ALIYUN_OSS_BUCKET_URL", "https://gusto-ai-base-oss.wemore.com")

if not ALIYUN_OSS_ACCESS_KEY_ID or not ALIYUN_OSS_ACCESS_KEY_SECRET:
    raise ValueError("Aliyun OSS Access Key ID and Secret are required.")

auth = oss2.AuthV2(
    ALIYUN_OSS_ACCESS_KEY_ID, ALIYUN_OSS_ACCESS_KEY_SECRET)

# 生成唯一的Bucket名称
bucket = oss2.Bucket(auth, ALIYUN_OSS_ENDPOINT, ALIYUN_OSS_BUCKET_NAME)


def upload_file(bucket, object_name, data):
    try:
        result = bucket.put_object(object_name, data)
        logging.info(
            f"File uploaded successfully, status code: {result.status}")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to upload file: {e}")
        raise


def upload_bytes(data: bytes, object_name: str) -> str:
    try:
        result = bucket.put_object(object_name, data)
        logging.info(
            f"File uploaded successfully, status code: {result.status}")
        return f"{ALIYUN_OSS_BUCKET_URL}/{object_name}"
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to upload file: {e}")
        raise


def upload(local_file_path: str, object_name: str = None) -> str:
    """
    Uploads a local file to Aliyun OSS.
    :param local_file_path: The path to the local file to upload.
    :return: remote_file_url: The URL of the uploaded file in Aliyun OSS.
    :raises: Exception if the upload fails.
    """
    # Create a Bucket object
    if not object_name:
        object_name = uuid.uuid4().hex + os.path.splitext(
            os.path.basename(local_file_path))[1]  # Generate a unique object name

    # Upload the file
    # For large files, consider using resumable_upload or multipart_upload
    try:
        with open(local_file_path, 'rb') as f:
            file_data = f.read()
        upload_file(bucket, object_name, file_data)
    except IOError as e:
        logging.error(f"Failed to read file {local_file_path}: {e}")
        raise
    except Exception as e:
        logging.error(f"Failed to upload file: {e}")
        raise

    return f"{ALIYUN_OSS_BUCKET_URL}/{object_name}"


def delete_uploaded_file(uploaded_url: str):
    """
    Deletes a file from Aliyun OSS.
    :param uploaded_url: The URL of the uploaded file to delete.
    :raises: Exception if the deletion fails.
    """
    object_name = os.path.split(
        uploaded_url)[-1]  # Extract the object name from the URL
    try:
        bucket.delete_object(object_name)
        logging.info(f"File {object_name} deleted successfully.")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to delete file: {e}")
        raise


if __name__ == '__main__':
    filename = '/Users/<USER>/dev/codes/work/ai/gusto-ai-base-service/data/audios/hello,world.mp3'
    # filename = '/Users/<USER>/Downloads/20250703.mp3'
    # filename = '/Users/<USER>/Downloads/56e83a8c-4de9-47e7-af54-4ceb05b4ba58.mp3'
    try:
        with open(filename, 'rb') as f:
            # remote_url = upload(filename)
            # delete_uploaded_file(remote_url)
            remote_url = upload_bytes(f.read(), f"{uuid.uuid4().hex}.mp3")
            logging.info(
                f"File uploaded successfully. Remote URL: {remote_url}")
    except Exception as e:
        import traceback
        traceback.print_exc()
        logging.error(f"Error uploading file: {str(e)}")
