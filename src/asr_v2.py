# ASR v2 Implementation - Direct Recognition API
# doc: https://www.volcengine.com/docs/6561/1631584
import base64
import json
import os
import uuid
import requests
from dotenv import load_dotenv

from src.utils.file_utils import check_if_valid_url

load_dotenv()

appid = os.getenv("DOUBAO_STT_APP_ID")
token = os.getenv("DOUBAO_STT_ACCESS_TOKEN")


def recognize_flash(file_url: str = None, file_data: bytes = None):
    """
    Direct ASR recognition using the flash API.

    Args:
        file_url (str, optional): The URL of the audio file to be processed.
        file_data (bytes, optional): The binary data of the audio file.

    Returns:
        dict: The recognition result with audio info and transcription.

    Raises:
        ValueError: If neither file_url nor file_data is provided.
        Exception: If the API request fails.
    """
    if not file_url and not file_data:
        raise ValueError("Either file_url or file_data must be provided.")

    if file_url:
        check_if_valid_url(file_url)

    recognize_url = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/recognize/flash"

    request_id = str(uuid.uuid4())

    headers = {
        "X-Api-App-Key": appid,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": "volc.bigasr.auc_turbo",
        "X-Api-Request-Id": request_id,
        "X-Api-Sequence": "-1"
    }

    # Prepare audio data
    if file_url:
        audio_data = {"url": file_url}
    else:
        # Convert binary data to base64
        base64_data = base64.b64encode(file_data).decode('utf-8')
        audio_data = {"data": base64_data}

    request_body = {
        "user": {
            "uid": appid
        },
        "audio": audio_data,
        "request": {
            "model_name": "bigmodel",
            # Optional parameters that can be enabled as needed
            # "enable_itn": True,
            # "enable_punc": True,
            # "enable_ddc": True,
            # "enable_speaker_info": False,
        }
    }

    print(f'ASR v2 Flash Recognition - Request ID: {request_id}')

    response = requests.post(
        recognize_url,
        json=request_body,
        headers=headers,
        timeout=300  # 5 minutes timeout for large files
    )

    # Check response headers
    if 'X-Api-Status-Code' in response.headers:
        status_code = response.headers["X-Api-Status-Code"]
        status_message = response.headers.get("X-Api-Message", "")
        x_tt_logid = response.headers.get("X-Tt-Logid", "")

        print(f'ASR v2 response header X-Api-Status-Code: {status_code}')
        print(f'ASR v2 response header X-Api-Message: {status_message}')
        print(f'ASR v2 response header X-Tt-Logid: {x_tt_logid}')

        if status_code == "20000000":  # Success
            return response.json()
        elif status_code == "20000003":  # Silent audio
            return {
                "audio_info": {"duration": 0},
                "result": {
                    "text": "",
                    "additions": {"duration": "0"},
                    "utterances": []
                }
            }
        else:
            # Handle API errors
            error_msg = f'ASR v2 recognition failed with status code: {status_code}, message: {status_message}'
            print(error_msg)
            raise ValueError(
                f'{error_msg}, headers: {response.headers}, response: {response.text}')
    else:
        error_msg = 'ASR v2 recognition failed - missing status code in response headers'
        print(f'{error_msg}: {response.headers}')
        raise ValueError(
            f'{error_msg}, headers: {response.headers}, response: {response.text}')


def format_srt_time(seconds):
    """
    Format seconds into SRT time format (HH:MM:SS,mmm).
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds - int(seconds)) * 1000)
    return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"


def convert_to_srt(json_data):
    """
    Convert the JSON response from the ASR v2 API to SRT format.

    Args:
        json_data (dict): The result portion of the ASR response

    Returns:
        str: SRT formatted string
    """
    srt_output = []
    utterances = json_data.get('utterances', [])

    for i, item in enumerate(utterances):
        start_time = item['start_time'] / 1000  # Convert from ms to seconds
        end_time = item['end_time'] / 1000      # Convert from ms to seconds
        text = item['text'].strip()

        # Format SRT entry
        srt_entry = f"{i + 1}\n"
        srt_entry += f"{format_srt_time(start_time)} --> {format_srt_time(end_time)}\n"
        srt_entry += f"{text}\n\n"

        srt_output.append(srt_entry)

    return ''.join(srt_output)


def test_helper_v2(file: str = None, file_url: str = None, srt: bool = False):
    """
    Helper function to test the ASR v2 flash recognition.

    Args:
        file (str, optional): Path to local audio file
        file_url (str, optional): URL to audio file
        srt (bool): Whether to generate SRT format output
    """
    if not file and not file_url:
        raise ValueError("Either file or file_url must be provided.")

    file_data = None
    if file:
        # Read local file
        with open(file, 'rb') as f:
            file_data = f.read()
        print(f"Using local file: {file}")
    else:
        print(f"Using file URL: {file_url}")

    try:
        # Call the flash recognition API
        result = recognize_flash(file_url=file_url, file_data=file_data)
        print("ASR v2 Recognition SUCCESS!")
        print("Result:", json.dumps(result, indent=2, ensure_ascii=False))

        if srt and result.get('result', {}).get('utterances'):
            srt_results = convert_to_srt(result['result'])
            print("SRT Results:\n", srt_results)

    except Exception as e:
        print(f"ASR v2 Recognition FAILED: {str(e)}")
        raise


def main():
    """Test the ASR v2 implementation"""
    files = [
        'data/audios/hello,world.mp3',
        # 'data/audios/hello,world.m4a',
        # 'data/audios/hello,world.wav',
        # 'https://gusto-ai-base-oss-test.wemore.com/b2679a714c26452b939f656b0a58f774.mp3',
    ]

    for file in files:
        if os.path.exists(file):
            print(f"\nTesting ASR v2 with file: {file}")
            test_helper_v2(file=file, srt=True)
        else:
            print(f"File not found: {file}")


if __name__ == '__main__':
    # Uncomment to test
    main()
