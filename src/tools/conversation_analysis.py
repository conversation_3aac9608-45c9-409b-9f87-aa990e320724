import asyncio
from typing import Any, List, Dict, Optional
import json
from src.chat import STRONG_MODEL, generate_response


async def analyze_conversation(messages: List[dict], grammar_results: Optional[Any], pronunciation_results: Optional[Any]) -> dict:
    """
    Analyze a conversation to provide comprehensive feedback including
    English level assessment, strengths, weaknesses, and improvement suggestions.
    """

    # Filter out system messages and format conversation
    user_messages = [msg for msg in messages if msg.get("role") == "user"]
    bot_messages = [msg for msg in messages if msg.get("role") == "assistant"]

    if len(user_messages) == 0:
        return {
            "overall_summary": "No user messages found in the conversation.",
            "english_level": "Unable to assess",
            "english_level_score": 0,
            "grammar": [],
            "pronunciation": [],
            "topic_relevance": [],
            "feedback": "No user messages to analyze."
        }

    # Create conversation text for analysis
    conversation_text = ""
    for i, msg in enumerate(messages):
        if msg.get("role") == "user":
            conversation_text += f"User: {msg.get('content', '')}\n"
        elif msg.get("role") == "assistant":
            conversation_text += f"Assistant: {msg.get('content', '')}\n"

    analysis_prompt = f"""
    Please analyze the following conversation between a user learning English and an AI assistant. 
    Use a lot of emojis to improve the readability of the analysis.
    Provide a comprehensive analysis in JSON format with the following structure:

    {{
        "overall_summary": "A brief summary of the conversation content and flow",
        "english_level": "Beginner/Elementary/Intermediate/Upper-Intermediate/Advanced/Proficient",
        "english_level_score": 0-100,  
        "grammar": ["List of user's English strengths observed"],
        "pronunciation": ["List of user's pronunciation strengths observed"],
        "topic_relevance": ["List of relevant topics discussed"],
        "feedback": ["List of constructive feedback on grammar, vocabulary, and sentence structure"]
    }}

    Conversation to analyze:
    ``` 
    {conversation_text}
    ```
    
    Grammar results (if available):
    ```
    {json.dumps(grammar_results) if grammar_results else "No grammar results available."}
    ```
    
    Pronunciation results (if available):
    ```
    {json.dumps(pronunciation_results) if pronunciation_results else "No pronunciation results available."}
    ```

    Focus on:
    1. Grammar usage and accuracy
    2. Vocabulary range and appropriateness
    3. Sentence structure complexity
    4. Communication effectiveness
    5. Engagement and interaction quality
    6. Areas showing improvement potential
    7. Positive reinforcement for good usage

    Provide constructive, encouraging feedback that motivates continued learning.
    """

    try:
        # Use the existing chat generation function
        analysis_messages = [
            {
                "role": "system",
                "content": "You are an expert English language assessment specialist. Analyze conversations to provide detailed, constructive feedback on English proficiency. Always respond with valid JSON format."
            },
            {
                "role": "user",
                "content": analysis_prompt
            }
        ]

        response = await generate_response(
            analysis_messages, STRONG_MODEL, temperature=0.3)

        # Extract the analysis from the response
        analysis_text = response.get("choices", [{}])[0].get(
            "message", {}).get("content", "{}")

        print("Analysis Text:", analysis_text)  # Debugging output

        # Try to parse as JSON
        analysis_data = json.loads(analysis_text)

        # Validate required fields and provide defaults
        required_fields = {
            "overall_summary": "Conversation analysis completed.",
            "english_level": "Intermediate",
            "grammar": [],
            "pronunciation": [],
            "topic_relevance": [],
            "feedback": ["General feedback not provided."],
            "english_level_score": 75  # Default score if not provided
        }

        for field, default in required_fields.items():
            if field not in analysis_data:
                analysis_data[field] = default

        # Ensure english_level_score is an integer between 0 and 100
        if "english_level_score" not in analysis_data or not isinstance(analysis_data["english_level_score"], int):
            analysis_data["english_level_score"] = 75  # Default score
        elif analysis_data["english_level_score"] < 0 or analysis_data["english_level_score"] > 100:
            analysis_data["english_level_score"] = max(
                0, min(100, analysis_data["english_level_score"]))

        return analysis_data

    except Exception as e:
        import traceback
        traceback.print_exc()
        # Fallback response if analysis fails
        return {
            "overall_summary": "Unable to complete detailed analysis due to technical issues.",
            "english_level": "Assessment unavailable",
            "grammar": [],
            "pronunciation": [],
            "topic_relevance": [],
            "feedback": ["No specific feedback provided."]
        }
