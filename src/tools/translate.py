import xml.etree.ElementTree as ET
from typing import List, Dict, Any
from src.chat import WEAK_MODEL, generate_response


async def translate_text(text: str, before_text: str = "") -> str:
    """
    Translate the given text to the target language using a chat model.

    Args:
        text (str): The input text to be translated.
        target_language (str): The language code to translate the text into.

    Returns:
        str: The translated text.
    """
    PROMPT = f"""
    <instructions>
    Please translate the following text into English if it is Chinese, or translate it into Chinese if it is English. 
    Ignore any other languages and return original text.
    Ignore any other languages and return original text.
    Ignore any other languages and return original text.
    If the text is mixed with both Chinese and English, translate it to the other language.
    Ensure that the translation is accurate and maintains the original meaning. Do not add any additional information or context.
    Follow the format in the examples below and put the translation in the output xml section:
    </instructions>
    
    <examples>
      <example>
        <input>Bonjour le monde</input>
        <output> 
          <translation>Bonjour le monde</translation>
        </output>
      </example>
      <example>
        <input>Hello, world</input>
        <output> 
          <translation>你好，世界</translation>
        </output>
      </example>
      <example>
        <input>你好，世界</input>
        <output> 
          <translation>Hello, world</translation>
        </output>
      </example>
      <example>
        <input>你好，world</input>
        <output> 
          <translation>Hello, 世界</translation>
        </output>
      </example>
      <example>
        <input>Hello, 世界</input>
        <output> 
          <translation>你好, world</translation>
        </output>
      </example>
    </examples>
    
    <input>
    <before_text>
    {before_text}
    </before_text>
    {text}
    </input>
    """

    response = await generate_response(
        messages=[{"role": "user", "content": PROMPT}],
        model=WEAK_MODEL,
    )

    # Extract and return the content of the response
    result = response.get("choices", [{}])[0].get(
        "message", {}).get("content", "")

    if not result:
        raise ValueError("Translation failed or returned empty result.")

    return extract_suggestions_from_response(result)


def extract_suggestions_from_response(result: str) -> str:
    # parse xml response
    try:
        root = ET.fromstring(result)
        translation_element = root.find(".//translation")
        if translation_element is not None:
            return translation_element.text.strip()
        else:
            raise ValueError("Translation element not found in the response.")

    except ET.ParseError as e:
        raise ValueError(f"Failed to parse the response XML: {str(e)}")


if __name__ == "__main__":
    pass

    # Example usage - commented out since function is now async
    # import asyncio
    # text_to_translate = "这是一个测试文本。"
    # translated_text = await translate_text(text_to_translate)
    # print(f"Translated text: {translated_text}")

    # Example usage
    # text_to_translate = "He is a good student."
    # translated_text = await translate_text(text_to_translate)
    # print(f"Translated text: {translated_text}")

    # Russian example
    # text_to_translate = "Привет, мир"
    # translated_text = await translate_text(text_to_translate)
    # print(f"Translated text: {translated_text}")
