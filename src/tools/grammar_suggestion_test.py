import unittest
from unittest.mock import patch, MagicMock
from src.tools.grammar_suggestion import extract_suggestions_from_response, generate_grammar_suggestion
from src.models import GrammarSuggestions

# filepath: /Users/<USER>/dev/codes/work/ai/gusto-ai-base-service/src/tools/grammar_suggestion_test.py

import xml.etree.ElementTree as ET


class TestExtractSuggestionsFromResponse(unittest.TestCase):

    def test_extract_valid_xml_response(self):
        xml_response = """
    <output>
      <errors>
        <error>"go" should be "went".</error>
        <error>"stores" should be "store".</error>
      </errors>
      <improvements>
        <improvement>"the day before today" can be simplified to "yesterday".</improvement>
      </improvements>
      <suggested_text>She went to the store yesterday.</suggested_text>
    </output>
    """
        expected_result = GrammarSuggestions(
            errors=['"go" should be "went".', '"stores" should be "store".'],
            improvements=[
                '"the day before today" can be simplified to "yesterday".'],
            suggested_text="She went to the store yesterday."
        )

        result = extract_suggestions_from_response(xml_response)
        self.assertEqual(result.errors, expected_result.errors)
        self.assertEqual(result.improvements, expected_result.improvements)
        self.assertEqual(result.suggested_text, expected_result.suggested_text)

    def test_extract_without_errors(self):
        xml_response = """
    <output>
      <errors></errors>
      <improvements>
        <improvement>Consider restructuring the sentence for clarity.</improvement>
      </improvements>
      <suggested_text>The text is grammatically correct.</suggested_text>
    </output>
    """
        result = extract_suggestions_from_response(xml_response)
        self.assertEqual(result.errors, [])
        self.assertEqual(result.improvements, [
                         "Consider restructuring the sentence for clarity."])
        self.assertEqual(result.suggested_text,
                         "The text is grammatically correct.")

    def test_extract_without_improvements(self):
        xml_response = """
    <output>
      <errors>
        <error>Spelling error in "serval", should be "several".</error>
      </errors>
      <improvements></improvements>
      <suggested_text>Corrected text with fixed spelling.</suggested_text>
    </output>
    """
        result = extract_suggestions_from_response(xml_response)
        self.assertEqual(
            result.errors, ["Spelling error in \"serval\", should be \"several\"."])
        self.assertEqual(result.improvements, [])
        self.assertEqual(result.suggested_text,
                         "Corrected text with fixed spelling.")

    def test_extract_without_suggested_text(self):
        xml_response = """
    <output>
      <errors>
        <error>Grammar error identified.</error>
      </errors>
      <improvements>
        <improvement>Style improvement suggested.</improvement>
      </improvements>
    </output>
    """
        result = extract_suggestions_from_response(xml_response)
        self.assertEqual(result.errors, ["Grammar error identified."])
        self.assertEqual(result.improvements, ["Style improvement suggested."])
        self.assertEqual(result.suggested_text, "")

    def test_invalid_xml_raises_error(self):
        invalid_xml = "<output><errors>Incomplete XML"
        with self.assertRaises(ValueError) as context:
            extract_suggestions_from_response(invalid_xml)
        self.assertTrue(
            "Failed to parse the response XML" in str(context.exception))


if __name__ == "__main__":
    unittest.main()
