
import xml.etree.ElementTree as ET
from src.chat import STRONG_MODEL, WEAK_MODEL, generate_response
from src.models import GrammarSuggestions


async def generate_grammar_suggestion(text: str) -> GrammarSuggestions:
    """
    Generate grammar suggestions for the given text.

    Args:
        text (str): The input text for which grammar suggestions are to be generated.

    Returns:
        str: A string containing grammar suggestions.
    """
    # Call the chat API to generate grammar suggestions

    PROMPT = f"""
    <instructions>
    Please provide grammar suggestions for the following text. Focus on correcting misspelling words, correcting grammatical errors, improving sentence structure, and enhancing clarity. Do not change the meaning of the text. Just focus on grammar and clarity improvements.
    Follow the format in the examples below and put the suggestions in the output xml section:
    </instructions>

    <examples>
      <example>
        <input>She go to the stores the day before today.</input>
        <output>
          <errors>
            <error>"go" should be "went".</error>
            <error>"stores" should be "store".</error>
          </errors>
          <improvements>
            <improvement>"the day before today" can be simplified to "yesterday".</improvement>
          </improvements>
          <suggested_text>She went to the store yesterday.</suggested_text>
        </output>
      </example>
      <example>
        <input>He is a good student.</input>
        <output>
          <errors></errors>
          <improvements></improvements>
          <suggested_text></suggested_text>
        </output>
      </example>
    </examples>
    <input>
    {text}
    </input>
    """
    response = await generate_response(
        messages=[
            {"role": "user", "content": PROMPT}],
        model=STRONG_MODEL,
    )
    # print(response)
    # Extract and return the content of the response

    result = response.get("choices", [{}])[0].get(
        "message", {}).get("content", "")
    if not result:
        raise ValueError(
            "No suggestions generated. Please check the input text.")

    print(f"Grammar suggestions result: {result}")
    return extract_suggestions_from_response(result)


async def generate_coloquial_grammar_suggestion(text: str) -> GrammarSuggestions:
    """
    Generate grammar suggestions for colloquial or informal text.

    Args:
        text (str): The input text for which grammar suggestions are to be generated.

    Returns:
        GrammarSuggestions: An object containing grammar suggestions.
    """
    # Call the chat API to generate grammar suggestions
    PROMPT = f"""
    <instructions>
    Please provide colloquial suggestions for the following colloquial or informal text. Focus on using more natural and idiomatic expressions, and enhancing the overall flow of the text. Do not change the meaning of the text. Just focus on colloquial improvements. Keep pronoun particles like "um", "uh", "like", etc. as they are, and do not remove them.
    Follow the format in the examples below and put the suggestions in the output xml section:
    </instructions>

    <examples>
      <example>
        <input>She go to the stores the day before today.</input>
        <output>
          <errors>
            <error>"go" should be "went".</error>
            <error>"stores" should be "store".</error>
          </errors>
          <improvements>
            <improvement>"the day before today" can be simplified to "yesterday".</improvement>
          </improvements>
          <suggested_text>She went to the store yesterday.</suggested_text>
        </output>
      </example>
      <example>
        <input>He is a good student.</input>
        <output>
          <errors></errors>
          <improvements></improvements>
          <suggested_text></suggested_text>
        </output>
      </example>
      <example>
        <input>Um, I have got you</input>
        <output>
          <errors></errors>
          <improvements>
            <improvement>"have got you" can be replaced with "gotcha"</improvement>
          </improvements>
          <suggested_text>Um, I gotcha.</suggested_text>
        </output>
      </example>
      <example>
        <input>He won't do it.</input>
        <output>
          <errors></errors>
          <improvements>
            <improvement>"won't" can be replaced with "ain't gonna"</improvement>
          </improvements>
          <suggested_text>He ain't gonna do it.</suggested_text>
        </output>
      </example>
        <example>
          <input>I would like to order a plate of pasta and a glass of water, please.</input>
          <output>
            <errors>
            </errors>
            <improvements>
              <improvement>Replace "I would like to order" with "Can I get" for a more conversational tone.</improvement>
              <improvement>Simplify "a plate of pasta" to "some pasta" to sound natural.</improvement>
              <improvement>Omit "please" in informal settings to match casual speech.</improvement>
            </improvements>
            <suggested_text>Can I get some pasta and a glass of water?</suggested_text>
          </output>
        </example>
        <example>
          <input>Could you kindly inform me what day of the week it is?</input>
          <output>
            <errors>
            </errors>
            <improvements>
              <improvement>Replace "Could you kindly inform me" with "Hey, what" for a friendly, direct tone.</improvement>
              <improvement>Simplify "what day of the week it is" to "what day is it today" for brevity.</improvement>
            </improvements>
            <suggested_text>Hey, what day is it today?</suggested_text>
          </output>
        </example>

    </examples>
    <input>{text}</input>
    """

    response = await generate_response(
        messages=[
            {"role": "user", "content": PROMPT}],
        model=WEAK_MODEL,
    )

    result = response.get("choices", [{}])[0].get(
        "message", {}).get("content", "")

    if not result:
        raise ValueError(
            "No suggestions generated. Please check the input text.")

    print(f"Colloquial grammar suggestions result: {result}")
    return extract_suggestions_from_response(result)


def extract_suggestions_from_response(result: str) -> GrammarSuggestions:
    # parse xml response
    try:
        root = ET.fromstring(result)
        errors = [error.text for error in root.findall(".//errors/error")]
        improvements = [improvement.text for improvement in root.findall(
            ".//improvements/improvement")]
        suggested_text = root.find(
            ".//suggested_text").text if root.find(".//suggested_text") is not None else ""
        if suggested_text is None:
            suggested_text = ""

        # print(f"Extracted errors: {errors}")
        # print(f"Extracted improvements: {improvements}")
        # print(f"Extracted suggested text: {suggested_text}", root.find(
            # ".//suggested_text"))

        return GrammarSuggestions(errors=errors, improvements=improvements, suggested_text=suggested_text)

    except ET.ParseError as e:
        raise ValueError(f"Failed to parse the response XML: {str(e)}")


if __name__ == "__main__":
    pass
    # Example usage
    # import asyncio
    # input_text = """
    # I think private cars can be really useful because we can go to anywhere with our family and friends in anytime. I had learned how to drive, but I hadn't bought any private cars yet. One major drawback of owning a private car in Shanghai is that you need to pay a lot of money to aution a car license plate except the car. It can be really stressful when you also rent an apartment. Another downside is that to maintain a private car is high every month, such as gasoline, parking fee etc. Not to mention to the cost of general maintenance every serval months."""
    # input_text = "hi"
    # suggestions = await generate_grammar_suggestion(input_text)
    # print("Grammar Suggestions:")
    # print(suggestions)
