import asyncio
import base64
import os
import re
import tempfile

from pydub import AudioSegment
from src.utils.tts import generate_speech
from src.utils.language_utils import is_chinese, is_english


def split_sentences(text: str) -> list:
    """
    Split text into sentences using punctuation.
    """
    sentences = re.split(r'(?<=[。！？.!?])\s*', text.strip())
    return [s for s in sentences if s]


async def text_to_speech(text: str, voice: str = "zh_female_shuangkuaisisi_emo_v2_mars_bigtts", speed: float = 1.0, audio_file_path: str = None) -> str:
    """
    Convert text to speech using a chat model.
    If text exceeds 1000 characters, split it into sentences, generate audio for each, and merge them.

    Args:
        text (str): The input text to be converted to speech.
        voice (str): The voice model to use for the speech synthesis.
        speed (float): The speed of the speech synthesis (default is 1.0).
        audio_file_path (str): Optional path to save the generated audio file. If not provided,
                              a temporary file will be created and cleaned up automatically.

    Returns:
        str: The base64 encoded audio content.
    """
    if not is_chinese(text) and not is_english(text):
        raise ValueError("Input text must be either Chinese or English.")

    # Trim the text first
    text = text.strip()
    if not text:
        raise ValueError("Input text cannot be empty.")

    # Determine the final output path.
    final_output_path = audio_file_path.strip() if audio_file_path else None

    try:
        if len(text) > 1000:
            sentences = split_sentences(text)
            if not sentences:
                raise ValueError("No valid sentences found in the input text.")

            audio_segments = []
            temp_files = []

            # Synthesize each sentence separately.
            for sentence in sentences:
                if len(sentence) > 1000:
                    raise ValueError(
                        "One of the sentences exceeds the maximum length of 1000 characters.")
                # Create a temporary file for each sentence
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    sentence_output = temp_file.name
                temp_files.append(sentence_output)

                print(
                    f"Generating speech for sentence: {sentence} using voice: {voice} to {sentence_output}")
                if not sentence_output.endswith(".mp3"):
                    raise ValueError("Audio file path must end with .mp3")
                await generate_speech(speaker=voice, text=sentence, speed_ratio=speed, output_path=sentence_output)

                # Verify non-empty audio file
                with open(sentence_output, "rb") as audio_file:
                    if not audio_file.read():
                        raise ValueError(
                            "Failed to generate speech. One of the audio files is empty.")

                # Load the audio segment using pydub
                segment = AudioSegment.from_file(sentence_output, format="mp3")
                audio_segments.append(segment)

            # Merge all segments
            merged = audio_segments[0]
            for segment in audio_segments[1:]:
                merged += segment

            # If no output path was specified, use a temporary file.
            if final_output_path is None:
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    final_output_path = temp_file.name

            # Export the merged audio to the final output path.
            merged.export(final_output_path, format="mp3")

            # Encode merged audio
            with open(final_output_path, "rb") as audio_file:
                audio_content = audio_file.read()
                if not audio_content:
                    raise ValueError(
                        "Failed to generate speech. The merged audio file is empty.")
                result = base64.b64encode(audio_content).decode('utf-8')

            # Clean up temporary sentence files
            for file_path in temp_files:
                if os.path.exists(file_path):
                    os.remove(file_path)
            return result

        else:
            # Process normally for text under limit.
            if final_output_path is None:
                with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                    output_path = temp_file.name
            else:
                output_path = final_output_path

            print(
                f"Generating speech for text: {text} using voice: {voice} to {output_path}")
            if not output_path.endswith(".mp3"):
                raise ValueError("Audio file path must end with .mp3")
            await generate_speech(speaker=voice, speed_ratio=speed, text=text, output_path=output_path)

            with open(output_path, "rb") as audio_file:
                audio_content = audio_file.read()
                if not audio_content:
                    raise ValueError(
                        "Failed to generate speech. The audio file is empty.")
                return base64.b64encode(audio_content).decode('utf-8')
    except Exception as e:
        raise RuntimeError(f"语音合成失败: {str(e)}") from e
    finally:
        # If no audio_file_path was provided, clean up the final output file
        if audio_file_path is None and final_output_path and os.path.exists(final_output_path):
            os.remove(final_output_path)


def get_voices() -> list:
    """
    Get the list of available voice models for text-to-speech.

    Returns:
        list: A list of available voice models.
    """
    csv_path = 'data/doubao_voices.csv'
    if not os.path.exists(csv_path):
        raise RuntimeError(f"请下载语音列表文件到 {csv_path}")
    import pandas as pd
    return pd.read_csv(csv_path).fillna('').to_dict(orient='records')


if __name__ == "__main__":
    print("Available voices:")
    voices = get_voices()[:2]
    from json import dumps
    print(dumps(voices, ensure_ascii=False, indent=2))

    test_text = "Hello, this is a test of the text-to-speech system. 你好，这是一个语音合成系统的测试。"
    audio_base64 = asyncio.run(text_to_speech(
        text=test_text, voice=voices[0]['voice_type'], speed=1.2))
    print(
        f"Audio base64: {audio_base64[:50]}... (length: {len(audio_base64)})")
    with open("/tmp/output.mp3", "wb") as f:
        f.write(base64.b64decode(audio_base64))
    print("Audio saved to /tmp/output.mp3")
    print("Done.")
