
import json
from xml.etree import ElementTree as ET
from src.chat import generate_response_with_prompt, STRONG_MODEL, WEAK_MODEL, THINKING_MODEL

USING_MODEL = STRONG_MODEL


class Mission:
    """A class representing a mission with its details and status."""

    def __init__(self, title: str, description: str, objectives: list, steps: list):
        self.title = title
        self.description = description
        self.objectives = objectives
        self.steps = steps

    def to_dict(self) -> dict:
        """Convert the mission to a dictionary."""
        return {
            "title": self.title,
            "description": self.description,
            "objectives": self.objectives,
            "steps": self.steps
        }

    def to_json(self, **kwargs) -> str:
        """Convert the mission to a JSON string."""
        return json.dumps(self.to_dict(), **kwargs)

    def is_completed(self) -> bool:
        """Check if the mission is completed."""
        return all(step.get("status") == "completed" for step in self.steps)

    @staticmethod
    def from_dict(data: dict) -> 'Mission':
        """Create a Mission instance from a dictionary."""
        return Mission(
            title=data.get("title", ""),
            description=data.get("description", ""),
            objectives=data.get("objectives", []),
            steps=data.get("steps", [])
        )


async def create_missions(mission_details: str) -> Mission:
    """Create a new mission with the provided details.
    Args:
        mission_details (str): Details of the mission to be created.
    Returns:
        Mission: The created mission object.
    """

    prompt = f"""
<Instructions>
    You are a mission planner. Create a new mission based on the following details:
    ```
    {mission_details}
    ```
    Ensure the mission is well-defined, actionable, and includes all necessary steps.
    Return the mission in a structured JSON format as example below.
    Steps statuses should be set to "pending" unless specified otherwise.
    If the mission is already in progress, set the status to "in_progress".
    If the mission is completed, set the status to "completed".
    If the mission is cancelled, set the status to "cancelled".
    If the mission is failed, set the status to "failed".
    Use the following JSON structure
</Instructions>

<Example>
    {{
        "title": "Mission Title",
        "description": "Detailed description of the mission.",
        "objectives": [
            "Objective 1",
            "Objective 2"
        ],
        "steps": [
            {{
                "action": "Action to be taken in step 1",
                "status": "in_progress"
            }},
            {{
                "action": "Action to be taken in step 2",
                "status": "pending"
            }}
        ]
    }}
</Example>
    """

    response = await generate_response_with_prompt(
        prompt=prompt,
        model=USING_MODEL,
        temperature=0.3,
    )

    response_text = response.get("choices", [{}])[0].get(
        "message", {}).get("content", "")

    # print(f"Response Text: {response_text}")

    extracted_json = extract_mission_json(response_text)

    return Mission.from_dict(extracted_json)


def extract_mission_json(response_text: str) -> dict:
    """Extract JSON from the response text."""
    try:
        response_json = json.loads(response_text)
    except json.JSONDecodeError:
        try:
            root = ET.fromstring(response_text)
            mission_element = root.find(".//Mission")
            if mission_element is not None:
                response_json = json.loads(mission_element.text.strip())
            else:
                raise ValueError("Mission element not found in XML.")
        except ET.ParseError:
            raise ValueError("Response text is neither valid JSON nor XML.")

    return response_json


async def check_mission(mission: Mission, conversation_history: str) -> Mission:
    """Check the status of a mission based on conversation history.

    Args:
        mission (Mission): The mission to check.
        conversation_history (str): The conversation history to analyze.

    Returns:
        Mission: The updated mission with its status.
    """
    prompt = f"""
<instructions>
    You are a mission status checker. Analyze the conversation history to determine the status of the mission.
    The mission details are as follows:
    ```json
    {mission.to_json()}
    ```
    Base your analysis on the following conversation history:
    ```
    {conversation_history}
    ```
    If the mission is in progress, set the status to "in_progress".
    If the mission is completed, set the status to "completed".
    If the mission is failed, set the status to "failed".
    If the mission is still pending, set the status to "pending".
    User may not follow the mission steps, so you need to analyze the conversation history to determine the status.
    User may also do something that is failing the mission, so you need to analyze the conversation history to determine the status.
    First analyze the conversation history and then return the updated mission.
    The English level of the user may not be very high, so you should not too severe in your analysis, allow user to make mistakes or vague statements. Partially completed steps should be considered as "completed".
    Use the following example structure to return the updated mission. Put all the response in XML format and in output tag.
</instructions>
<example>
    <output>
        <thinking>[Your thoughts on the mission status based on the conversation history]</thinking>
        <result>{{
            "title": "Mission Title",
            "description": "Detailed description of the mission.",
            "objectives": [
                "Objective 1",
                "Objective 2"
            ],
            "steps": [
                {{
                    "action": "Action to be taken in step 1",
                    "status": "in_progress"
                }},
                {{
                    "action": "Action to be taken in step 2",
                    "status": "pending"
                }}
            ]
        }}
        </result>
    </output>
</example>
    """

    # print(f"Prompt: {prompt}")

    response = await generate_response_with_prompt(
        prompt=prompt,
        model=THINKING_MODEL,
        temperature=0.1,
    )
    response_text = response.get("choices", [{}])[0].get(
        "message", {}).get("content", "")
    print(f"Response Text: {response_text}")

    return Mission.from_dict(extract_mission_status(response_text))


def extract_mission_status(response_text: str) -> dict:
    """Extract mission status from the response text."""
    from xml.etree import ElementTree as ET
    import json
    try:
        # print(f"Response Text: {response_text}")
        root = ET.fromstring(response_text.strip())
        result_elem = root.find(".//result")
        if result_elem is None:
            raise ValueError("No result element found in XML.")
        mission_json_text = result_elem.text.strip()
        if not mission_json_text:
            raise ValueError("No JSON content found in result element.")
        return json.loads(mission_json_text)

    except (ET.ParseError, json.JSONDecodeError) as e:
        # If XML or JSON parsing fails, raise an error
        raise ValueError(f"Failed to parse response: {str(e)}")


if __name__ == "__main__":
    # Example usage
    #     mission_details = """
    # You are preparing for a job interview. Please create a mission plan about the interview process dialogue that includes the following:
    # - A polite greeting
    # - A brief introduction of your resume(简历)
    # - Your strengths and weaknesses
    # - Your goals in this position
    # - Your expected salary range
    # - Questions for the interviewer(e.g. working hours, job responsibilities, benefits, etc.)
    #     """
    #     mission_response = create_missions(mission_details)
    #     print(mission_response)

    mission_json_text = """
{
    "title": "Job Interview Process Mission",
    "description": "This mission is to guide you through a job interview process, including a polite greeting, resume introduction, sharing of strengths and weaknesses, stating goals for the position, mentioning expected salary range, and asking questions to the interviewer.",
    "objectives": [
        "Make a good first - impression with a polite greeting",
        "Effectively introduce your resume",
        "Honestly share your strengths and weaknesses",
        "Clearly state your goals for the position",
        "Mention your expected salary range",
        "Ask relevant questions to the interviewer"
    ],
    "steps": [
        {
            "action": "Enter the interview room and greet the interviewer politely, e.g., 'Good [time of day], sir/madam. Thank you for giving me this opportunity to interview for the position.'",
            "status": "pending"
        },
        {
            "action": "Briefly introduce your resume, highlighting key work experiences, educational background, and any relevant achievements. For example, 'I graduated from [University Name] with a degree in [Major]. I have [X] years of experience in [Field], working at [Company Names]. In my previous role at [Company], I successfully [mention an achievement].'",
            "status": "pending"
        },
        {
            "action": "Share your strengths and weaknesses. Say something like, 'One of my strengths is my strong problem - solving ability. I once [give an example of problem - solving]. My weakness is that I can be a bit perfectionist, which sometimes makes me spend more time on a task than necessary, but I'm working on improving my time management skills.'",
            "status": "pending"
        },
        {
            "action": "State your goals for the position. For instance, 'My goal in this position is to contribute to the company's growth by [mention how you plan to contribute, e.g., improving sales, optimizing processes]. In the short - term, I aim to learn the ropes quickly and make a positive impact in the team. In the long - term, I hope to take on more responsibilities and grow within the company.'",
            "status": "pending"
        },
        {
            "action": "Mention your expected salary range. You could say, 'Based on my experience and the industry standards, I'm expecting a salary in the range of [X] to [Y] per month.'",
            "status": "pending"
        },
        {
            "action": "Ask questions to the interviewer. For example, 'Could you tell me about the typical working hours for this position?', 'What are the main job responsibilities?', 'What kind of benefits does the company offer?'",
            "status": "pending"
        }
    ]
}"""
    mission = Mission.from_dict(json.loads(mission_json_text))

    check_mission_response = """
<output>
    <thinking>
        The candidate failed to follow the first step of the mission which is to greet the interviewer politely. Instead, the candidate started talking about their own job offer from Bytedance. So, the mission status should be set to "failed".
    </thinking>
    <result>
        {
            "title": "Job Interview Process Mission",
            "description": "This mission is to guide you through a job interview process, including a polite greeting, resume introduction, sharing of strengths and weaknesses, stating goals for the position, mentioning expected salary range, and asking questions to the interviewer.",
            "objectives": [
                "Make a good first - impression with a polite greeting",
                "Effectively introduce your resume",
                "Honestly share your strengths and weaknesses",
                "Clearly state your goals for the position",
                "Mention your expected salary range",
                "Ask relevant questions to the interviewer"
            ],
            "steps": [
                {
                    "action": "Enter the interview room and greet the interviewer politely, e.g., 'Good [time of day], sir/madam. Thank you for giving me this opportunity to interview for the position.'",
                    "status": "failed"
                },
                {
                    "action": "Briefly introduce your resume, highlighting key work experiences, educational background, and any relevant achievements. For example, 'I graduated from [University Name] with a degree in [Major]. I have [X] years of experience in [Field], working at [Company Names]. In my previous role at [Company], I successfully [mention an achievement].'",
                    "status": "pending"
                },
                {
                    "action": "Share your strengths and weaknesses. Say something like, 'One of my strengths is my strong problem - solving ability. I once [give an example of problem - solving]. My weakness is that I can be a bit perfectionist, which sometimes makes me spend more time on a task than necessary, but I'm working on improving my time management skills.'",
                    "status": "pending"
                },
                {
                    "action": "State your goals for the position. For instance, 'My goal in this position is to contribute to the company's growth by [mention how you plan to contribute, e.g., improving sales, optimizing processes]. In the short - term, I aim to learn the ropes quickly and make a positive impact in the team. In the long - term, I hope to take on more responsibilities and grow within the company.'",
                    "status": "pending"
                },
                {
                    "action": "Mention your expected salary range. You could say, 'Based on my experience and the industry standards, I'm expecting a salary in the range of [X] to [Y] per month.'",
                    "status": "pending"
                },
                {
                    "action": "Ask questions to the interviewer. For example, 'Could you tell me about the typical working hours for this position?', 'What are the main job responsibilities?', 'What kind of benefits does the company offer?'",
                    "status": "pending"
                }
            ]
        }
    </result>
</output>
    """
    print(extract_mission_status(check_mission_response))

    # conversation_history = """
    # Interviewer: Good morning, thank you for coming in today.
    # Candidate: Good morning, thank you for having me. I'm excited to be here.
    # Interviewer: Can you start by telling me a bit about your background?
    # Candidate: Sure, I graduated from XYZ University with a degree in Computer Science. I've worked at ABC Company for the past three years as a software developer.
    # Interviewer: That's great. What would you say are your strengths and weaknesses?
    # Candidate: One of my strengths is my problem-solving ability. I enjoy tackling complex challenges. As for weaknesses, I tend to be a perfectionist, which can slow me down sometimes.
    # Interviewer: Interesting. What are your goals for this position?
    # Candidate: My goal is to contribute to the team by improving our software development processes and delivering high-quality products.
    # Interviewer: And what are your salary expectations?
    # Candidate: Based on my experience, I'm looking for a salary in the range of $70,000 to $80,000 per year.
    # Interviewer: Do you have any questions for us?
    # Candidate: Yes, could you tell me more about the team I'll be working with and the company's culture?
    # """
    # conversation_history = """
    # Interviewer: Good morning, thank you for coming in today.
    # Candidate: Good morning, thank you for having me. I'm excited to be here.
    # """
    # conversation_history = """
    # Interviewer: Good morning, thank you for coming in today.
    # """
    # Test code - commented out since function is now async
    # import asyncio
    # conversation_history = """
    # Interviewer: Good morning, thank you for coming in today.
    # Candidate: Hey, guess what? I just got a job offer from Bytedance! I'm so excited! You should definitely apply there too, it's a great company!
    # """
    #
    # updated_mission = await check_mission(mission, conversation_history)
    # print(updated_mission.to_json(indent=4, ensure_ascii=False))
