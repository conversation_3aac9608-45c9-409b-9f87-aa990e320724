import asyncio
import json
import random
from src.chat import STRONG_MODEL, generate_response


async def generate_chat_suggestions(conversation_history, context=""):
    """
    Generate contextual chat suggestions based on conversation history
    """
    try:
        # Prepare the prompt for generating suggestions
        messages = []

        conversation_history = conversation_history[-2:]

        conversation_history_text = "\n".join(
            f"{'A' if msg['role'] == 'user' else 'B'}: {msg['content']}" for msg in conversation_history
        ) if conversation_history else ""

        user_prompt = f"""

<Instructions>
Based on the following English conversation, generate two appropriate responses:
- A simple and straightforward reply using basic vocabulary
- A natural and idiomatic response that a native speaker would use

Guidelines:
- Keep suggestions under 20 words each
- Make them conversational and natural
- Focus on continuing the discussion or exploring related topics
- Answer questions or provide relevant information
- Make them actionable and specific
- Follow the xml format of the examples below, without numbering or bullet points
- Always keep your response in English!
</Instructions>

<Examples>
    <Example>
        <Conversation>
        A: Hey, do you know if the meeting was rescheduled?
        B: I didn't get any updates about it.
        </Conversation>
        <Responses>
            <Simple>Maybe you should ask the manager.</Simple>
            <Idiomatic>You might wanna check with <PERSON> from HR, he's usually in the loop about these things.</Idiomatic>
        </Responses>
    </Example>
    <Example>
        <Conversation>
        A: Hey, do you know if the meeting was rescheduled?
        B: Wait, what meeting? 
        </Conversation>
        <Responses>
            <Simple>The one about the project update.</Simple>
            <Idiomatic>The project update meeting, you know, the one we were all supposed to attend last week?</Idiomatic>
        </Responses>
    </Example>
</Examples>

<Conversation>
{conversation_history_text}
</Conversation>
        """

        # Add conversation history (limit to last 6 messages to avoid token limits)
        messages.append(
            {"role": "user", "content": user_prompt})

        print("messages", json.dumps(messages, indent=2))

        # Generate suggestions using the existing chat function
        response = await generate_response(messages, STRONG_MODEL)
        # print("generate_chat_suggestions response:", response)

        # Parse the response to extract suggestions
        suggestions_text = response["choices"][0]["message"]["content"].strip()
        print("generate_chat_suggestions:", suggestions_text)
        # Xml to text conversion
        suggestions = extract_suggestions_from_response(suggestions_text)
        if not suggestions or len(suggestions) < 2:
            suggestions = [
                "Tell me more about that",
                "Can you explain further?"
            ]

        return suggestions

    except Exception as e:
        print(f"Error generating chat suggestions: {e}")
        # Return fallback suggestions
        return [
            "Tell me more",
            "Can you elaborate?"
        ]


def extract_suggestions_from_response(response_text: str):
    """
    Extracts simple and idiomatic suggestions from the response text.
    Expects the response to be in a specific XML format.
    """
    try:
        # Parse the XML response
        from xml.etree import ElementTree as ET
        root = ET.fromstring(response_text)
        simple = root.find(
            ".//Simple").text if root.find(".//Simple") is not None else "Tell me more"
        idiomatic = root.find(
            ".//Idiomatic").text if root.find(".//Idiomatic") is not None else "Can you elaborate?"
        if simple is None:
            simple = "Tell me more"
        if idiomatic is None:
            idiomatic = "Can you elaborate?"
        # Return as a list of suggestions
        return [simple, idiomatic]

    except json.JSONDecodeError as e:
        print(f"Failed to parse suggestions response: {e}")
        return ["Tell me more", "Can you elaborate?"]


if __name__ == "__main__":
    response = """
    <Responses>
    <Simple>We usually have ham for dinner.</Simple>
    <Idiomatic>We often go for ham as the centerpiece of our meal.</Idiomatic>
    </Responses>
    """
    print(extract_suggestions_from_response(response))
