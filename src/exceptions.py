

from src.models import ErrorResponse


class InvalidUrlError(Exception):
    """Custom exception for invalid URLs."""

    def __init__(self, message):
        super().__init__(message)


def generate_error_response(error: Exception) -> ErrorResponse:
    """ Generate a structured error response for OpenAI API errors.
    Args:
        error: The exception raised by the OpenAI API call
    Returns:
        An ErrorResponse object with the error details
    """

    return ErrorResponse(
        error=f"OpenAI API Error: {error}"
    )
