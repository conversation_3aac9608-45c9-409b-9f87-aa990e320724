from fastapi.templating import <PERSON><PERSON>2Templates
from fastapi.staticfiles import StaticFiles

from src.api import app, Request

# Mount the static files directory
app.mount("/static", StaticFiles(directory="src/static"), name="static")

# Set up Jinja2 templates
templates = Jinja2Templates(directory="src/static")


@app.get("/asr")
async def asr_page(request: Request):
    """
    Serve the beautiful ASR page for the 2-step ASR process
    """
    return templates.TemplateResponse("asr.html", {"request": request})


@app.get("/chat")
async def chat_page(request: Request):
    """
    Serve the beautiful CHAT page
    """
    return templates.TemplateResponse("chat.html", {"request": request})


@app.get("/health")
async def health_page(request: Request):
    """
    Serve the health check page
    """
    return templates.TemplateResponse("health.html", {"request": request})


@app.get("/pronunciation_assessment")
async def pronunciation_assessment_page(request: Request):
    """
    Serve the pronunciation assessment page
    """
    return templates.TemplateResponse("pronunciation_assessment.html", {"request": request})


@app.get("/grammar_suggestion")
async def grammar_suggestion_page(request: Request):
    """
    Serve the grammar suggestion page
    """
    return templates.TemplateResponse("grammar_suggestion.html", {"request": request})


@app.get("/mission")
async def mission_page(request: Request):
    """
    Serve the mission manager page
    """
    return templates.TemplateResponse("mission.html", {"request": request})


@app.get("/tts")
async def tts_page(request: Request):
    """
    Serve the TTS page for the 2-step TTS process
    """
    return templates.TemplateResponse("tts.html", {"request": request})


@app.get("/translate")
async def translate_page(request: Request):
    """
    Serve the translation page for the 2-step translation process
    """
    return templates.TemplateResponse("translate.html", {"request": request})


@app.get("/pronunciation_assessment_elsa")
async def pronunciation_assessment_elsa_page(request: Request):
    """
    Serve the Elsa pronunciation assessment page
    """
    return templates.TemplateResponse("pronunciation_assessment_elsa.html", {"request": request})


@app.get("/tts_websocket")
async def tts_websocket_page(request: Request):
    """
    Serve the TTS WebSocket test page
    """
    return templates.TemplateResponse("tts_websocket.html", {"request": request})


# upload page
@app.get("/upload")
async def upload_page(request: Request):
    """
    Serve the upload page for voice messages or images
    """
    return templates.TemplateResponse("upload.html", {"request": request})


@app.get("/llm_chat")
async def llm_chat_page(request: Request):
    """
    Serve the beautiful LLM chat page
    """
    return templates.TemplateResponse("llm_chat.html", {"request": request})


@app.get("/asr_v2")
async def asr_v2_page(request: Request):
    """
    Serve the beautiful ASR v2 page for instant transcription
    """
    return templates.TemplateResponse("asr_v2.html", {"request": request})


@app.get("/batch_tts")
async def batch_tts_page(request: Request):
    """
    Serve the batch TTS page for processing multiple text inputs
    """
    return templates.TemplateResponse("batch_tts.html", {"request": request})


@app.get("/tts_voices_test")
async def tts_voices_test_page(request: Request):
    """
    Serve the TTS voices test page for generating text with multiple voices
    """
    return templates.TemplateResponse("tts_voices_test.html", {"request": request})
