# doc: https://www.volcengine.com/docs/6561/1354868
import asyncio
import pydub
import json
import os
import tempfile
import time
import uuid
import requests
import aiohttp
from dotenv import load_dotenv

from src.upload import upload
from src.utils.audio_utils import change_audio_speed, convert_to_16kHz_mono_wav_audio
from src.utils.fc_utils import invoke_fc_async
from src.utils.file_utils import check_if_valid_url, detect_file_type
load_dotenv()

appid = os.getenv("DOUBAO_STT_APP_ID")
token = os.getenv("DOUBAO_STT_ACCESS_TOKEN")


def submit_task(file_url: str):
    """Submit a task to the ASR service.
    Args:
        file_url (str): The URL of the audio file to be processed.
    Returns:
        str: The task ID for the submitted task.
    """
    check_if_valid_url(file_url)

    submit_url = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/submit"

    task_id = str(uuid.uuid4())

    headers = {
        "X-Api-App-Key": appid,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": "volc.bigasr.auc",
        "X-Api-Request-Id": task_id,
        "X-Api-Sequence": "-1"
    }

    request = {
        "user": {
            "uid": "fake_uid"
        },
        "audio": {
            "url": file_url,
            "format": "mp3",
            "codec": "raw",
            "rate": 16000,
            "bits": 16,
            "channel": 1,
            "language": "en",
        },
        "request": {
            "model_name": "bigmodel",
            # "enable_itn": True,
            # "enable_punc": True,
            # "enable_ddc": True,
            "show_utterances": True,
            # "enable_channel_split": True,
            # "vad_segment": True,
            # "enable_speaker_info": True,
            "corpus": {
                # "boosting_table_name": "test",
                "correct_table_name": "",
                "context": ""
            }
        }
    }
    print(f'Submit task id: {task_id}')
    response = requests.post(
        submit_url, data=json.dumps(request), headers=headers)
    if 'X-Api-Status-Code' in response.headers and response.headers["X-Api-Status-Code"] == "20000000":
        print(
            f'Submit task response header X-Api-Status-Code: {response.headers["X-Api-Status-Code"]}')
        print(
            f'Submit task response header X-Api-Message: {response.headers["X-Api-Message"]}')
        x_tt_logid = response.headers.get("X-Tt-Logid", "")
        print(
            f'Submit task response header X-Tt-Logid: {x_tt_logid}\n')
        return task_id, x_tt_logid
    else:
        print(
            f'Submit task failed and the response headers are: {response.headers}')
        raise Exception(
            f'Submit task failed with status code: {response.status_code}, headers: {response.headers}, response: {response.text}')


def query_task(task_id, x_tt_logid):
    query_url = "https://openspeech.bytedance.com/api/v3/auc/bigmodel/query"

    headers = {
        "X-Api-App-Key": appid,
        "X-Api-Access-Key": token,
        "X-Api-Resource-Id": "volc.bigasr.auc",
        "X-Api-Request-Id": task_id,
        "X-Tt-Logid": x_tt_logid  # 固定传递 x-tt-logid
    }

    response = requests.post(query_url, json.dumps({}), headers=headers)

    if 'X-Api-Status-Code' in response.headers:
        print(
            f'Query task response header X-Api-Status-Code: {response.headers["X-Api-Status-Code"]}')
        print(
            f'Query task response header X-Api-Message: {response.headers["X-Api-Message"]}')
        print(
            f'Query task response header X-Tt-Logid: {response.headers["X-Tt-Logid"]}\n')
    else:
        print(
            f'Query task failed and the response headers are: {response.headers}')
    return response


def format_srt_time(seconds):
    """
    Format seconds into SRT time format (HH:MM:SS,mmm).
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millis = int((seconds - int(seconds)) * 1000)
    return f"{hours:02}:{minutes:02}:{secs:02},{millis:03}"


def convert_to_srt(json_data):
    """
    Convert the JSON response from the ASR API to SRT format.
    """
    srt_output = []
    for i, item in enumerate(json_data.get('utterances', [])):
        start_time = item['start_time'] / 1000
        end_time = item['end_time'] / 1000
        text = item['text'].strip()

        # Format SRT entry
        srt_entry = f"{i + 1}\n"
        srt_entry += f"{format_srt_time(start_time)} --> {format_srt_time(end_time)}\n"
        srt_entry += f"{text}\n\n"

        srt_output.append(srt_entry)

    return ''.join(srt_output)


def test_helper(file: str = None, file_url: str = None, srt: bool = False, speed: float = 1.0):
    """
    Helper function to test the upload and task submission.
    """
    if not file and not file_url:
        raise ValueError("Either file or file_url must be provided.")
    if not file_url:
        file_url = upload(file)
        print(f"file_url: {file_url}")
    task_id, x_tt_logid = submit_task(file_url)
    while True:
        query_response = query_task(task_id, x_tt_logid)
        code = query_response.headers.get('X-Api-Status-Code', "")
        if code == '20000000':  # task finished
            print(query_response.json())
            print("SUCCESS!")
            break
        elif code != '20000001' and code != '20000002':  # task failed
            print("FAILED!")
            break
        time.sleep(1)

    results = query_response.json()
    print("Task results:", results)
    if speed != 1.0:
        for utterance in results['result']['utterances']:
            utterance['start_time'] *= speed
            utterance['end_time'] *= speed
            for word in utterance['words']:
                word['start_time'] *= speed
                word['end_time'] *= speed

    if srt:
        srt_results = convert_to_srt(results['result'])
        print("SRT Results:\n", srt_results)


def main():

    # for file in os.listdir("audios"):

    files = [
        'data/audios/hello,world.mp3',
        'data/audios/hello,world.m4a',
        'data/audios/hello,world.wav',
    ]

    for file in files:
        test_helper(file, srt=True)


def test_convert_to_srt():
    sample_json = {
        "text": "Hello, what?",
        "additions": {
                "duration": "2052"
        },
        "utterances": [
            {
                "text": "Hello, what?",
                "start_time": 640.0,
                "end_time": 1120.0,
                "words": [
                    {
                        "text": "Hello",
                        "start_time": 640.0,
                        "end_time": 720.0,
                        "confidence": 0.0
                    },
                    {
                        "text": " ",
                        "start_time": -1.0,
                        "end_time": -1.0,
                        "confidence": 0.0
                    },
                    {
                        "text": "what",
                        "start_time": 1080.0,
                        "end_time": 1120.0,
                        "confidence": 0.0
                    }
                ]
            }
        ]
    }
    srt_output = convert_to_srt(sample_json)
    print(srt_output)


def test_speed_up_audio_asr_results():

    file = 'data/audios/alex_thomas_pronunciation_raw_sample.mp3'
    # test_helper(file, srt=True)

    import tempfile
    import shutil

    def helper(speed: float):
        # Create a temporary file without auto-deletion
        temp_fd, temp_path = tempfile.mkstemp(suffix='.mp3')
        try:
            os.close(temp_fd)  # Close the file descriptor

            if not os.path.exists(file):
                raise FileNotFoundError(f"Audio file {file} not found.")

            # Copy the source file to temp location
            shutil.copy2(file, temp_path)

            # Process the audio (this may change the file format/path)
            processed_path = change_audio_speed(temp_path, speed)
            print(f"Processed audio saved at: {processed_path}")

            # Use the processed file for testing
            test_helper(processed_path, srt=True, speed=speed)

        finally:
            pass
            # Clean up - remove both original temp file and any processed versions
            # for path_to_remove in [temp_path, temp_path.replace('.mp3', '.wav')]:
            #     if os.path.exists(path_to_remove):
            #         try:
            #             os.remove(path_to_remove)
            #         except OSError:
            #             pass  # Ignore cleanup errors

    helper(2)
    # helper(2.5)
    # helper(3)
    # helper(4)


def test_empty_audio():
    """
    Test with an empty audio file.
    """
    file = 'data/audios/empty.wav'
    if not os.path.exists(file):
        print(f"File {file} does not exist.")
        return
    test_helper(file, srt=True)


if __name__ == '__main__':

    # Original test code (commented out)
    # error audios
    # https://gusto-ai-base-oss-test.wemore.com/b2679a714c26452b939f656b0a58f774.mp3
    # https://gusto-ai-base-oss-test.wemore.com/5d415632be97462fa2f487bd1604a862.wav
    # test_helper(
    #     # file_url="https://gravity-oss.wemore.com/prod/728558618835681280.wav",
    #     # file="/Users/<USER>/Downloads/20250703.mp3",
    #     file_url="https://gravity-oss.wemore.com/prod/5491dc3e-8239-4aba-9d07-cf5b5310799f.aac",
    #     srt=True, speed=1.0)
    # main()
    # test_convert_to_srt()
    # test_speed_up_audio_asr_results()
    # test_empty_audio()
    # auto_convert_file_format(
    #     "https://gravity-oss.wemore.com/prod/5491dc3e-8239-4aba-9d07-cf5b5310799f.aac")

    test_helper(
        file_url="https://gusto-english-oss.wemore.com/exam/questions/1752481321249B1 team work or working online.mp4",
        srt=True, speed=1.0
    )
