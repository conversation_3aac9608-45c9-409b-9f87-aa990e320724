"""Database base configuration and session management."""

import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Database configuration
DB_URL = os.environ.get('DB', '')
if not DB_URL:
    raise ValueError(
        "Database URL is missing. Please set the DB environment variable.")

# Setup SQLAlchemy
engine = create_engine(DB_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


def get_db() -> Session:
    """Get database session"""
    db = SessionLocal()
    try:
        return db
    except Exception:
        db.close()
        raise


def init_db() -> None:
    """Initialize the database and create tables if they don't exist."""
    Base.metadata.create_all(bind=engine)
