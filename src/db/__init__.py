"""Database package initialization."""

from .base import Base, engine, SessionLocal, get_db, init_db
from .models import Task, Chatbot, UserChatHistory
from .crud import (
    get_task,
    create_task,
    update_task,
    check_for_timed_out_tasks,
    create_or_update_chatbot,
    get_chatbot,
    get_all_chatbots,
    delete_chatbot,
    create_chat_history,
    get_chat_history,
    get_user_chat_history,
    get_chat_history_by_before_timestamp,
    update_chat_history,
    delete_chat_history,
    delete_user_chat_history,
    get_chat_history_count,
    get_bot_chat_history,
    update_chat_history_with_api_result,
    sync_chat_history_bulk,
)

# Make everything available at package level
__all__ = [
    "Base",
    "engine",
    "SessionLocal",
    "get_db",
    "init_db",
    "Task",
    "Chatbot",
    "UserChatHistory",
    "get_task",
    "create_task",
    "update_task",
    "check_for_timed_out_tasks",
    "create_or_update_chatbot",
    "get_chatbot",
    "get_all_chatbots",
    "delete_chatbot",
    "create_chat_history",
    "get_chat_history",
    "get_user_chat_history",
    "get_chat_history_by_before_timestamp",
    "update_chat_history",
    "delete_chat_history",
    "delete_user_chat_history",
    "get_chat_history_count",
    "get_bot_chat_history",
    "update_chat_history_with_api_result",
    "sync_chat_history_bulk",
]
