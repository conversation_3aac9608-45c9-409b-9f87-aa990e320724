"""Database models."""

from datetime import datetime
from sqlalchemy import Column, String, DateTime, Text
from sqlalchemy.dialects.postgresql import JSONB

from .base import Base


class Task(Base):
    """Task model for ORM mapping"""
    __tablename__ = "tasks"

    task_id = Column(String, primary_key=True)
    task_type = Column(String, nullable=False)
    input_data = Column(JSONB, nullable=False)
    status = Column(String, nullable=False)
    result = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)


class Chatbot(Base):
    """Chatbot model for ORM mapping"""
    __tablename__ = "chatbots"

    id = Column(String, primary_key=True)
    name = Column(String, nullable=False)
    persona = Column(Text, nullable=False)
    bot_type = Column(String, nullable=False,
                      default="normal")  # Add bot type field
    image_url = Column(String, nullable=True)  # Add image URL field
    voice = Column(String, nullable=True,
                   default="en_female_amanda_mars_bigtts")  # Add voice field
    # Add hello message field
    hello_message = Column(Text, nullable=True,
                           default="Hello! How can I assist you today?")
    # Add voice speed field
    voice_speed = Column(String, nullable=True, default="1.0")
    # Add extra data field
    extra_data = Column(JSONB, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)


class UserChatHistory(Base):
    """User chat history model for ORM mapping"""
    __tablename__ = "user_chat_history"

    id = Column(String, primary_key=True)
    bot_id = Column(String, nullable=False)
    user_id = Column(String, nullable=False)
    message = Column(JSONB, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow,
                        onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
