import base64
from fastapi.websockets import WebSocketState
import requests
from src.models import ChatMessageItem, LLMChatModel, LLMChatModelsResponse, ChatSuggestRequest, ChatSuggestResponse, ChatbotRequest, ChatbotResponse, ChatbotListResponse, ConversationSummaryRequest, ConversationSummaryResponse, DeleteChatbotResponse, LLMChatRequest, LLMChatResponse, MissionRequest, MissionResponse, MissionCheckRequest, MissionCheckResponse, ChatHistoryResponse, DeleteChatHistoryResponse, CreateChatHistoryRequest, CreateChatHistoryResponse, SyncChatHistoryRequest, SyncChatHistoryResponse
from src.db import create_or_update_chatbot, get_all_chatbots, get_chatbot, delete_chatbot, get_bot_chat_history, delete_chat_history, sync_chat_history_bulk
import json
from typing import Optional, Union
import sentry_sdk
import asyncio
from datetime import datetime, timedelta
import shutil
import tempfile
from fastapi import FastAPI, HTTPException, BackgroundTasks, Request, Response, Header, Depends, File, UploadFile, Form, Query, Security, WebSocket, WebSocketDisconnect
from pydantic import BaseModel
from typing import Any, Dict, List, Optional
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse, JSONResponse, StreamingResponse
from fastapi.security.api_key import APIKeyHeader
import uuid
import os  # For os.getenv in background task and error messages
from fastapi.templating import Jinja2Templates
from dotenv import load_dotenv
from src import asr
from src import db
from src.scheduler import start_scheduler
from src.upload import delete_uploaded_file, upload, upload_bytes
from src.models import (ASRTaskResponse, ASRTaskStatus, GrammarSuggestionRequest, GrammarSuggestionResponse, GrammarSuggestions,
                        PronunciationAssessmentRequest, PronunciationAssessmentResponse, ElsaPronunciationAssessmentResponse, ChatRequest, ChatResponse, TTSRequest, TTSResponse, TTSVoiceResponse, TranslateRequest, TranslateResponse, ChatbotRequest, ChatbotResponse, ChatbotListResponse)
from src.pronunciation_azure import continuous_pronunciation_assessment as pronunciation_assessment_impl
from src.pronunciation_elsa import pronunciation_assessment as elsa_pronunciation_assessment_impl
# from src.pronunciation_xfyun import pronunciation_assessment as pronunciation_assessment_impl
from src.utils.audio_utils import change_audio_format, convert_to_16kHz_mono_wav_audio, get_audio_duration
from src.chat import generate_response, generate_stream_response
from src.utils.tts import get_voices
from src.utils.tts_duplex_client import TTSDuplexClient
load_dotenv()


def extract_voice_message_info(content: str) -> tuple[bool, Optional[str], Optional[float]]:
    """
    Extract voice message information from content.

    Returns:
        tuple: (is_voice_message, audio_url, duration)
    """
    try:
        parsed = json.loads(content)
        if isinstance(parsed, dict) and parsed.get("type") == "voice":
            return True, parsed.get("audioUrl"), parsed.get("duration")
    except (json.JSONDecodeError, TypeError):
        pass
    return False, None, None


def process_voice_message_for_storage(message: ChatMessageItem) -> ChatMessageItem:
    """
    Process voice message for proper storage in database.
    """
    if message.content and message.type == "text":
        is_voice, audio_url, duration = extract_voice_message_info(
            message.content)
        if is_voice:
            message.type = "audio"
            message.duration = duration
            # Keep the original content for the audio URL
    return message


sentry_sdk.init(
    dsn="https://<EMAIL>/48",

    # Set environment to production
    environment=os.getenv("ENVIRONMENT", "production"),
    # Set the sample rate for performance monitoring
    traces_sample_rate=0.2,
)


API_KEY = os.getenv("API_KEY", "")
if not API_KEY:
    raise ValueError("API_KEY is not set in the environment variables.")

api_key_header = APIKeyHeader(name="X-API-KEY", auto_error=False)
user_auth_header = APIKeyHeader(name="X-5E-USER", auto_error=False)

app = FastAPI()

# Mount the static files directory
app.mount("/static", StaticFiles(directory="src/static"), name="static")

# Set up Jinja2 templates
templates = Jinja2Templates(directory="src/static")


async def verify_api_key(
    api_key: Optional[str] = Security(api_key_header),
    user_auth: Optional[str] = Security(user_auth_header)
):
    if not api_key and not user_auth:
        raise HTTPException(
            status_code=401,
            detail="Missing API Key or User Auth",
            headers={"WWW-Authenticate": "Invalid API key or User Auth"},
        )
    if api_key and api_key != API_KEY:
        raise HTTPException(
            status_code=403,
            detail="Invalid API Key",
            headers={"WWW-Authenticate": "Invalid API key"},
        )

    user_id = None
    try:
        if user_auth:
            user_id = json.loads(user_auth).get("id")
            if user_id is None:
                raise HTTPException(
                    status_code=403,
                    detail="Invalid User Auth",
                    headers={"WWW-Authenticate": "Invalid User Auth"},
                )

    except json.JSONDecodeError:
        raise HTTPException(
            status_code=403,
            detail="Invalid User Auth format",
            headers={"WWW-Authenticate": "Invalid User Auth format"},
        )

    return {"api_key": api_key, "user_id": user_id}


async def verify_api_key_in_websocket(websocket: WebSocket) -> tuple[bool, Optional[str]]:
    """
    Verify API key or user auth in WebSocket connection.
    Returns (is_valid, user_id)
    """
    api_key = websocket.headers.get(
        "X-API-KEY") or websocket.query_params.get("X-API-KEY")
    user_auth = websocket.headers.get(
        "X-5E-USER") or websocket.query_params.get("X-5E-USER")

    if not api_key and not user_auth:
        await websocket.close(code=4001, reason="Missing API Key or User Auth")
        return False, None

    if api_key and api_key != API_KEY:
        await websocket.close(code=4002, reason="Invalid API Key")
        return False, None

    user_id = None
    if user_auth:
        try:
            user_data = json.loads(user_auth)
            user_id = user_data.get("id")
            if user_id is None:
                await websocket.close(code=4003, reason="Invalid User Auth")
                return False, None
        except json.JSONDecodeError:
            await websocket.close(code=4003, reason="Invalid User Auth format")
            return False, None

    return True, user_id


@app.on_event("startup")
async def startup_event():
    start_scheduler()
    db.init_db()
    app.state.start_time = datetime.now()  # Add this line to track start time


@app.get("/asr")
async def asr_page(request: Request):
    """
    Serve the beautiful ASR page for the 2-step ASR process
    """
    return templates.TemplateResponse("asr.html", {"request": request})


@app.get("/chat")
async def chat_page(request: Request):
    """
    Serve the beautiful CHAT page
    """
    return templates.TemplateResponse("chat.html", {"request": request})


@app.get("/health")
async def health_page(request: Request):
    """
    Serve the health check page
    """
    return templates.TemplateResponse("health.html", {"request": request})


@app.get("/pronunciation_assessment")
async def pronunciation_assessment_page(request: Request):
    """
    Serve the pronunciation assessment page
    """
    return templates.TemplateResponse("pronunciation_assessment.html", {"request": request})


@app.get("/grammar_suggestion")
async def grammar_suggestion_page(request: Request):
    """
    Serve the grammar suggestion page
    """
    return templates.TemplateResponse("grammar_suggestion.html", {"request": request})


@app.get("/mission")
async def mission_page(request: Request):
    """
    Serve the mission manager page
    """
    return templates.TemplateResponse("mission.html", {"request": request})


@app.post("/api/asr", response_model=ASRTaskResponse)
async def create_asr_task(
    background_tasks: BackgroundTasks,
    file: Optional[UploadFile] = File(None),
    audio_url: Optional[str] = Form(None),
    srt: Optional[bool] = Form(False),
    message_id: Optional[str] = Form(None),
    auth_data: dict = Depends(verify_api_key)
):
    """
    First step: Submit an audio file or audio URL for ASR processing
    """
    user_id = auth_data.get("user_id")
    log_user_activity(user_id, "ASR_TASK_CREATED",
                      f"srt={srt}, file={file.filename if file else 'none'}, url={bool(audio_url)}")

    try:
        # Generate a task ID
        task_id = str(uuid.uuid4())
        file_path = None

        # Check if we have either file or audio_url
        if not file and not audio_url:
            raise HTTPException(
                status_code=400,
                detail="Either file or audio_url must be provided"
            )

        if file:
            # Save the uploaded file temporarily
            file_path = f"data/uploads/{task_id}_{file.filename}"
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, "wb") as f:
                content = await file.read()
                f.write(content)

            # Create a task in the database with file information
            input_data = {
                "file_name": file.filename,
                "file_path": file_path,
                "is_url": False,
                "srt": srt,
                "user_id": user_id,  # Include user_id in task data
                "message_id": message_id
            }
        else:
            # Use the provided audio URL
            input_data = {
                "audio_url": audio_url,
                "is_url": True,
                "srt": srt,
                "user_id": user_id,  # Include user_id in task data
                "message_id": message_id
            }

        # Create task in the database
        db.create_task(task_id, "asr", input_data)

        # Process the ASR task in the background
        if file_path:
            background_tasks.add_task(
                process_asr_task, task_id, file_path, message_id, user_id)
        else:
            background_tasks.add_task(
                process_asr_url_task, task_id, audio_url, message_id, user_id)

        return ASRTaskResponse(task_id=task_id, status='processing')
    except Exception as e:
        sentry_sdk.capture_exception(e)
        # Clean up any temporary files that might have been created
        if 'file_path' in locals() and file_path and os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(
            status_code=500, detail=f"Failed to create ASR task: {str(e)}")


@app.get("/api/asr/status", response_model=ASRTaskStatus)
async def check_asr_status(
    task_id: str = Query(...),
    auth_data: dict = Depends(verify_api_key)
):
    """
    Second step: Check the status of an ASR task
    """
    user_id = auth_data.get("user_id")
    try:
        task = db.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        result = task["result"]["result"] if task["status"] == "completed" else None
        error = task["result"].get(
            "error", "ASR task failed") if task["status"] == "failed" else None
        if error and not isinstance(error, str):
            error = json.dumps(error) if error else None
        # print("result:", result)
        if result is not None:
            if task["input_data"].get("srt", False):
                # Convert the result to SRT format if requested
                print("Converting result to SRT format")
                result["srt"] = asr.convert_to_srt(result)

        return {
            "task_id": task["task_id"],
            "status": task["status"],
            "result": result,
            "error": error,
            "created_at": task["created_at"],
            "updated_at": task["updated_at"]
        }
    except HTTPException:
        # Don't capture 404s in Sentry as they're expected errors
        raise
    except Exception as e:
        import traceback
        traceback.print_stack()
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Failed to check ASR task status: {str(e)}")


async def process_asr_task(task_id: str, file_path: str, message_id: str = None, user_id: str = None):
    """
    Background task to process the ASR request
    """
    original_file_url = None
    file_url = None
    try:
        # Update task status to processing
        db.update_task(task_id, "processing")

        # Upload the file and get the URL
        file_url = upload(file_path)
        original_file_url = file_url
        file_url = await asr.convert_to_16kHz_mono_wav_audio(file_url)
        print(f"Using file_url: {file_url}")

        # Submit the ASR task
        asr_task_id, x_tt_logid = asr.submit_task(file_url)
        now = datetime.now()
        timeout = now + timedelta(minutes=10)

        # Poll for results
        while True:
            if datetime.now() > timeout:
                # Update task as failed due to timeout
                error_msg = "ASR processing timed out after 10 minutes"
                sentry_sdk.capture_message(error_msg, level="error")
                db.update_task(task_id, "failed", {"error": error_msg})
                break
            query_response = asr.query_task(asr_task_id, x_tt_logid)
            # print("query_response:", query_response.json())
            code = query_response.headers.get('X-Api-Status-Code', "")

            # task finished successfully : 20000000
            # no valid speech detected : 20000003
            if code == '20000000' or code == '20000003':
                # Update task with results
                result = query_response.json()
                db.update_task(task_id, "completed", result)

                # Save API result to chat history if message_id is provided
                if message_id and user_id:
                    db.update_chat_history_with_api_result(
                        user_id, message_id, "asr", result)
                break

            elif code != '20000001' and code != '20000002':  # task failed
                # Update task as failed
                error_msg = "ASR processing failed with code: " + code + ' - task_id: ' + task_id
                sentry_sdk.capture_message(error_msg, level="error")
                db.update_task(task_id, "failed", {
                    "error": {
                        "error": error_msg,
                        "headers": str(query_response.headers),
                        "text": query_response.text
                    }})
                break

            # Wait before checking again
            await asyncio.sleep(2)

        # Clean up the temporary file
        if os.path.exists(file_path):
            os.remove(file_path)

    except Exception as e:
        # Capture exception in Sentry
        sentry_sdk.capture_exception(e)

        # Update task as failed if there's an exception
        db.update_task(task_id, "failed", {"error": str(e)})

        # Clean up the temporary file
        if os.path.exists(file_path):
            os.remove(file_path)
    finally:
        try:
            if file_url:
                delete_uploaded_file(file_url)
                if original_file_url and original_file_url != file_url:
                    delete_uploaded_file(original_file_url)
        except Exception as e:
            sentry_sdk.capture_exception(e)
            print(f"Failed to delete temporary audio URL: {str(e)}")


async def process_asr_url_task(task_id: str, audio_url: str, message_id: str = None, user_id: str = None):
    """
    Background task to process the ASR request using an audio URL
    """
    original_audio_url = audio_url
    try:
        # Update task status to processing
        db.update_task(task_id, "processing")

        audio_url = await asr.convert_to_16kHz_mono_wav_audio(audio_url)
        print(f"Using audio_url: {audio_url}")

        # Submit the ASR task with the URL directly
        asr_task_id, x_tt_logid = asr.submit_task(audio_url)
        now = datetime.now()
        timeout = now + timedelta(minutes=10)

        # Poll for results
        while True:
            if datetime.now() > timeout:
                # Update task as failed due to timeout
                error_msg = "ASR processing timed out after 10 minutes"
                sentry_sdk.capture_message(error_msg, level="error")
                db.update_task(task_id, "failed", {"error": error_msg})
                break
            query_response = asr.query_task(asr_task_id, x_tt_logid)
            code = query_response.headers.get('X-Api-Status-Code', "")

            if code == '20000000' or code == '20000003':  # task finished successfully
                # Update task with results
                result = query_response.json()
                db.update_task(task_id, "completed", result)

                # Save API result to chat history if message_id is provided
                if message_id and user_id:
                    db.update_chat_history_with_api_result(
                        user_id, message_id, "asr", result)
                break

            elif code != '20000001' and code != '20000002':  # task failed
                # Update task as failed
                error_msg = "ASR processing failed with code: " + code + ' - task_id: ' + task_id
                sentry_sdk.capture_message(error_msg, level="error")
                db.update_task(task_id, "failed", {
                    "error": {
                        "error": error_msg,
                        "headers": str(query_response.headers),
                        "text": query_response.text
                    }})
                break

            # Wait before checking again
            await asyncio.sleep(2)

    except Exception as e:
        # Capture exception in Sentry
        sentry_sdk.capture_exception(e)

        # Update task as failed if there's an exception
        db.update_task(task_id, "failed", {"error": str(e)})
    finally:
        if original_audio_url != audio_url:
            # Clean up the audio URL if it was a temporary file
            try:
                delete_uploaded_file(audio_url)
            except Exception as e:
                sentry_sdk.capture_exception(e)
                print(f"Failed to delete temporary audio URL: {str(e)}")


@app.get("/api/status", response_model=Dict[str, Any])
async def get_api_status():
    """
    Get the current status of the API including version, uptime, and database connection status
    """
    try:
        # Check database connection
        db_status = "healthy"
        try:
            # Perform a simple database operation to check connection
            db.get_task("test-connection")
        except Exception as e:
            db_status = f"error: {str(e)}"
            sentry_sdk.capture_exception(e)

        # print(app.state.start_time)

        # Get application uptime
        start_time = getattr(app.state, "start_time", datetime.now())
        uptime = (datetime.now() - start_time).total_seconds()

        return {
            "status": "online",
            "version": "1.0.0",  # You may want to define this elsewhere and import it
            "database": db_status,
            "uptime_seconds": uptime,
            "environment": os.environ.get("ENVIRONMENT", "production"),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Failed to get API status: {str(e)}")


@app.post('/api/pronunciation_assessment',
          dependencies=[Depends(verify_api_key)],
          response_model=PronunciationAssessmentResponse)
async def pronunciation_assessment(
    reference_text: str = Form(...),
    audio_file: Optional[UploadFile] = File(None),
    audio_url: Optional[str] = Form(None),
    language: str = Form('en-US'),
    message_id: Optional[str] = Form(None),
    auth_data: dict = Depends(verify_api_key)
):
    user_id = auth_data.get("user_id")
    if not audio_file and not audio_url:
        return JSONResponse(
            content={'error': 'Either audio_file or audio_url must be provided'},
            status_code=400
        )

    audio_filename = None
    try:
        # Handle audio_url case
        if audio_url and not audio_file:

            # Download the audio file from URL
            response = requests.get(audio_url)
            response.raise_for_status()

            # Create a temporary file for the downloaded audio
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                tmp_file.write(response.content)
                audio_filename = tmp_file.name

            # Convert to proper format if needed
            audio_filename = change_audio_format(
                audio_filename, 'wav', samperate=16000, channels=1)

        # Handle audio_file case (existing logic)
        elif audio_file:
            # change to wav if not wav
            if not audio_file.filename.endswith('.wav'):
                # Create a temporary file for the audio with original extension
                original_extension = os.path.splitext(audio_file.filename)[1]
                with tempfile.NamedTemporaryFile(suffix=original_extension, delete=False) as tmp_audio_file:
                    audio_filename = tmp_audio_file.name
                    shutil.copyfileobj(audio_file.file, tmp_audio_file)

                # Always convert to wav format with proper sample rate
                audio_filename = change_audio_format(
                    audio_filename, 'wav', samperate=16000, channels=1)
            else:
                # For .wav files, save them to a temporary location
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                    shutil.copyfileobj(audio_file.file, tmp_file)
                    audio_filename = tmp_file.name

        # Check audio duration
        # duration_seconds = get_audio_duration(audio_filename)

        # if duration_seconds > 30:
        #     return JSONResponse(
        #         content={
        #             'error': 'Audio duration exceeds maximum limit of 30 seconds'},
        #         status_code=400
        #     )

        # Perform pronunciation assessment
        result = pronunciation_assessment_impl(
            audio_filename, reference_text, language=language)

        if result["status"] != "success":
            # Log failures to Sentry
            sentry_sdk.capture_message(
                f"Pronunciation assessment failed: {result['message']}",
                level="error"
            )
            return PronunciationAssessmentResponse(
                status="failed",
                error_message=result["message"]
            )

        # Return the result directly as the response model will validate and format it
        response = PronunciationAssessmentResponse(
            result=result["data"],
            status="success",
            error_message=None
        )

        # Save API result to chat history if message_id is provided
        if message_id and user_id:
            db.update_chat_history_with_api_result(
                user_id, message_id, "pronunciation_assessment", result["data"])

        return response

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(error_traceback)

        # Send exception to Sentry with additional context
        context_data = {
            "reference_text": reference_text,
            "language": language,
        }

        if audio_file:
            context_data["file_name"] = audio_file.filename
        if audio_url:
            context_data["audio_url"] = audio_url

        sentry_sdk.capture_exception(e, extras=context_data)

        return PronunciationAssessmentResponse(
            status="failed",
            error_message=str(e)
        )
    finally:
        # Clean up temporary files
        if audio_filename and os.path.exists(audio_filename):
            os.remove(audio_filename)


@app.post('/api/pronunciation_assessment/elsa',
          dependencies=[Depends(verify_api_key)],
          response_model=ElsaPronunciationAssessmentResponse)
async def elsa_pronunciation_assessment(
    audio_file: Optional[UploadFile] = File(None),
    audio_url: Optional[str] = Form(None),
    return_json: bool = Form(True),
    sync: bool = Form(True),
    force_grammar_vocab: bool = Form(False)
):
    """
    Elsa Unscripted Pronunciation Assessment API endpoint.
    Provides detailed pronunciation feedback using Elsa's API.
    """
    if not audio_file and not audio_url:
        return JSONResponse(
            content={'error': 'Either audio_file or audio_url must be provided'},
            status_code=400
        )

    converted_audio_url = None
    try:
        if audio_file:
            # Save the uploaded audio file to a temporary location
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                shutil.copyfileobj(audio_file.file, tmp_file)
                audio_filename = tmp_file.name

            audio_url = upload(audio_filename)

        converted_audio_url = await convert_to_16kHz_mono_wav_audio(
            file_url=audio_url)

        # Use the existing Elsa pronunciation assessment implementation
        result = await elsa_pronunciation_assessment_impl(
            return_json=return_json,
            sync=sync,
            force_grammar_vocab=force_grammar_vocab,
            audio_file=None,
            audio_path=converted_audio_url,
            audio_data=None
        )

        # Return the result directly as Elsa API response
        return ElsaPronunciationAssessmentResponse(
            speakers=result.get("speakers", []),
            transcript=result.get("transcript", ""),
            timeline=result.get("timeline", []),
            api_version=result.get("api_version", "1.0"),
            api_plan=result.get("api_plan", "premium"),
            recording_quality=result.get("recording_quality", "ok"),
            assessment_quality=result.get("assessment_quality", "good"),
            total_time=result.get("total_time", 0.0),
            success=result.get("success", True),
            status="success",
            error_message=None
        )

    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(error_traceback)

        # Send exception to Sentry with additional context
        context_data = {}
        if audio_file:
            context_data["file_name"] = audio_file.filename
        if audio_url:
            context_data["audio_url"] = audio_url

        sentry_sdk.capture_exception(e, extras=context_data)

        return ElsaPronunciationAssessmentResponse(
            speakers=[],
            transcript="",
            timeline=[],
            api_version="1.0",
            api_plan="premium",
            recording_quality="poor",
            assessment_quality="failed",
            total_time=0.0,
            success=False,
            status="failed",
            error_message=str(e)
        )
    finally:
        # Clean up temporary files
        if converted_audio_url:
            try:
                delete_uploaded_file(converted_audio_url)
            except Exception as e:
                sentry_sdk.capture_exception(e)
                print(f"Failed to delete temporary audio URL: {str(e)}")


@app.get("/pronunciation_assessment_elsa")
async def pronunciation_assessment_elsa_page(request: Request):
    """
    Serve the Elsa pronunciation assessment page
    """
    return templates.TemplateResponse("pronunciation_assessment_elsa.html", {"request": request})


@app.post("/api/chat", response_model=ChatResponse)
async def chat_completion(
    chat_request: ChatRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    OpenAI-compatible chat completion API endpoint with optional streaming support.
    If chat_request.stream is True the response will be streamed.
    """
    user_id = auth_data.get("user_id")

    user_message_id = uuid.uuid4().hex
    bot_message_id = uuid.uuid4().hex

    # Check if streaming is requested
    if getattr(chat_request, "stream", False):

        async def event_generator():
            messages = [{"role": msg.role, "content": msg.content}
                        for msg in chat_request.messages]
            messages[-1]["content"] = f"""
!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!

{messages[-1]["content"]}

!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!

Now you are ready to answer the user's question.
            """
            user_message_id_msg = json.dumps(
                {"user_message_id": user_message_id})
            bot_message_id_msg = json.dumps({"bot_message_id": bot_message_id})
            yield f'data: {user_message_id_msg}\n\n'
            yield f'data: {bot_message_id_msg}\n\n'
            # generate_stream_response returns an async iterator of chunks (which can be formatted as needed)
            async for chunk in generate_stream_response(messages, model=chat_request.model):
                print(f"Streaming chunk: {chunk}")
                # Here, we yield each chunk as an SSE message
                yield f"data: {chunk}\n\n"
                # Simulate some delay for streaming effect
                # await asyncio.sleep(0.3)

        return StreamingResponse(event_generator(), media_type="text/event-stream")
    else:
        try:
            messages = [{"role": msg.role, "content": msg.content}
                        for msg in chat_request.messages]
            response = await generate_response(
                messages,
                model=chat_request.model,
            )
            return response
        except Exception as e:
            sentry_sdk.capture_exception(e)
            raise HTTPException(
                status_code=500,
                detail=f"Chat completion failed: {str(e)}"
            )


async def process_grammar_suggestion_task(task_id: str, text: str, coloquial: Optional[bool] = False, message_id: str = None, user_id: str = None):
    """
    Background task to process grammar suggestions
    """
    try:
        db.update_task(task_id, "processing")

        # Import grammar suggestion tool
        from src.tools.grammar_suggestion import generate_grammar_suggestion, generate_coloquial_grammar_suggestion

        # Call the actual grammar suggestion service
        if coloquial:
            # If colloquial is True, use the colloquial grammar suggestion
            suggestions = await generate_coloquial_grammar_suggestion(text)
        else:
            suggestions = await generate_grammar_suggestion(text)

        # Convert to dict for storage
        suggestions_dict = {
            "errors": suggestions.errors,
            "improvements": suggestions.improvements,
            "suggested_text": suggestions.suggested_text if hasattr(suggestions, "suggested_text") else None
        }

        # Save the suggestions to the database
        db.update_task(task_id, "completed", suggestions_dict)

        # Save API result to chat history if message_id is provided
        if message_id and user_id:
            db.update_chat_history_with_api_result(
                user_id, message_id, "grammar_suggestion", suggestions_dict)

    except Exception as e:
        sentry_sdk.capture_exception(e)
        db.update_task(task_id, "failed", {"error": str(e)})


@app.post("/api/grammar_suggestion", response_model=GrammarSuggestionResponse)
async def grammar_suggestion(
    background_tasks: BackgroundTasks,
    request: GrammarSuggestionRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Endpoint for grammar suggestions
    """
    user_id = auth_data.get("user_id")
    try:
        # Create a task ID
        task_id = str(uuid.uuid4())

        # Store input data in database
        input_data = {
            "text": request.text,
            "coloquial": request.coloquial if request.coloquial is not None else False,
            "message_id": request.message_id
        }
        db.create_task(task_id, "grammar_suggestion", input_data)

        # Process grammar suggestion in background
        # background_tasks.add_task(
        # process_grammar_suggestion_task, task_id, request.text, request.coloquial)
        await process_grammar_suggestion_task(task_id, request.text, request.coloquial, request.message_id, user_id)

        task = db.get_task(task_id)
        if task is not None and task["status"] == "completed":
            suggestions = GrammarSuggestions(
                errors=task["result"]["errors"],
                improvements=task["result"]["improvements"],
                suggested_text=task["result"].get("suggested_text")
            )
            return GrammarSuggestionResponse(
                task_id=task_id,
                status="completed",
                suggestions=suggestions
            )

        # Return task ID for status checking
        return GrammarSuggestionResponse(
            task_id=task_id,
            status=task["status"],
            suggestions=None
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Grammar suggestion failed: {str(e)}"
        )


@app.get("/api/grammar_suggestion/{task_id}", response_model=GrammarSuggestionResponse)
async def get_grammar_suggestion_task_result(
    task_id: str,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Endpoint for grammar suggestions
    """
    user_id = auth_data.get("user_id")
    try:
        # Call the grammar suggestion service
        task = db.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")

        return GrammarSuggestionResponse(
            task_id=task_id,
            status=task["status"],
            suggestions=task["result"] if task["status"] == "completed" else None
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve grammar suggestion: {str(e)}"
        )


# --- New TTS Background Task ---


async def process_tts_task(task_id: str, text: str, voice: str, speed: float, message_id: str = None, user_id: str = None):
    """
    Background task to process TTS requests
    """
    try:
        db.update_task(task_id, "processing")
        # Import the text_to_speech function from tts module
        from src.tools.tts import text_to_speech

        # Generate the speech audio (as base64 encoded string)
        audio_base64 = await text_to_speech(text, voice, speed)
        audio_data = base64.b64decode(audio_base64)
        obj_name = f"{message_id}_tts.mp3" if message_id else f"{task_id}_tts.mp3"
        remote_url = upload_bytes(audio_data, obj_name)
        result = {"audio_url": remote_url}
        db.update_task(task_id, "completed", result)

        # Save API result to chat history if message_id is provided
        if message_id and user_id:
            db.update_chat_history_with_api_result(
                user_id, message_id, "tts", result)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        db.update_task(task_id, "failed", {"error": str(e)})

# --- New TTS Endpoints ---


@app.get("/tts")
async def tts_page(request: Request):
    """
    Serve the TTS page for the 2-step TTS process
    """
    return templates.TemplateResponse("tts.html", {"request": request})


@app.post("/api/tts", response_model=TTSResponse)
async def tts_request(
    background_tasks: BackgroundTasks,
    request: TTSRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Endpoint to create a TTS task (2-step API). Supports streaming mode if request.stream is True.
    """
    user_id = auth_data.get("user_id")
    if request.stream:
        client = TTSDuplexClient(
            voice=request.voice, speed_ratio=request.speed)

        async def stream_generator():
            try:
                await client.connect()
                await client.send_text(request.text)
                await client.end_input()

                async for chunk in client.audio_stream():
                    yield chunk
            finally:
                await client.close()

        return StreamingResponse(
            stream_generator(),
            media_type="audio/mpeg",
            headers={"Content-Disposition": f"attachment; filename=tts_stream.mp3"}
        )
    try:
        # Create a task ID and store input data
        task_id = str(uuid.uuid4())
        input_data = {"text": request.text,
                      "voice": request.voice, "message_id": request.message_id}
        db.create_task(task_id, "tts", input_data)

        # Process TTS task in background
        background_tasks.add_task(
            process_tts_task, task_id, request.text, request.voice, request.speed, request.message_id, user_id)

        return TTSResponse(task_id=task_id, status="processing", audio_base64=None)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"TTS request failed: {str(e)}"
        )


@app.get("/api/tts/{task_id}", response_model=TTSResponse)
async def get_tts_result(
    task_id: str,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Endpoint to get the result of a TTS task.
    """
    user_id = auth_data.get("user_id")
    try:
        task = db.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        audio_base64 = task["result"]["audio_base64"] if task["status"] == "completed" and task.get(
            "result") else None
        return TTSResponse(
            task_id=task_id,
            status=task["status"],
            audio_base64=audio_base64
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve TTS result: {str(e)}"
        )


@app.get("/api/tts_voices", response_model=TTSVoiceResponse)
async def get_tts_voices(api_key: str = Depends(verify_api_key)):
    """
    Endpoint to retrieve available TTS voices.
    """
    voices = get_voices()
    if not voices:
        raise HTTPException(status_code=404, detail="No TTS voices found")
    return TTSVoiceResponse(
        status="success",
        voices=voices
    )

# --- New Translate Background Task and Endpoints ---

# tranlate_page


@app.get("/translate")
async def translate_page(request: Request):
    """
    Serve the translation page for the 2-step translation process
    """
    return templates.TemplateResponse("translate.html", {"request": request})


async def process_translate_task(task_id: str, text: str, before_text: Optional[str] = None, message_id: str = None, user_id: str = None):
    """
    Background task to process translation requests.
    """
    try:
        db.update_task(task_id, "processing")
        # Import the translate function from translate module
        from src.tools.translate import translate_text
        # Run the async translate_text function
        translated_text = await translate_text(text, before_text)
        result = {"translated_text": translated_text}
        db.update_task(task_id, "completed", result)

        # Save API result to chat history if message_id is provided
        if message_id and user_id:
            db.update_chat_history_with_api_result(
                user_id, message_id, "translate", result)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        db.update_task(task_id, "failed", {"error": str(e)})


@app.post("/api/translate", response_model=TranslateResponse)
async def translate_request(
    background_tasks: BackgroundTasks,
    request: TranslateRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Endpoint to create a translation task (2-step API).
    """
    user_id = auth_data.get("user_id")
    try:
        # Create a new task ID and store the input text
        task_id = str(uuid.uuid4())
        input_data = {"text": request.text,
                      "before_text": request.before_text or "",
                      "message_id": request.message_id}
        db.create_task(task_id, "translate", input_data)

        # Process the translation in background
        # background_tasks.add_task(
        # process_translate_task, task_id, request.text, request.before_text)
        await process_translate_task(task_id, request.text, request.before_text, request.message_id, user_id)
        task = db.get_task(task_id)
        if task is not None and task["status"] == "completed":
            translated_text = task["result"].get("translated_text")
            return TranslateResponse(
                task_id=task_id,
                status="completed",
                translated_text=translated_text
            )

        return TranslateResponse(task_id=task_id, status="processing", translated_text=None)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Translate request failed: {str(e)}"
        )


@app.get("/api/translate/{task_id}", response_model=TranslateResponse)
async def get_translate_result(
    task_id: str,
    api_key: str = Depends(verify_api_key)
):
    """
    Endpoint to retrieve the result of a translation task.
    """
    try:
        task = db.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        translated_text = (
            task["result"].get("translated_text")
            if task["status"] == "completed" and task.get("result")
            else None
        )
        return TranslateResponse(
            task_id=task_id,
            status=task["status"],
            translated_text=translated_text
        )
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve translate result: {str(e)}"
        )


@app.post("/api/chat_suggest", response_model=ChatSuggestResponse)
async def chat_suggest(
    request: ChatSuggestRequest,
    api_key: str = Depends(verify_api_key)
):
    """
    Endpoint to create a chat suggestion request.
    """
    try:
        # Import the chat suggestion generation function
        from src.tools.chat_suggestion import generate_chat_suggestions

        # Generate context-aware suggestions based on the conversation
        chat_suggestions = await generate_chat_suggestions(
            request.conversation_history,
            request.context or ""
        )

        return ChatSuggestResponse(suggestions=chat_suggestions)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500, detail=f"Chat suggestion request failed: {str(e)}"
        )


@app.websocket("/api/tts_stream")
async def tts_stream_websocket(websocket: WebSocket):
    """
    WebSocket endpoint for bidirectional TTS streaming.

    Allows clients to:
    - Connect and set voice parameters
    - Send text chunks incrementally
    - Receive audio chunks as they're synthesized
    - Signal end of text input

    Protocol:
    1. Client sends initialization JSON with voice and parameters
    2. Client sends text chunks as strings
    3. Server streams audio chunks in binary
    4. Client sends "END" to signal completion
    """

    is_valid, user_id = await verify_api_key_in_websocket(websocket)
    if not is_valid:
        return

    message_id = websocket.query_params.get("message_id", None)
    await websocket.accept()
    print(f"WebSocket TTS connection established for user: {user_id}")
    tts_client = None

    try:
        # Wait for the initial configuration message
        init_data = await websocket.receive_json()

        bot_id = init_data.get("bot_id", "")
        voice = init_data.get("voice", "en_female_amanda_mars_bigtts")
        # Support both 'speed' and 'speed_ratio' for backward compatibility
        voice_speed = float(init_data.get(
            "speed", init_data.get("voiceSpeed", 1.0)))
        emotion = init_data.get("emotion", "")

        # Create and connect the TTS client
        tts_client = TTSDuplexClient(
            voice=voice,
            speed_ratio=voice_speed,
            emotion=emotion
        )

        if not await tts_client.connect():
            await websocket.send_json({"error": "Failed to connect to TTS service"})
            return

        # Inform client that connection is ready
        await websocket.send_json({"status": "ready"})

        # Start a background task to receive audio chunks and forward them to the websocket
        audio_task = asyncio.create_task(
            forward_audio_chunks(tts_client, websocket))

        try:
            # Main loop to receive text chunks from the client
            while True:
                data = await websocket.receive_text()

                # Check if client wants to end the session
                if data == "<|END|>":
                    await tts_client.end_input()
                    break

                # Process the received text chunk
                success = await tts_client.send_text(data)
                if not success:
                    await websocket.send_json({"error": "Failed to process text chunk"})

            # Wait for audio processing to complete
            full_audio_response = await audio_task
            remote_url = None
            if full_audio_response:
                obj_name = f"{uuid.uuid4().hex}_tts.mp3" if message_id is None else f"{message_id}_tts.mp3"
                remote_url = upload_bytes(full_audio_response, obj_name)

            await websocket.send_json({"status": "completed", "full_audio_response": remote_url})
            print(
                f"TTS streaming completed for user: {user_id}, message_id: {message_id}, remote_url: {remote_url}")

            if message_id and user_id:
                db.update_chat_history_with_api_result(
                    user_id,
                    message_id,
                    "tts", {"audio_url": remote_url} if remote_url else {}
                )

        except (WebSocketDisconnect, Exception) as e:
            # Cancel the audio task if it's still running
            if not audio_task.done():
                audio_task.cancel()
                try:
                    await audio_task
                except asyncio.CancelledError:
                    pass

            # Re-raise non-disconnect exceptions
            if not isinstance(e, WebSocketDisconnect):
                raise

    except json.JSONDecodeError:
        await websocket.send_json({"error": "Invalid JSON format in initialization"})
    except Exception as e:
        # Log the error and notify the client
        sentry_sdk.capture_exception(e)
        try:
            await websocket.send_json({"error": f"TTS streaming error: {str(e)}"})
        except:
            pass
    finally:
        # Ensure client is closed properly
        if tts_client:
            await tts_client.close()


async def forward_audio_chunks(client: TTSDuplexClient, websocket: WebSocket):
    """
    Forwards audio chunks from the TTS client to the WebSocket.
    """
    try:

        full_audio_response = b""
        async for audio_chunk in client.audio_stream():
            # Send the binary audio data directly to the client
            full_audio_response += audio_chunk
            await websocket.send_bytes(audio_chunk)
        return full_audio_response
    except Exception as e:
        sentry_sdk.capture_exception(e)
        await websocket.send_json({"error": f"Audio streaming error: {str(e)}"})


@app.websocket("/api/chat_with_tts_stream")
async def chat_with_tts_stream_websocket(
    websocket: WebSocket,
    background_tasks: BackgroundTasks
):
    """
    WebSocket endpoint for real-time chat with TTS streaming.

    Protocol:
    1. Client sends initialization JSON with model, voice, TTS parameters, and API key
    2. Client sends chat messages as JSON with messages array
    3. Server streams text responses and audio chunks back to client
    4. Client can send new messages to continue the conversation
    """
    is_valid, user_id = await verify_api_key_in_websocket(websocket)
    if not is_valid:
        return
    await websocket.accept()
    print(f"WebSocket Chat+TTS connection established for user: {user_id}")
    tts_client = None

    try:
        # Wait for the initial configuration message
        init_data = await websocket.receive_json()

        # TTS configuration
        bot_id = init_data.get("bot_id", "")
        if not bot_id:
            await websocket.send_json({"error": "Bot ID is required"})
            return
        voice = init_data.get("voice", "en_female_amanda_mars_bigtts")
        voice_speed = float(init_data.get(
            "speed", init_data.get("voiceSpeed", 1.0)))
        emotion = init_data.get("emotion", "")

        # Create and connect the TTS client
        tts_client = TTSDuplexClient(
            voice=voice,
            speed_ratio=voice_speed,
            emotion=emotion
        )

        if not await tts_client.connect():
            await websocket.send_json({"error": "Failed to connect to TTS service"})
            return

        # Inform client that connection is ready
        await websocket.send_json({"status": "ready"})

        audio_task = None
        try:
            # Receive chat message from client
            message_data = await websocket.receive_json()

            # Extract messages array from the request
            messages = message_data.get("messages", [])
            if not messages:
                await websocket.send_json({"error": "Messages array is required"})
                return

            last_message = messages[-1]
            if last_message.get("role") != "user":
                await websocket.send_json({"error": "Last message must be from user"})
                return

            # Always generate a new message ID on the server side
            user_message_id = uuid.uuid4().hex
            last_message["id"] = user_message_id

            # Send the generated message ID back to client immediately
            await websocket.send_json({"user_message_id": user_message_id})

            if user_id and last_message:
                db.create_chat_history(
                    message_id=last_message["id"],
                    user_id=user_id,
                    bot_id=bot_id,
                    message=ChatMessageItem(
                        role='user',
                        type=last_message.get("type", "text"),
                        content=last_message.get("content", ""),
                        asr=last_message.get("asr"),
                        tts=last_message.get("tts"),
                        translation=last_message.get("translation"),
                        grammar_analysis=last_message.get("grammar_analysis"),
                        pronunciation_assessment=last_message.get(
                            "pronunciation_assessment"),
                    ).model_dump(),
                )

            # Convert messages to the format expected by the chat completion
            formatted_messages = [
                {
                    "role": msg.get("role", "user"),
                    # For voice messages, use ASR text if available, otherwise use content
                    "content": msg.get("asr") or msg.get("content", "") if msg.get("type") == "audio" else msg.get("content", "")
                } for msg in messages
            ]

            last_formatted_message = formatted_messages[-1]
            if last_formatted_message.get('type', "text") == "audio":
                # If the last message is audio, we need to process it
                if not last_formatted_message.get("asr"):
                    await websocket.send_json({"error": "ASR data is required for audio messages"})
                    return
                # Add ASR content to the last message
                last_formatted_message["content"] = last_formatted_message["asr"]

            formatted_messages[-1] = {
                "role": formatted_messages[-1].get("role", "user"),
                "content": f"""
!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!

${last_formatted_message.get("content", "")}

!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!
!!!IMPORTANT: Keep your response in English!!!

Now you are ready to answer the user's question.
                """
            }
            # Start audio streaming task
            audio_task = asyncio.create_task(
                forward_audio_chunks_for_chat(tts_client, websocket)
            )

            # Generate streaming response from LLM
            full_response = ""
            async for chunk in generate_stream_response(formatted_messages):
                chunk = json.loads(chunk)
                chunk = chunk.get("choices", [{}])[0].get(
                    "delta", {}).get("content", "")
                if not chunk.strip():
                    continue
                # print("Chunk received:", chunk)
                # Send text chunk to client
                await websocket.send_json({
                    "type": "text",
                    "content": chunk
                })

                # Send text chunk to TTS client
                await tts_client.send_text(chunk)
                full_response += chunk

            # Signal end of text input to TTS
            await tts_client.end_input()

            # Wait for audio streaming to complete
            full_audio_response = await audio_task

            # Generate bot message ID on server side
            bot_message_id = uuid.uuid4().hex

            tts_audio_url = upload_bytes(
                full_audio_response, f"{bot_message_id}_tts.mp3")

            await websocket.send_json({
                "type": "complete",
                "bot_message_id": bot_message_id,
                "full_response": full_response,
                "full_audio_response": tts_audio_url
            })
            if user_id:
                db.create_chat_history(
                    message_id=bot_message_id,
                    user_id=user_id,
                    bot_id=bot_id,
                    message=ChatMessageItem(
                        role='assistant',
                        type='text',
                        content=full_response,
                        tts=tts_audio_url
                    ).model_dump(),
                )

        except json.JSONDecodeError:
            await websocket.send_json({"error": "Invalid JSON format"})
            # Cancel audio task if running
            if audio_task and not audio_task.done():
                audio_task.cancel()
                try:
                    await audio_task
                except asyncio.CancelledError:
                    pass
        except Exception as e:
            sentry_sdk.capture_exception(e)
            await websocket.send_json({"error": f"Chat processing error: {str(e)}"})
            # Cancel audio task if running
            if audio_task and not audio_task.done():
                audio_task.cancel()
                try:
                    await audio_task
                except asyncio.CancelledError:
                    pass
    except WebSocketDisconnect:
        # Client disconnected, clean up
        pass
    except json.JSONDecodeError:
        await websocket.send_json({"error": "Invalid JSON format in initialization"})
    except Exception as e:
        # Log the error and notify the client
        sentry_sdk.capture_exception(e)
        try:
            await websocket.send_json({"error": f"Chat with TTS streaming error: {str(e)}"})
        except:
            pass
    finally:
        # Ensure TTS client is closed properly
        if tts_client:
            await tts_client.close()


async def forward_audio_chunks_for_chat(client: TTSDuplexClient, websocket: WebSocket):
    """
    Forwards audio chunks from the TTS client to the WebSocket for chat streaming.
    """
    try:
        full_audio_response = b""
        async for audio_chunk in client.audio_stream():
            # Send audio chunk as JSON with type and base64 encoded data
            chunk_b64 = base64.b64encode(audio_chunk).decode('utf-8')
            full_audio_response += audio_chunk
            await websocket.send_json({
                "type": "audio",
                "data": chunk_b64
            })
        return full_audio_response
    except Exception as e:
        sentry_sdk.capture_exception(e)
        await websocket.send_json({"error": f"Audio streaming error: {str(e)}"})


@app.middleware("http")
async def add_cors_headers(request: Request, call_next):
    # For OPTIONS requests, return a response with CORS headers directly
    if request.method == "OPTIONS":
        response = Response()
        response.headers["Access-Control-Allow-Origin"] = "*"
        response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response.headers["Access-Control-Allow-Headers"] = "Content-Type, X-API-KEY, X-5E-USER"
        response.headers["Access-Control-Allow-Credentials"] = "true"
        return response

    # For other requests, proceed as normal and add CORS headers
    response: Response = await call_next(request)
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, X-API-KEY, X-5E-USER"
    response.headers["Access-Control-Allow-Credentials"] = "true"
    return response


# You can add this template as src/static/tts_websocket.html

@app.get("/tts_websocket")
async def tts_websocket_page(request: Request):
    """
    Serve the TTS WebSocket test page
    """
    return templates.TemplateResponse("tts_websocket.html", {"request": request})


# upload page
@app.get("/upload")
async def upload_page(request: Request):
    """
    Serve the upload page for voice messages or images
    """
    return templates.TemplateResponse("upload.html", {"request": request})


@app.post("/api/upload")
async def upload_file(
    file: UploadFile = File(...),
    auth_data: dict = Depends(verify_api_key)
):
    """
    Upload a file (for voice messages / image) and return a URL to access it
    """
    user_id = auth_data.get("user_id")
    print(f"File upload request from user: {user_id}")
    temp_file_path = None
    try:
        # Generate a unique filename
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = file.filename
        # client already provides a unique filename, but we ensure it starts with 'chatbot_user'
        if not unique_filename.startswith('chatbot_user'):
            unique_filename = f"{str(uuid.uuid4())}{file_extension}"

        # Save the uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as tmp_file:
            temp_file_path = tmp_file.name

        with open(temp_file_path, "wb") as f:
            content = await file.read()
            f.write(content)

        # Upload to storage and get URL
        file_url = upload(temp_file_path, unique_filename)

        return {"url": file_url}

    except Exception as e:
        sentry_sdk.capture_exception(e)
        # Clean up any temporary files that might have been created
        raise HTTPException(
            status_code=500, detail=f"Failed to upload file: {str(e)}")
    finally:
        # Clean up the temporary file if it exists
        if temp_file_path and os.path.exists(temp_file_path):
            os.remove(temp_file_path)


@app.get("/api/chatbot_list", response_model=ChatbotListResponse)
async def get_chatbots(auth_data: dict = Depends(verify_api_key)):
    """
    Get a list of all available chatbots
    """
    user_id = auth_data.get("user_id")
    try:
        chatbots = get_all_chatbots()
        return ChatbotListResponse(chatbots=chatbots)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve chatbots: {str(e)}"
        )


@app.post("/api/chatbot", response_model=ChatbotResponse)
async def create_or_update_chatbot_endpoint(
    request: ChatbotRequest,
    api_key: str = Depends(verify_api_key)
):
    """
    Create a new chatbot or update an existing one
    """
    try:
        chatbot = create_or_update_chatbot(
            request.id,
            request.name,
            request.persona,
            request.bot_type,  # Include bot type
            request.image_url,
            request.voice,
            request.voice_speed,  # Include voice speed
            request.hello_message,  # Include hello message
            request.extra_data  # Include extra data
        )
        return chatbot
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create or update chatbot: {str(e)}"
        )


@app.delete("/api/chatbot/{chatbot_id}", response_model=DeleteChatbotResponse)
async def delete_chatbot_endpoint(
    chatbot_id: str,
    api_key: str = Depends(verify_api_key)
):
    """
    Delete a chatbot by its ID
    """
    try:
        success = delete_chatbot(chatbot_id)
        return {"success": success}
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete chatbot: {str(e)}"
        )


@app.get("/api/voices")
async def get_available_voices(api_key: str = Depends(verify_api_key)):
    """
    Get available voice options for chatbots
    """
    voices = [
        {
            "id": "en_female_amanda_mars_bigtts",
            "name": "Amanda (Female, English)",
            "language": "en-US",
            "gender": "female"
        },
        {
            "id": "en_male_jackson_mars_bigtts",
            "name": "Jackson (Male, English)",
            "language": "en-US",
            "gender": "male"
        },
        {
            "id": "en_female_anna_mars_bigtts",
            "name": "Anna (Female, English)",
            "language": "en-BR",
            "gender": "female"
        },
        {
            "id": "en_male_smith_mars_bigtts",
            "name": "Smith (Male, English)",
            "language": "en-BR",
            "gender": "male"
        }
    ]
    return {"voices": voices}


@app.get("/api/chat_history/{bot_id}", response_model=ChatHistoryResponse)
async def get_chat_history_by_bot(
    bot_id: str,
    timestamp: Optional[str] = Query(
        None, description="ISO format timestamp to get messages before this time"),
    limit: Optional[int] = Query(
        50, description="Maximum number of messages to return"),
    auth_data: dict = Depends(verify_api_key)
):
    """
    Get chat history for a specific bot and user, optionally filtered by timestamp
    """
    user_id = auth_data.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail="User authentication required for this endpoint"
        )

    try:
        before_timestamp = None
        if timestamp:
            try:
                before_timestamp = datetime.fromisoformat(
                    timestamp.replace('Z', '+00:00'))
            except ValueError:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid timestamp format. Use ISO format (e.g., 2023-01-01T00:00:00Z)"
                )

        chat_history = get_bot_chat_history(
            bot_id, user_id, before_timestamp, limit)
        print("chat history:", chat_history)

        return ChatHistoryResponse(
            status="success",
            data=chat_history,
            total_count=len(chat_history)
        )
    except HTTPException:
        raise
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get chat history: {str(e)}"
        )


@app.delete("/api/chat_history/{message_id}", response_model=DeleteChatHistoryResponse)
async def delete_chat_history_message(
    message_id: str,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Delete a specific chat history message
    """
    user_id = auth_data.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail="User authentication required for this endpoint"
        )
    try:
        success = delete_chat_history(user_id, message_id)
        if success:
            return DeleteChatHistoryResponse(
                status="success",
                message=f"Chat history message {message_id} deleted successfully"
            )
        else:
            raise HTTPException(
                status_code=404,
                detail=f"Chat history message {message_id} not found"
            )
    except HTTPException:
        raise
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete chat history message: {str(e)}"
        )


@app.post("/api/mission", response_model=MissionResponse)
async def create_mission(
    request: MissionRequest,
    api_key: str = Depends(verify_api_key)
):
    """
    Create a new mission based on the provided mission details.
    """
    try:
        # Import the mission creation function
        from src.tools.mission import create_missions
        mission_details = request.mission_details
        # Create the mission
        mission = await create_missions(mission_details)
        # Convert mission to dict for storage
        mission_dict = mission.to_dict()

        return MissionResponse(mission=mission_dict)
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/mission/check", response_model=MissionCheckResponse)
async def check_mission_status(
    request: MissionCheckRequest,
    api_key: str = Depends(verify_api_key)
):
    """
    Check the status of a mission based on conversation history.
    """
    try:
        # Import the mission creation function
        from src.tools.mission import check_mission, Mission
        mission_data = request.mission.model_dump()  # Convert to dict for processing
        # print(f"Mission data: {mission_data}")
        # Reconstruct the Mission object
        mission = Mission.from_dict(mission_data)
        conversation_history = request.conversation_history
        mission = await check_mission(mission, conversation_history)
        return MissionCheckResponse(updated_mission=mission.to_dict())
    except Exception as e:
        sentry_sdk.capture_exception(e)
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/chat_history", response_model=CreateChatHistoryResponse)
async def create_chat_history_entry(
    request: CreateChatHistoryRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Create a new chat history entry
    """
    user_id = auth_data.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail="User authentication required for this endpoint"
        )

    try:
        # Process voice message for proper storage
        processed_message = process_voice_message_for_storage(request.message)

        # Convert ChatMessageItem to dict for storage
        message_dict = processed_message.dict()

        # Create the chat history entry
        result = db.create_chat_history(
            message_id=request.message_id,
            bot_id=request.bot_id,
            user_id=user_id,
            message=message_dict
        )

        if result:
            return CreateChatHistoryResponse(
                status="success",
                message_id=request.message_id
            )
        else:
            raise HTTPException(
                status_code=500,
                detail="Failed to create chat history entry"
            )

    except HTTPException:
        raise
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/api/chat_history/sync", response_model=SyncChatHistoryResponse)
async def sync_chat_history(
    request: SyncChatHistoryRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    Sync multiple chat history entries from client with their original created_at timestamps.
    This endpoint allows bulk upload of chat history messages, maintaining the original
    creation timestamps from the client side.
    """
    user_id = auth_data.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=401,
            detail="User authentication required for this endpoint"
        )

    try:
        # Validate request data
        if not request.chat_history:
            return SyncChatHistoryResponse(
                status="success",
                synced_count=0,
                skipped_count=0,
                errors=["No chat history items provided"]
            )

        # Convert request data to format expected by the database function
        chat_history_items = []
        for item in request.chat_history:
            chat_history_items.append({
                "message_id": item.message_id,
                "bot_id": item.bot_id,
                "message": item.message.dict(),
                "created_at": item.created_at
            })

        # Perform bulk sync operation
        result = sync_chat_history_bulk(user_id, chat_history_items)

        return SyncChatHistoryResponse(
            status="success" if not result["errors"] else "partial_success",
            synced_count=result["synced_count"],
            skipped_count=result["skipped_count"],
            errors=result["errors"]
        )

    except HTTPException:
        raise
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to sync chat history: {str(e)}"
        )


def log_user_activity(user_id: Optional[str], action: str, details: str = ""):
    """
    Utility function to log user activities with user_id context
    """
    if user_id:
        print(f"[USER:{user_id}] {action} - {details}")
        # You can also send this to your logging service, database, etc.
        # Example: logger.info(f"User {user_id} performed {action}: {details}")
    else:
        print(f"[ANONYMOUS] {action} - {details}")


@app.get("/llm_chat")
async def llm_chat_page(request: Request):
    """
    Serve the beautiful LLM chat page
    """
    return templates.TemplateResponse("llm_chat.html", {"request": request})


@app.get("/api/llm_chat/models", response_model=LLMChatModelsResponse)
async def get_llm_models(
    api_key: str = Depends(verify_api_key)
):
    """
    Endpoint to retrieve available LLM models.
    """
    try:
        # Import the model retrieval function
        from src.chat import get_available_models

        models = get_available_models()

        return LLMChatModelsResponse(
            object="list",
            data=[LLMChatModel(
                id=model,
                object="model",
                created=0,  # Placeholder, as we don't have creation time for models
                owned_by="doubao",  # Placeholder, as models are system-owned
            ) for model in models])
    except Exception as e:
        sentry_sdk.capture_exception(e)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve LLM models: {str(e)}"
        )


@app.post("/api/llm_chat", response_model=LLMChatResponse)
async def llm_chat_completion(
    chat_request: LLMChatRequest,
    auth_data: dict = Depends(verify_api_key)
):
    """
    OpenAI-compatible chat completion API endpoint with optional streaming support.
    If chat_request.stream is True the response will be streamed.
    """
    user_id = auth_data.get("user_id")

    # Check if streaming is requested
    if getattr(chat_request, "stream", False):

        async def event_generator():
            messages = [{"role": msg.role, "content": msg.content}
                        for msg in chat_request.messages]
            # generate_stream_response returns an async iterator of chunks (which can be formatted as needed)
            async for chunk in generate_stream_response(messages, model=chat_request.model):
                print(f"Streaming chunk: {chunk}")
                # Here, we yield each chunk as an SSE message
                yield f"data: {chunk}\n\n"
                # Simulate some delay for streaming effect
                # await asyncio.sleep(0.3)

        return StreamingResponse(event_generator(), media_type="text/event-stream")
    else:
        try:
            messages = [{"role": msg.role, "content": msg.content}
                        for msg in chat_request.messages]
            response = await generate_response(
                messages,
                model=chat_request.model,
            )
            return response
        except Exception as e:
            sentry_sdk.capture_exception(e)
            raise HTTPException(
                status_code=500,
                detail=f"Chat completion failed: {str(e)}"
            )
