import azure.cognitiveservices.speech as speechsdk
import json
from dotenv import load_dotenv
import os
load_dotenv()


def pronunciation_assessment(audio_file_path, reference_text, language="en-US"):
    # Azure Speech Service configuration
    speech_key = os.getenv("AZURE_SPEECH_KEY")
    # Replace with your region, e.g., "eastus"
    speech_region = os.getenv("AZURE_SPEECH_REGION")

    # Initialize speech configuration
    speech_config = speechsdk.SpeechConfig(
        subscription=speech_key, region=speech_region)
    speech_config.speech_recognition_language = language
    audio_config = speechsdk.audio.AudioConfig(filename=audio_file_path)

    # Configure pronunciation assessment
    pronunciation_config = speechsdk.PronunciationAssessmentConfig(
        # reference_text=reference_text,
        # Scores out of 100
        grading_system=speechsdk.PronunciationAssessmentGradingSystem.HundredMark,
        # Phoneme-level scoring
        granularity=speechsdk.PronunciationAssessmentGranularity.Phoneme,
        enable_miscue=True  # Detects omissions or insertions
    )

    # Create a speech recognizer
    recognizer = speechsdk.SpeechRecognizer(
        speech_config=speech_config,
        audio_config=audio_config
    )

    # Apply pronunciation assessment configuration
    pronunciation_config.apply_to(recognizer)

    # Perform recognition and get pronunciation assessment
    result = recognizer.recognize_once()

    # Prepare JSON response
    response = {
        "status": "",
        "message": "",
        "data": {}
    }

    # Check the result
    if result.reason == speechsdk.ResultReason.RecognizedSpeech:
        # Parse pronunciation assessment result
        pronunciation_result = speechsdk.PronunciationAssessmentResult(result)

        # Extract scores
        response["status"] = "success"
        response["data"] = {
            "recognized_text": result.text,
            "reference_text": reference_text,
            "pronunciation_score": pronunciation_result.pronunciation_score,
            "accuracy_score": pronunciation_result.accuracy_score,
            "fluency_score": pronunciation_result.fluency_score,
            "completeness_score": pronunciation_result.completeness_score,
            "words": []
        }

        # Detailed word-level and phoneme-level results
        for word in pronunciation_result.words:
            word_data = {
                "word": word.word,
                "accuracy_score": word.accuracy_score,
                "error_type": str(word.error_type),
                "phonemes": []
            }

            for phoneme in word.phonemes:
                word_data["phonemes"].append({
                    "phoneme": phoneme.phoneme,
                    "accuracy_score": phoneme.accuracy_score
                })

            response["data"]["words"].append(word_data)

    elif result.reason == speechsdk.ResultReason.NoMatch:
        response["status"] = "error"
        response["message"] = "No speech could be recognized."
    elif result.reason == speechsdk.ResultReason.Canceled:
        cancellation_details = result.cancellation_details
        response["status"] = "error"
        response["message"] = f"Speech recognition canceled: {cancellation_details.reason}"

        if cancellation_details.reason == speechsdk.CancellationReason.Error:
            response["message"] += f" - error_details: {cancellation_details.error_details}"

    return response


def continuous_pronunciation_assessment(audio_file_path, reference_text, language="en-US"):
    """
    Performs pronunciation assessment on longer audio files by using continuous recognition.

    Args:
        audio_file_path (str): Path to the audio file
        reference_text (str): Text that should be spoken in the audio
        language (str): Language code (default: "en-US")

    Returns:
        dict: Assessment results with detailed pronunciation scores
    """
    # Azure Speech Service configuration
    speech_key = os.getenv("AZURE_SPEECH_KEY")
    speech_region = os.getenv("AZURE_SPEECH_REGION")

    # Initialize speech configuration
    speech_config = speechsdk.SpeechConfig(
        subscription=speech_key, region=speech_region)
    speech_config.speech_recognition_language = language
    audio_config = speechsdk.audio.AudioConfig(filename=audio_file_path)

    # Configure pronunciation assessment
    pronunciation_config = speechsdk.PronunciationAssessmentConfig(
        # reference_text=reference_text,
        grading_system=speechsdk.PronunciationAssessmentGradingSystem.HundredMark,
        granularity=speechsdk.PronunciationAssessmentGranularity.Phoneme,
        enable_miscue=True
    )

    # Create a speech recognizer for continuous recognition
    recognizer = speechsdk.SpeechRecognizer(
        speech_config=speech_config,
        audio_config=audio_config
    )

    # Apply pronunciation assessment configuration
    pronunciation_config.apply_to(recognizer)

    # Prepare response structure
    response = {
        "status": "success",
        "message": "",
        "data": {
            "recognized_text": "",
            "reference_text": reference_text,
            "pronunciation_score": 0,
            "accuracy_score": 0,
            "fluency_score": 0,
            "completeness_score": 0,
            "words": []
        }
    }

    # Variables to accumulate scores
    total_pronunciation_score = 0
    total_accuracy_score = 0
    total_fluency_score = 0
    total_completeness_score = 0
    segment_count = 0
    all_text = []
    done = False

    # Set up callbacks for continuous recognition
    def recognized_cb(evt):
        nonlocal total_pronunciation_score, total_accuracy_score, total_fluency_score
        nonlocal total_completeness_score, segment_count, all_text

        pronunciation_result = speechsdk.PronunciationAssessmentResult(
            evt.result)

        # Accumulate scores
        total_pronunciation_score += pronunciation_result.pronunciation_score
        total_accuracy_score += pronunciation_result.accuracy_score
        total_fluency_score += pronunciation_result.fluency_score
        total_completeness_score += pronunciation_result.completeness_score
        segment_count += 1
        all_text.append(evt.result.text)

        # Process words
        for word in pronunciation_result.words:
            word_data = {
                "word": word.word,
                "accuracy_score": word.accuracy_score,
                "error_type": str(word.error_type),
                "phonemes": []
            }

            for phoneme in word.phonemes:
                word_data["phonemes"].append({
                    "phoneme": phoneme.phoneme,
                    "accuracy_score": phoneme.accuracy_score
                })

            response["data"]["words"].append(word_data)

    def canceled_cb(evt):
        nonlocal response, done
        cancellation_details = evt.cancellation_details

        if cancellation_details.reason == speechsdk.CancellationReason.Error:
            response["status"] = "error"
            response["message"] = f"Recognition canceled: {cancellation_details.reason}"
            response["data"]["error_details"] = cancellation_details.error_details

        done = True

    def session_stopped_cb(evt):
        nonlocal done
        done = True

    # Connect callbacks
    recognizer.recognized.connect(recognized_cb)
    recognizer.canceled.connect(canceled_cb)
    recognizer.session_stopped.connect(session_stopped_cb)

    # Start continuous recognition
    recognizer.start_continuous_recognition()

    # Wait for completion
    import time
    while not done:
        time.sleep(0.5)

    # Stop recognition
    recognizer.stop_continuous_recognition()

    # Calculate average scores if segments were recognized
    if segment_count > 0:
        response["data"]["pronunciation_score"] = total_pronunciation_score / segment_count
        response["data"]["accuracy_score"] = total_accuracy_score / segment_count
        response["data"]["fluency_score"] = total_fluency_score / segment_count
        response["data"]["completeness_score"] = total_completeness_score / segment_count
        response["data"]["recognized_text"] = " ".join(all_text)
    else:
        response["status"] = "error"
        response["message"] = "No speech could be recognized."

    return response


if __name__ == "__main__":
    # Example usage
    # audio_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/alex_thomas_pronunciation_raw_sample.wav"
    # reference_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/alex_thomas_pronunciation_raw_sample.md"
    # audio_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/20250415.wav"
    # reference_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/20250415.md"
    audio_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/hello,world.wav"
    reference_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/hello,world.md"
    with open(reference_file, 'r') as f:
        reference_text = f.read().strip()

    # Use continuous mode for longer audio files
    # result = pronunciation_assessment(audio_file, reference_text)
    result = pronunciation_assessment(audio_file, reference_text)
    print(json.dumps(result, indent=2))
