import asyncio
import uuid
import websockets

# Import necessary functions and constants from tts.py
from src.utils.tts import (
    DOUBAO_APP_ID,
    DOUBAO_API_TOKEN,
    start_connection,
    start_session,
    send_text,
    finish_session,
    finish_connection,
    parser_response,
    AUDIO_ONLY_RESPONSE,
    EVENT_ConnectionStarted,
    EVENT_SessionStarted,
    EVENT_TTSResponse,
    EVENT_TTSSentenceStart,
    EVENT_TTSSentenceEnd,
)


async def stream_tts(text: str, voice: str, voice_speed: float = 1.0):
    """
    Implements duplex TTS streaming.
    Connects to the TTS websocket service, sends the text,
    and yields audio chunks as they are received.
    """
    ws_header = {
        "X-Api-App-Key": DOUBAO_APP_ID,
        "X-Api-Access-Key": DOUBAO_API_TOKEN,
        "X-Api-Resource-Id": "volc.service_type.10029",
        "X-Api-Connect-Id": str(uuid.uuid4()),
    }
    url = "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
    async with websockets.connect(url, additional_headers=ws_header, max_size=1000000000) as ws:
        # Establish connection
        await start_connection(ws)
        res = parser_response(await ws.recv())
        if res.optional.event != EVENT_ConnectionStarted:
            raise RuntimeError("Start connection failed")

        # Start TTS session using voice as the speaker
        session_id = uuid.uuid4().hex
        await start_session(ws, voice, session_id, speed_ratio=voice_speed)
        res = parser_response(await ws.recv())
        if res.optional.event != EVENT_SessionStarted:
            raise RuntimeError("Start session failed")

        # Send the text for synthesis and signal end of input
        await send_text(ws, voice, text, session_id, speed_ratio=voice_speed)
        await finish_session(ws, session_id)

        # Yield incoming audio chunks until a non-audio event is received
        while True:
            try:
                res_bytes = await ws.recv()
            except websockets.exceptions.ConnectionClosed:
                break

            res = parser_response(res_bytes)
            if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                yield res.payload
            elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                continue
            else:
                break

        # Close the connection gracefully and return immediately to prevent hanging
        await finish_connection(ws)
        return

# test the streaming TTS function


async def test_stream_tts():
    """
    Test function to demonstrate the TTS streaming functionality.
    This function can be run directly to see how the streaming works.
    """
    text = "Hello, this is a test of the TTS streaming service."
    voice = "en_female_amanda_mars_bigtts"
    async for audio_chunk in stream_tts(text, voice):
        # Here you can process the audio chunk, e.g., save it to a file or play it
        print(f"Received audio chunk of size: {len(audio_chunk)} bytes")


if __name__ == "__main__":
    # Run the test function to see the streaming in action
    asyncio.run(test_stream_tts())
