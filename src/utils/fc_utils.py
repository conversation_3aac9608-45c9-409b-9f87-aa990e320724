# 签名方式参考：https://help.aliyun.com/zh/functioncompute/configure-signature-authentication-for-http-triggers
from datetime import datetime
import os
from urllib.parse import parse_qs, urlparse
from alibabacloud_openapi_util.client import Client as util
from Tea.request import TeaRequest
import requests

access_key_id = os.getenv('ALIYUN_FC_ACCESS_KEY_ID')
access_key_secret = os.getenv('ALIYUN_FC_ACCESS_KEY_SECRET')


def invoke_fc(url, body):
    method = 'POST'
    date = datetime.utcnow().isoformat('T')[:19] + 'Z'
    headers = {
        'x-acs-date': date,
    }
    parsed_url = urlparse(url)
    auth_request = TeaRequest()
    auth_request.method = method
    auth_request.pathname = parsed_url.path.replace('$', '%24')
    auth_request.headers = headers
    auth_request.query = {k: v[0]
                          for k, v in parse_qs(parsed_url.query).items()}

    auth = util.get_authorization(
        auth_request, 'ACS3-HMAC-SHA256', '', access_key_id, access_key_secret)
    headers['authorization'] = auth

    return requests.post(url, json=body, headers=headers)


async def invoke_fc_async(url, body, headers, timeout_seconds=60):
    """
    Asynchronous function to invoke a Function Compute service.
    :param url: The URL of the Function Compute service.
    :param body: The request body to send.
    :return: The response from the Function Compute service.
    """
    import aiohttp
    timeout = aiohttp.ClientTimeout(total=timeout_seconds)

    method = 'POST'
    date = datetime.utcnow().isoformat('T')[:19] + 'Z'
    headers = {
        'x-acs-date': date,
    }
    parsed_url = urlparse(url)
    auth_request = TeaRequest()
    auth_request.method = method
    auth_request.pathname = parsed_url.path.replace('$', '%24')
    auth_request.headers = headers
    auth_request.query = {k: v[0]
                          for k, v in parse_qs(parsed_url.query).items()}

    auth = util.get_authorization(
        auth_request, 'ACS3-HMAC-SHA256', '', access_key_id, access_key_secret)
    headers['authorization'] = auth

    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.post(url, json=body, headers=headers) as response:
            # response.raise_for_status()  # Raise an error for bad responses
            return await response.json()
