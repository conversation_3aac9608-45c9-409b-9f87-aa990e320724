import asyncio
import base64
import json
import tempfile
import aiohttp
from pydub import AudioSegment
import os
import numpy as np
import shutil
import sys

import pydub
import requests

from src.upload import upload
from src.utils.fc_utils import invoke_fc_async
from src.utils.file_utils import check_if_valid_url, detect_file_type


def _check_dependencies():
    """Check if required dependencies are installed."""
    if shutil.which("ffmpeg") is None or shutil.which("ffprobe") is None:
        print("Error: FFmpeg is required but not found.")
        print("Install FFmpeg: brew install ffmpeg")
        return False
    return True


# Run dependency check when module is imported
if not _check_dependencies():
    print("Warning: Some audio functions may not work correctly.")


def change_audio_speed(audio_path, speed):
    if not os.path.exists(audio_path):
        raise FileNotFoundError(f"Audio file {audio_path} not found.")
    if not isinstance(speed, (int, float)) or speed <= 0:
        raise ValueError("Speed must be a positive number.")
    if not audio_path.endswith('.wav'):
        new_audio_path = change_audio_format(audio_path, 'wav')
        os.remove(audio_path)
        audio_path = new_audio_path

    try:
        audio = AudioSegment.from_wav(audio_path)
        new_audio = audio.speedup(playback_speed=speed)
        new_audio.export(audio_path, format="wav")
        return audio_path
    except Exception as e:
        raise RuntimeError(f"Error processing audio file: {e}")


def change_audio_format(audio_path, new_format, samperate=16000, channels=1):
    if not os.path.exists(audio_path):
        raise FileNotFoundError(f"Audio file {audio_path} not found.")
    if not isinstance(new_format, str) or new_format not in ['wav', 'mp3', 'flac']:
        raise ValueError("New format must be one of: 'wav', 'mp3', 'flac'.")
    try:
        audio = AudioSegment.from_file(audio_path)

        # Apply sample rate and channel changes
        if audio.frame_rate != samperate or audio.channels != channels:
            audio = audio.set_frame_rate(samperate).set_channels(channels)

        new_audio_path = os.path.splitext(audio_path)[0] + f".{new_format}"
        audio.export(new_audio_path, format=new_format)
        return new_audio_path
    except Exception as e:
        raise RuntimeError(f"Error processing audio file: {e}")


def get_audio_duration(audio_path):
    if not os.path.exists(audio_path):
        raise FileNotFoundError(f"Audio file {audio_path} not found.")
    try:
        audio = AudioSegment.from_file(audio_path)
        duration = len(audio) / 1000.0  # Convert milliseconds to seconds
        return duration
    except Exception as e:
        raise RuntimeError(f"Error processing audio file: {e}")


def encode_audio_to_base64(audio_content: bytes) -> str:
    """Encode audio bytes to base64 string."""
    return base64.b64encode(audio_content).decode('utf-8')


async def convert_to_16kHz_mono_wav_audio(file_url: str) -> str:
    """
    Convert audio file format using the audio conversion service.

    Args:
        file_url (str): The URL of the audio file to be converted.

    Returns:
        str: The URL of the converted audio file.

    Raises:
        ValueError: If file_url is not provided.
        RuntimeError: If the conversion fails or the service returns an error.
    """
    check_if_valid_url(file_url)

    if os.getenv("ENVIRONMENT") in ["dev", "development", "test"]:
        conversion_service_url = "https://ai-basert-audio-vdpmsysomt.cn-beijing.fcapp.run"
    else:
        conversion_service_url = "https://ai-basert-audio-qxeogndebs.cn-beijing.fcapp.run"

    headers = {
        "Content-Type": "application/json"
    }

    payload = {
        "file_url": file_url
    }

    try:
        result = await invoke_fc_async(conversion_service_url, body=payload, headers=headers, timeout_seconds=600)
        if result.get("success"):
            converted_url = result.get("converted_file_url")
            if converted_url:
                print(
                    f"Successfully converted audio file: {file_url} -> {converted_url}")
                return converted_url
            else:
                raise RuntimeError(
                    "Conversion service returned success but no converted_file_url")
        else:
            raise RuntimeError(
                f"Conversion service returned failure: {result}")

    except aiohttp.ClientError as e:
        raise RuntimeError(
            f"Failed to call conversion service: {str(e)}") from e
    except json.JSONDecodeError as e:
        raise RuntimeError(
            f"Failed to parse conversion service response: {str(e)}") from e
    except (ValueError, RuntimeError):
        raise  # Re-raise our own exceptions
    except Exception as e:
        raise RuntimeError(f"Audio conversion failed: {str(e)}") from e


def convert_to_16kHz_mono_wav_audio_local(file_url: str) -> str:

    # print(f"File URL suffix: {suffix}")
    # download file url to a temporary file
    temp_file_path = None
    with tempfile.NamedTemporaryFile(delete=False) as temp_file:
        response = requests.get(file_url)
        if response.status_code == 200:
            temp_file.write(response.content)
            temp_file.flush()  # Ensure data is written to disk
        else:
            raise Exception(
                f"Failed to download file from {file_url}, status code: {response.status_code}")

        temp_file_path = temp_file.name

    try:
        # print(f"Temporary file created at: {temp_file_path}")

        file_type = detect_file_type(temp_file_path)
        if file_type is None:
            raise ValueError(
                f"Could not detect file type for {temp_file_path}. Please check the file format.")
        if not file_type.startswith('audio/'):
            raise ValueError(
                f"File {temp_file_path} is not an audio file, detected type: {file_type}")

        # check if the file is mp3, 16000Hz, 16bit, mono
        audio = pydub.AudioSegment.from_file(temp_file_path)
        if file_type != 'audio/mpeg' or audio.frame_rate != 16000 or audio.sample_width != 2 or audio.channels != 1:
            # convert to mp3, 16000Hz, 16bit, mono
            audio = (
                audio
                .set_frame_rate(16000)
                .set_sample_width(2)
                .set_channels(1)
            )
            # print(
            # f"Converting file to 16000Hz, 16bit, mono mp3 format: {temp_file_path}")
            audio.export(temp_file_path, format="mp3")
            # print(f"Converted file to {temp_file_path}")
            uploaded_file_url = upload(
                temp_file_path, object_name=os.path.basename(temp_file_path))
            # print(f"Uploaded converted file to {uploaded_file_url}")
            return uploaded_file_url
        else:
            return file_url
    finally:
        # Clean up the temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            os.unlink(temp_file_path)


if __name__ == "__main__":
    # file = "data/audios/5491dc3e-8239-4aba-9d07-cf5b5310799f.aac"
    # print("Original Duration:", get_audio_duration(file))
    # Test the auto_convert_file_format function
    test_url = "https://gusto-ai-base-oss-test.wemore.com/b2679a714c26452b939f656b0a58f774.mp3"
    # test_url = "https://gusto-ai-base-oss-test.wemore.com/99582856fbd349c089b069280ed37722.mp3"
    # test_url = "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/d1b46e5d-b10d-4524-a93f-1df948a777f0.aac"
    # invalid audio file
    # test_url = "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/d00783ab-07f5-4217-8b2f-6867f0d5f926.m4a"
    # test_url = "file:///var/mobile/Containers/Data/Application/FB6DE051-4DF6-4CE9-9648-D06BF8622609/Library/Caches/a27f5394-6968-4ac4-83a0-f30c4fe356c3.m4a"
    # test_url = "https://a1.easemob.com/1128250120193676/gusto-spot-cn/chatfiles/5e3c0a40-5344-11f0-a261-c57b34d4eed0"

    try:
        converted_file_url = asyncio.run(
            # convert_to_16kHz_mono_wav_audio(test_url))
            convert_to_16kHz_mono_wav_audio_local(test_url))
        print(f"Conversion successful: {converted_file_url}")
    except (ValueError, RuntimeError) as e:
        print(f"Conversion failed: {e}")
