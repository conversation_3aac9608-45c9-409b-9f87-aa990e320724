from langdetect import detect, DetectorFactory, LangDetectException
import re

# Set seed for consistent results
DetectorFactory.seed = 0


def detect_language(text):
    """
    Detect the language of the given text.

    Args:
        text (str): The text to analyze.

    Returns:
        str: The detected language code (e.g., 'en' for English).
    """
    if not text or not isinstance(text, str) or not text.strip():
        return "unknown"

    try:
        return detect(text)
    except LangDetectException:
        return "unknown"


def detect_language_percentages(text):
    """
    Analyzes text and returns the percentage distribution of languages.

    Args:
        text (str): The text to analyze.

    Returns:
        dict: Dictionary of language codes and their percentage in the text.
              Example: {'en': 65.2, 'zh': 34.8}
    """
    if not text or not isinstance(text, str) or not text.strip():
        return {"unknown": 100.0}

    # Extract characters by language patterns
    chinese_chars = re.findall(
        "[\u4e00-\u9fff\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002b73f"
        "\U0002b740-\U0002b81f\U0002b820-\U0002ceaf\U0002ceb0-\U0002ebef]",
        text
    )

    # Consider Latin alphabet (approximation for English)
    english_chars = re.findall(r'[a-zA-Z]', text)

    # Other characters (punctuation, spaces, numbers, symbols)
    other_chars = re.findall(
        r'[^a-zA-Z\u4e00-\u9fff\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002b73f'
        r'\U0002b740-\U0002b81f\U0002b820-\U0002ceaf\U0002ceb0-\U0002ebef]', text)

    # Use character counts rather than calculating multiple times
    zh_count = len(chinese_chars)
    en_count = len(english_chars)
    other_count = len(other_chars)

    # print(
    # f"Chinese characters: {zh_count}, English characters: {en_count}, Other characters: {other_count}")

    # Calculate total characters (sum of all categories)
    total_chars = zh_count + en_count + other_count

    # Calculate percentages
    result = {}
    if zh_count > 0:
        result['zh'] = round(zh_count / total_chars * 100, 1)
    if en_count > 0:
        result['en'] = round(en_count / total_chars * 100, 1)
    if other_count > 0:
        result['other'] = round(other_count / total_chars * 100, 1)

    # Normalize percentages to ensure they sum to 100%
    if result:
        # Calculate sum of percentages (might not be exactly 100 due to rounding)
        total_percentage = sum(result.values())

        # If not exactly 100%, adjust the largest percentage category
        if total_percentage != 100.0:
            # Find the category with highest percentage
            max_key = max(result, key=result.get)
            # Adjust to make total exactly 100%
            result[max_key] = round(
                result[max_key] + (100.0 - total_percentage), 1)
    else:
        return {"unknown": 100.0}

    return result


def is_primarily_language(text, language_code, threshold=50.0):
    """
    Determines if text is primarily in the specified language based on character percentage.

    Args:
        text (str): The text to analyze
        language_code (str): Language code to check for ('en', 'zh')
        threshold (float): Percentage threshold to consider primary language (default: 50.0)

    Returns:
        bool: True if the specified language exceeds the threshold percentage
    """
    percentages = detect_language_percentages(text)
    return percentages.get(language_code, 0.0) >= threshold


def get_dominant_language(text):
    """
    Returns the dominant language in the text regardless of threshold.

    Args:
        text (str): The text to analyze

    Returns:
        str: The language code with highest percentage ('en', 'zh', 'other', 'unknown')
    """
    if not text or not isinstance(text, str) or not text.strip():
        return "unknown"

    percentages = detect_language_percentages(text)
    if "unknown" in percentages:
        return "unknown"

    return max(percentages, key=percentages.get)


def is_english(text, threshold=50.0, use_dominant=True):
    """
    Check if the given text is primarily English.

    Args:
        text (str): The text to analyze.
        threshold (float): Percentage threshold to consider English (default: 50.0)
        use_dominant (bool): If True, also returns True when English is the dominant language
                            even if below threshold

    Returns:
        bool: True if the text is primarily English, False otherwise.
    """
    if not text or not isinstance(text, str) or not text.strip():
        return False

    # First check using threshold-based approach
    if is_primarily_language(text, 'en', threshold):
        return True

    # If below threshold, check if it's still the dominant language
    if use_dominant and get_dominant_language(text) == 'en':
        return True

    return False


def is_chinese(text, threshold=50.0, use_dominant=True):
    """
    Check if the given text is primarily Chinese.

    Args:
        text (str): The text to analyze.
        threshold (float): Percentage threshold to consider Chinese (default: 50.0)
        use_dominant (bool): If True, also returns True when Chinese is the dominant language
                            even if below threshold

    Returns:
        bool: True if the text is primarily Chinese, False otherwise.
    """
    if not text or not isinstance(text, str) or not text.strip():
        return False

    # First check using threshold-based approach
    if is_primarily_language(text, 'zh', threshold):
        return True

    # If below threshold, check if it's still the dominant language
    if use_dominant and get_dominant_language(text) == 'zh':
        return True

    return False


if __name__ == "__main__":
    # Example usage
    text = "Hello, world"
    print(text)
    print(f"Detected language: {detect_language(text)}")
    print(f"Language percentages: {detect_language_percentages(text)}")
    print(f"Is English: {is_english(text)}")
    print(f"Is Chinese: {is_chinese(text)}")
    print()

    text_chinese = "你好，世界"
    print(text_chinese)
    print(f"Detected language: {detect_language(text_chinese)}")
    print(f"Language percentages: {detect_language_percentages(text_chinese)}")
    print(f"Is English: {is_english(text_chinese)}")
    print(f"Is Chinese: {is_chinese(text_chinese)}")
    print()

    text_mixed = "Hello, 世界"
    print(text_mixed)
    print(f"Detected language: {detect_language(text_mixed)}")
    print(f"Language percentages: {detect_language_percentages(text_mixed)}")
    print(f"Is English: {is_english(text_mixed)}")
    print(f"Is Chinese: {is_chinese(text_mixed)}")
    print()

    text_mixed_long = "This is a longer text that contains both English and Chinese characters. 你好，世界"
    print(text_mixed_long)
    print(f"Detected language: {detect_language(text_mixed_long)}")
    print(
        f"Language percentages: {detect_language_percentages(text_mixed_long)}")
    print(f"Is English: {is_english(text_mixed_long)}")
    print(f"Is Chinese: {is_chinese(text_mixed_long)}")
    print()

    text_mixed_long = "这是一个包含英语和中文字符的较长文本。Hello"
    print(text_mixed_long)
    print(f"Detected language: {detect_language(text_mixed_long)}")
    print(
        f"Language percentages: {detect_language_percentages(text_mixed_long)}")
    print(f"Is English: {is_english(text_mixed_long)}")
    print(f"Is Chinese: {is_chinese(text_mixed_long)}")
    print()
