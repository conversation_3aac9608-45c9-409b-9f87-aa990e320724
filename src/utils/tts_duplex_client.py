import asyncio
import uuid
import logging
from typing import AsyncGenerator, Optional, List, Dict
import websockets
from websockets.asyncio.client import ClientConnection

# Import necessary functions and constants from tts.py
from src.utils.tts import (
    DOUBAO_APP_ID,
    DOUBAO_API_TOKEN,
    start_connection,
    start_session,
    send_text,
    finish_session,
    finish_connection,
    parser_response,
    AUDIO_ONLY_RESPONSE,
    EVENT_ConnectionStarted,
    EVENT_SessionStarted,
    EVENT_TTSResponse,
    EVENT_TTSSentenceStart,
    EVENT_TTSSentenceEnd,
)

logger = logging.getLogger(__name__)


class TTSDuplexClient:
    """
    A client for bidirectional TTS communication.
    Allows sending text chunks and receiving audio chunks simultaneously.
    """

    def __init__(
        self,
        voice: str,
        speed_ratio: float = 1.0,
        emotion: str = "",
        url: str = "wss://openspeech.bytedance.com/api/v3/tts/bidirection"
    ):
        """
        Initialize the TTS duplex client.

        Args:
            voice: The voice ID to use for synthesis
            speed_ratio: Speed ratio for the synthesized speech (default 1.0)
            emotion: Optional emotion parameter for the voice
            url: WebSocket URL for the TTS service
        """
        self.voice = voice
        self.speed_ratio = speed_ratio
        self.emotion = emotion
        self.url = url
        self.websocket: Optional[ClientConnection] = None
        self.session_id: Optional[str] = None
        self.connected = False
        self.audio_queue = asyncio.Queue()
        self._receive_task = None

    async def connect(self) -> bool:
        """
        Connect to the TTS service and initialize a session.

        Returns:
            bool: True if connection and session initialization were successful
        """
        if self.connected:
            return True

        try:
            # Prepare the WebSocket headers
            ws_headers = {
                "X-Api-App-Key": DOUBAO_APP_ID,
                "X-Api-Access-Key": DOUBAO_API_TOKEN,
                "X-Api-Resource-Id": "volc.service_type.10029",
                "X-Api-Connect-Id": str(uuid.uuid4()),
            }

            # Connect to the WebSocket server
            self.websocket = await websockets.connect(
                self.url,
                additional_headers=ws_headers,
                max_size=1000000000
            )

            # Establish connection
            await start_connection(self.websocket)
            res = parser_response(await self.websocket.recv())
            if res.optional.event != EVENT_ConnectionStarted:
                logger.error("Failed to establish connection with TTS service")
                return False

            # Start a session
            self.session_id = uuid.uuid4().hex
            await start_session(
                self.websocket,
                self.voice,
                self.session_id,
                speed_ratio=self.speed_ratio,
                emotion=self.emotion
            )
            res = parser_response(await self.websocket.recv())
            if res.optional.event != EVENT_SessionStarted:
                logger.error("Failed to start TTS session")
                return False

            # Start background task for receiving audio chunks
            self._receive_task = asyncio.create_task(self._receive_audio())
            self.connected = True
            return True

        except Exception as e:
            logger.exception(f"Error connecting to TTS service: {str(e)}")
            await self.close()
            return False

    async def send_text(self, text: str) -> bool:
        """
        Send a text chunk for synthesis.

        Args:
            text: The text to synthesize

        Returns:
            bool: True if the text was successfully sent
        """
        if not self.connected or not self.websocket:
            if not await self.connect():
                return False

        try:
            await send_text(
                self.websocket,
                self.voice,
                text,
                self.session_id,
                speed_ratio=self.speed_ratio,
                emotion=self.emotion
            )
            return True
        except Exception as e:
            logger.exception(f"Error sending text to TTS service: {str(e)}")
            return False

    async def end_input(self) -> bool:
        """
        Signal the end of text input to the TTS service.

        Returns:
            bool: True if the end signal was successfully sent
        """
        if not self.connected or not self.websocket:
            return False

        try:
            await finish_session(self.websocket, self.session_id)
            return True
        except Exception as e:
            logger.exception(f"Error finishing TTS session: {str(e)}")
            return False

    async def _receive_audio(self):
        """
        Background task that receives audio chunks and places them in the queue.
        """
        if not self.websocket:
            return

        try:
            while True:
                try:
                    res_bytes = await self.websocket.recv()
                except websockets.exceptions.ConnectionClosed:
                    break

                res = parser_response(res_bytes)
                if res.optional.event == EVENT_TTSResponse and res.header.message_type == AUDIO_ONLY_RESPONSE:
                    await self.audio_queue.put(res.payload)
                elif res.optional.event in [EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                    continue
                else:
                    # End of audio stream
                    await self.audio_queue.put(None)  # Signal end of stream
                    break

        except Exception as e:
            logger.exception(f"Error in audio receiving task: {str(e)}")
            await self.audio_queue.put(None)  # Signal error condition

    async def get_audio_chunk(self) -> Optional[bytes]:
        """
        Get the next available audio chunk.

        Returns:
            Optional[bytes]: Audio chunk data or None if the stream has ended
        """
        return await self.audio_queue.get()

    async def audio_stream(self) -> AsyncGenerator[bytes, None]:
        """
        Generator that yields audio chunks as they become available.

        Yields:
            bytes: Audio chunk data
        """
        while True:
            chunk = await self.audio_queue.get()
            if chunk is None:  # End of stream
                break
            yield chunk

    async def close(self):
        """
        Close the connection to the TTS service.
        """
        if self.connected and self.websocket:
            try:
                # Try to gracefully close the connection
                await finish_connection(self.websocket)
            except Exception as e:
                logger.exception(f"Error closing TTS connection: {str(e)}")

        # Always cancel the receiving task and clean up
        if self._receive_task and not self._receive_task.done():
            self._receive_task.cancel()
            try:
                await self._receive_task
            except asyncio.CancelledError:
                pass

        # Close the websocket
        if self.websocket:
            try:
                await self.websocket.close()
            except Exception as e:
                logger.exception(f"Error closing websocket: {str(e)}")

        # Clear the audio queue to prevent memory buildup
        while not self.audio_queue.empty():
            try:
                self.audio_queue.get_nowait()
            except asyncio.QueueEmpty:
                break

        # Reset state
        self.connected = False
        self.websocket = None
        self.session_id = None
        self._receive_task = None

    async def __aenter__(self):
        """
        Support for async context manager protocol.
        """
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        Support for async context manager protocol.
        """
        await self.close()


# Example usage function
async def example_usage():
    """
    Example of how to use the TTSDuplexClient class.
    """
    # Create a client
    client = TTSDuplexClient(voice="zh_female_qingqing_emo_v2_mars_bigtts")

    try:
        # Connect to the service
        if not await client.connect():
            print("Failed to connect to TTS service")
            return

        # Send some text
        await client.send_text("你好，这是一个测试。")
        await client.send_text("我是一个语音合成服务。")

        # Signal end of text input
        await client.end_input()

        # Process audio chunks as they arrive
        async for audio_chunk in client.audio_stream():
            # In a real application, you might write to a file or send to an audio player
            print(f"Received audio chunk: {len(audio_chunk)} bytes")

    finally:
        # Make sure to close the connection
        await client.close()


if __name__ == "__main__":
    # Run the example
    asyncio.run(example_usage())
