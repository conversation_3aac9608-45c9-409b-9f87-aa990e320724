"""
日志配置工具
"""

import logging
import os
import socket
import sys
from typing import Dict, Optional

from aliyun.log import LogClient, LogItem, PutLogsRequest

aliyun_sls_enable = os.getenv("ALIYUN_SLS_ENABLE", "false").lower() == "true"
aliyun_sls_endpoint = os.getenv("ALIYUN_SLS_ENDPOINT", "")
aliyun_sls_project = os.getenv("ALIYUN_SLS_PROJECT", "")
aliyun_sls_log_store = os.getenv("ALIYUN_SLS_LOG_STORE", "")
aliyun_sls_access_key_id = os.getenv("ALIYUN_SLS_ACCESS_KEY_ID", "")
aliyun_sls_access_key_secret = os.getenv("ALIYUN_SLS_ACCESS_KEY_SECRET", "")

# 全局标记，确保只配置一次根logger
_root_logger_configured = False


def setup_root_logger(level: str = "INFO", format_string: Optional[str] = None) -> None:
    global _root_logger_configured

    if _root_logger_configured:
        return

    if format_string is None:
        format_string = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

    root_logger = logging.getLogger()

    # 清除现有的处理器，重新配置
    root_logger.handlers.clear()

    # 控制台处理器
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(logging.Formatter(format_string))
    root_logger.addHandler(handler)
    root_logger.setLevel(getattr(logging, level.upper()))

    # SLS处理器
    if aliyun_sls_enable:
        sls_handler = SLSHandler()
        formatter = logging.Formatter(format_string)
        sls_handler.setFormatter(formatter)
        root_logger.addHandler(sls_handler)

    setup_uvicorn_loggers(root_logger.handlers)

    root_logger.info("日志已配置")
    _root_logger_configured = True


def setup_uvicorn_loggers(handlers):
    """配置 uvicorn 的日志处理器"""
    # 配置 uvicorn.access logger
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.handlers.clear()
    uvicorn_access.handlers.extend(handlers)
    uvicorn_access.propagate = False
    # 配置 uvicorn logger
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.handlers.clear()
    uvicorn_logger.handlers.extend(handlers)
    uvicorn_logger.propagate = False
    # 配置 uvicorn.error logger
    uvicorn_error = logging.getLogger("uvicorn.error")
    uvicorn_error.handlers.clear()
    uvicorn_error.handlers.extend(handlers)
    uvicorn_error.propagate = False


class SLSHandler(logging.Handler):
    """阿里云SLS日志处理器"""

    def __init__(self):
        super().__init__()
        self.client = None
        self.hostname = socket.gethostname()
        self.service_name = "gusto-table-manager"

        try:
            self.client = LogClient(  # type: ignore
                aliyun_sls_endpoint, aliyun_sls_access_key_id, aliyun_sls_access_key_secret
            )
            logging.getLogger(__name__).info("SLS日志处理器初始化成功")
        except Exception as e:
            logging.getLogger(__name__).error(f"SLS日志处理器初始化失败: {e}")

    def emit(self, record: logging.LogRecord):
        """发送日志记录到SLS"""
        if not self.client:
            return

        try:
            # 构建日志内容
            log_data = self._build_log_data(record)

            # 创建日志项
            log_item = LogItem()  # type: ignore
            log_item.set_time(int(record.created))
            log_item.set_contents(list(log_data.items()))

            # 发送到SLS
            request = PutLogsRequest(  # type: ignore
                aliyun_sls_project,
                aliyun_sls_log_store,
                "",  # topic
                "",  # source
                [log_item],
            )

            self.client.put_logs(request)

        except Exception as e:
            # 避免日志处理器本身的错误影响主程序
            print(f"SLS日志发送失败: {e}")

    def _build_log_data(self, record: logging.LogRecord) -> Dict[str, str]:
        """构建日志数据"""
        log_data = {
            "level": record.levelname,
            "logger": record.name,
            "message": self.format(record),
            "hostname": self.hostname,
            "service": self.service_name,
            "location": f"{record.module}.{record.funcName}:{record.lineno}",
        }

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)  # type: ignore

        # 添加额外字段
        if hasattr(record, "extra_fields"):
            log_data.update(record.extra_fields)  # type: ignore

        return log_data
