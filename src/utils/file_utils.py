import mimetypes
import os
import subprocess
import tempfile
import json

import requests

from src.exceptions import InvalidUrlError


def detect_file_type(file_path):
    """
    Detect file type using Python's built-in mimetypes module.
    Returns the MIME type of the file based on its extension.
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"

        # Get MIME type based on file extension
        mime_type, _ = mimetypes.guess_type(file_path)

        if mime_type:
            return mime_type
        else:
            # If mimetypes can't determine, try to read file signature for common types
            return _detect_by_signature(file_path)

    except (OSError, IOError) as e:
        return f"Error detecting file type: {e}"


def _detect_by_signature(file_path):
    """
    Detect file type by reading file signature (magic numbers).
    This is a fallback method for when mimetypes can't determine the type.
    """
    try:
        with open(file_path, 'rb') as f:
            header = f.read(16)  # Read first 16 bytes

        # Common file signatures
        signatures = {
            b'\x89PNG\r\n\x1a\n': 'image/png',
            b'\xff\xd8\xff': 'image/jpeg',
            b'GIF87a': 'image/gif',
            b'GIF89a': 'image/gif',
            b'RIFF': 'audio/wav',  # Could also be video/avi, but we'll assume audio
            b'ID3': 'audio/mpeg',
            b'\xff\xfb': 'audio/mpeg',  # MP3
            b'\xff\xf3': 'audio/mpeg',  # MP3
            b'\xff\xf2': 'audio/mpeg',  # MP3
            b'ftyp': 'video/mp4',  # MP4 (starts at offset 4)
            b'%PDF': 'application/pdf',
        }

        # Check for MP4/M4A (ftyp signature is at offset 4)
        if len(header) >= 8 and header[4:8] == b'ftyp':
            if len(header) >= 12:
                subtype = header[8:12]
                if subtype in [b'M4A ', b'mp42', b'isom']:
                    return 'audio/mp4'  # M4A files
                else:
                    return 'video/mp4'

        # Check other signatures
        for signature, mime_type in signatures.items():
            if header.startswith(signature):
                return mime_type

        # Default fallback
        return 'application/octet-stream'

    except (OSError, IOError) as e:
        return f"Error reading file signature: {e}"


def check_if_valid_url(file_url: str):
    """
    Check if the provided file URL is valid and accessible.

    Args:
        file_url (str): The URL of the audio file to be checked.

    Returns:
        None: If the URL is valid and accessible.

    Raises:
        ValueError: If the URL is invalid or inaccessible.
    """
    if not file_url:
        raise InvalidUrlError("file_url must be provided: " + file_url)
    # check if the file url is valid: https:// or http://
    if not (file_url.startswith("https://") or file_url.startswith("http://")):
        raise InvalidUrlError(
            "file_url must start with 'https://' or 'http://'. Please provide a valid URL: " + file_url)

    try:
        # First, do a quick HEAD request to check if URL is accessible
        head_response = requests.head(
            file_url, allow_redirects=True, timeout=10)
        if head_response.status_code != 200:
            raise InvalidUrlError(
                f"File URL is not accessible. Status code: {head_response.status_code}. URL: {file_url}")

    except requests.RequestException as e:
        raise InvalidUrlError(
            f"Failed to access the file URL: {file_url}. Error: {str(e)}") from e
    except InvalidUrlError as e:
        raise e
    except subprocess.TimeoutExpired as e:
        raise ValueError("ffprobe analysis timed out") from e
    except json.JSONDecodeError as e:
        raise ValueError(f"Failed to parse ffprobe output: {str(e)}") from e
    except Exception as e:
        raise ValueError(
            f"Unexpected error during validation: {str(e)}") from e


if __name__ == "__main__":
    # Test file type detection
    file = "data/audios/hello,world.mp3"
    print("File type:", detect_file_type(file))

    # Test URL validation with ffprobe
    # test_url = "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/db9639e3-97ff-4785-8c30-2e6756b5097a.m4a"
    # test_url = "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/d55215af-8320-4c13-85cf-4a78669e97fd.mp3"
    test_url = "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/d8tyfrw9y.wav"
    try:
        check_if_valid_url(test_url)
    except ValueError as e:
        print(f"URL validation failed: {e}")
