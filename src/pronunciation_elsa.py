"""
FastAPI server for Elsa Unscripted API integration.
This server provides endpoints to interact with <PERSON>'s pronunciation assessment API.
"""

import asyncio
import json
import os
from typing import Optional
from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
import httpx
import base64
from dotenv import load_dotenv
import uvicorn
from enum import Enum

from src.upload import upload
from src.utils.audio_utils import convert_to_16kHz_mono_wav_audio_local

# Load environment variables
load_dotenv()

# Configuration
ELSA_API_BASE_URL = "https://api.elsanow.io/api/v1"
ELSA_API_V2_BASE_URL = "https://api.elsanow.io/api/v2"
ELSA_API_TOKEN = os.getenv("ELSA_API_TOKEN")

# Enums


class ApiPlan(str, Enum):
    STANDARD = "standard"
    PREMIUM = "premium"
    ASR = "asr"


class AudioFormat(str, Enum):
    MP3 = "mp3"
    WAV = "wav"
    FLAC = "flac"
    MP4 = "mp4"
    M4A = "m4a"


# Helper functions
async def make_elsa_request(
    method: str,
    endpoint: str,
    headers: dict = None,
    data: dict = None,
    files: dict = None,
    params: dict = None
) -> dict:
    """Make HTTP request to Elsa API with proper error handling."""
    url = f"{ELSA_API_BASE_URL}/{endpoint}"

    default_headers = {
        "Authorization": f"ELSA {ELSA_API_TOKEN}",
        "Accept": "application/json"
    }

    if headers:
        default_headers.update(headers)

    async with httpx.AsyncClient(timeout=120.0) as client:
        try:
            if method.upper() == "GET":
                response = await client.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = await client.post(
                    url,
                    headers=default_headers,
                    data=data,
                    files=files,
                    params=params
                )
            else:
                raise HTTPException(
                    status_code=400, detail="Unsupported HTTP method")

            response.raise_for_status()

            # Try to return JSON, otherwise return text
            try:
                return response.json()
            except (ValueError, TypeError):
                return {"response": response.text}

        except httpx.HTTPStatusError as e:
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Elsa API error: {e.response.text}"
            ) from e
        except httpx.TimeoutException as e:
            raise HTTPException(
                status_code=504, detail="Request to Elsa API timed out") from e
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Internal server error: {str(e)}") from e


async def make_elsa_v2_request(
    method: str,
    endpoint: str,
    headers: dict = None,
    data: dict = None,
    files: dict = None,
    params: dict = None
) -> dict:
    """Make HTTP request to Elsa V2 API with proper error handling."""
    url = f"{ELSA_API_V2_BASE_URL}/{endpoint}"

    default_headers = {
        "Authorization": f"ELSA {ELSA_API_TOKEN}",
        "Accept": "application/json"
    }

    if headers:
        default_headers.update(headers)

    async with httpx.AsyncClient(timeout=120.0) as client:
        try:
            if method.upper() == "GET":
                response = await client.get(url, headers=default_headers, params=params)
            elif method.upper() == "POST":
                response = await client.post(
                    url,
                    headers=default_headers,
                    data=data,
                    files=files,
                    params=params
                )
            else:
                raise HTTPException(
                    status_code=400, detail="Unsupported HTTP method")

            response.raise_for_status()

            # Try to return JSON, otherwise return text
            try:
                return response.json()
            except (ValueError, TypeError):
                return {"response": response.text}

        except httpx.HTTPStatusError as e:
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"Elsa API error: {e.response.text}"
            ) from e
        except httpx.TimeoutException as e:
            raise HTTPException(
                status_code=504, detail="Request to Elsa API timed out") from e
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Internal server error: {str(e)}") from e


async def pronunciation_assessment(
    return_json: bool = True,
    sync: bool = True,
    force_grammar_vocab: bool = False,
    audio_file: Optional[UploadFile] = None,
    audio_path: Optional[str] = None,
    audio_data: Optional[str] = None
):
    """
    Direct implementation of Elsa's score_audio_plus endpoint.
    This endpoint mirrors the exact Elsa API structure for unscripted assessment.

    Parameters:
    - api_plan: "standard" or "premium" (required)
    - return_json: true/false (required)
    - sync: true/false (optional, default: true)
    - force_grammar_vocab: true/false (optional, default: false)
    - Audio input (exactly one required):
      - audio_file: Upload audio file directly
      - audio_path: URL to audio file
      - audio_data: Base64-encoded audio data

    Returns:
    - JSON response with assessment results if return_json=true
    - Download link if return_json=false
    """

    # Validate input - exactly one audio source should be provided
    audio_sources = [audio_file, audio_path, audio_data]
    provided_sources = [src for src in audio_sources if src is not None]
    print(f"Provided audio sources: {provided_sources}")

    if len(provided_sources) != 1:
        raise HTTPException(
            status_code=400,
            detail="Exactly one audio source must be provided: audio_file, audio_path, or audio_data"
        )

    # Prepare request data
    data = {
        "api_plan": ApiPlan.PREMIUM.value,  # Default to premium plan
        "return_json": str(return_json).lower(),
        "sync": str(sync).lower()
    }

    if force_grammar_vocab:
        data["force_grammar_vocab"] = "true"

    files = None

    if audio_file:
        # Handle file upload
        audio_content = await audio_file.read()
        files = {"audio_file": (audio_file.filename,
                                audio_content, audio_file.content_type)}
    elif audio_path:
        # Handle URL (note: parameter name is audio_path for this endpoint)
        data["audio_path"] = audio_path
    elif audio_data:
        # Handle base64 data
        data["audio_data"] = audio_data

    # Make request to Elsa API
    result = await make_elsa_request(
        method="POST",
        endpoint="score_audio_plus",
        data=data,
        files=files
    )

    return result

if __name__ == "__main__":

    async def test():
        audio_file = "data/audios/hello,world.wav"
        # Upload the file to get a URL if needed
        uploaded_file_url = upload(audio_file)
        print(f"Uploaded file URL: {uploaded_file_url}")
        converted_file_url = convert_to_16kHz_mono_wav_audio_local(
            uploaded_file_url)

        result = await pronunciation_assessment(
            return_json=True,
            sync=True,
            force_grammar_vocab=False,
            # audio_file=audio_file,
            audio_path=converted_file_url,
            # audio_data=audio_data
        )

        print(json.dumps(result, indent=2, ensure_ascii=False))

    asyncio.run(test())
