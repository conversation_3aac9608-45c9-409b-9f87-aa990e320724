import tempfile
import websocket
import datetime
import hashlib
import base64
import hmac
import json
from urllib.parse import urlencode
import time
import ssl
from wsgiref.handlers import format_date_time
from datetime import datetime
from time import mktime
import threading
from dotenv import load_dotenv
import os

load_dotenv()

# XFYun API Configuration
XFYUN_APP_ID = os.getenv("XFYUN_APP_ID", "f6c191f0")  # Default from test.py
XFYUN_API_SECRET = os.getenv(
    "XFYUN_API_SECRET", "YTA3YTUzY2M1MTAzZTlmYWZhMjIzNTg0")
XFYUN_API_KEY = os.getenv("XFYUN_API_KEY", "dcfdda29d789e6e39d945615b87dc7d5")

HOST_URL = "ws://ise-api.xfyun.cn/v2/open-ise"


class XFYunPronunciationAssessment:
    def __init__(self, app_id=None, api_secret=None, api_key=None):
        self.app_id = app_id or XFYUN_APP_ID
        self.api_secret = api_secret or XFYUN_API_SECRET
        self.api_key = api_key or XFYUN_API_KEY
        self.result = None
        self.error = None
        self.is_complete = False
        self.lock = threading.Lock()

    def _generate_auth_url(self):
        """Generate authenticated WebSocket URL"""
        now_time = datetime.now()
        now_date = format_date_time(mktime(now_time.timetuple()))

        # Build authentication string
        origin_base = f"host: ise-api.xfyun.cn\n"
        origin_base += f"date: {now_date}\n"
        origin_base += "GET /v2/open-ise HTTP/1.1"

        # SHA256 encryption
        signature_sha = hmac.new(
            self.api_secret.encode('utf-8'),
            origin_base.encode('utf-8'),
            digestmod=hashlib.sha256
        ).digest()
        signature_sha = base64.b64encode(
            signature_sha).decode(encoding='utf-8')

        authorization_origin = f'api_key="{self.api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha}"'
        authorization = base64.b64encode(
            authorization_origin.encode('utf-8')).decode(encoding='utf-8')

        # Combine authentication parameters
        auth_params = {
            "authorization": authorization,
            "date": now_date,
            "host": "ise-api.xfyun.cn"
        }

        return HOST_URL + '?' + urlencode(auth_params)

    def _on_message(self, ws, message):
        """Handle WebSocket message"""
        try:
            response = json.loads(message)
            if response.get("code") != 0:
                self.error = f"Error from XFYun: {response.get('message', 'Unknown error')}"
                with self.lock:
                    self.is_complete = True
                ws.close()
                return
            # print(f"Received message: {response}")
            status = response["data"]["status"]

            if status == 2:  # Assessment complete
                # Decode the XML result
                xml_data = base64.b64decode(response["data"]["data"])
                xml_result = xml_data.decode("utf-8")

                # Parse the result and convert to our format
                self.result = self._parse_xfyun_result(xml_result)
                with self.lock:
                    self.is_complete = True
                ws.close()

        except Exception as e:
            self.error = f"Error parsing response: {str(e)}"
            with self.lock:
                self.is_complete = True

    def _on_error(self, ws, error):
        """Handle WebSocket error"""
        self.error = f"WebSocket error: {str(error)}"
        with self.lock:
            self.is_complete = True

    def _on_close(self, ws, close_status_code, close_msg):
        """Handle WebSocket close"""
        with self.lock:
            self.is_complete = True

    def _on_open(self, ws, reference_text, audio_file_path):
        """Handle WebSocket open and send assessment request"""
        try:
            # Send initial frame with text
            send_dict = {
                "common": {
                    "app_id": self.app_id
                },
                "business": {
                    "category": "read_sentence",
                    "rstcd": "utf8",
                    "sub": "ise",
                    "group": "pupil",
                    "ent": "en_vip",
                    "tte": "utf-8",
                    "cmd": "ssb",
                    "auf": "audio/L16;rate=16000",
                    "aue": "lame",
                    "text": '\uFEFF' + f"[content]\n{reference_text}"
                },
                "data": {
                    "status": 0,
                    "data": ""
                }
            }
            ws.send(json.dumps(send_dict))

            # Send audio data
            with open(audio_file_path, "rb") as audio_file:
                while True:
                    buffer = audio_file.read(1280)
                    if not buffer:
                        # Send final frame
                        final_dict = {
                            "business": {"cmd": "auw", "aus": 4, "aue": "lame"},
                            "data": {"status": 2, "data": str(base64.b64encode(buffer).decode())}
                        }
                        ws.send(json.dumps(final_dict))
                        break

                    # Send audio frame
                    audio_dict = {
                        "business": {
                            "cmd": "auw",
                            "aus": 1,
                            "aue": "lame"
                        },
                        "data": {
                            "status": 1,
                            "data": str(base64.b64encode(buffer).decode()),
                            "data_type": 1,
                            "encoding": "raw"
                        }
                    }
                    ws.send(json.dumps(audio_dict))
                    time.sleep(0.04)

        except Exception as e:
            self.error = f"Error sending audio: {str(e)}"
            with self.lock:
                self.is_complete = True

    def _parse_xfyun_result(self, xml_content):
        """Parse iFlytek XML result with detailed pronunciation analysis."""
        try:
            import xml.etree.ElementTree as ET

            root = ET.fromstring(xml_content)

            # Navigate to the sentence element
            read_chapter = root.find('.//read_chapter')
            if read_chapter is None:
                return None

            result = {
                'content': read_chapter.get('content', ''),
                # Convert to 100 scale
                'accuracy_score': round(float(read_chapter.get('accuracy_score', 0)) * 20),
                'fluency_score': round(float(read_chapter.get('fluency_score', 0)) * 20),
                'total_score': round(float(read_chapter.get('total_score', 0)) * 20),
                'completeness_score': round(float(read_chapter.get('integrity_score', 0)) * 20),
                'pronunciation_score': round(float(read_chapter.get('standard_score', 0)) * 20),
                'word_count': int(read_chapter.get('word_count', 0)),
                'words': []
            }

            sentence = root.find('.//sentence')
            if sentence is None:
                return {"data": result}

            # Parse each word
            for word_elem in sentence.findall('word'):
                word_data = {
                    'word': word_elem.get('content', ''),
                    # Convert to 100 scale
                    'accuracy_score': round(float(word_elem.get('total_score', 0)) * 20),
                    'beg_pos': int(word_elem.get('beg_pos', 0)),
                    'end_pos': int(word_elem.get('end_pos', 0)),
                    'pitch': word_elem.get('pitch', '').strip(),
                    'phonemes': []
                }

                # Parse syllables
                for syll_elem in word_elem.findall('syll'):
                    syll_data = {
                        'phoneme': syll_elem.get('content', ''),
                        # Convert to 100 scale
                        'accuracy_score': round(float(syll_elem.get('syll_score', 0)) * 20),
                        'syll_accent': int(syll_elem.get('syll_accent', 0)),
                        'phones': []
                    }

                    # Parse phones
                    for phone_elem in syll_elem.findall('phone'):
                        phone_data = {
                            'content': phone_elem.get('content', ''),
                            'beg_pos': int(phone_elem.get('beg_pos', 0)),
                            'end_pos': int(phone_elem.get('end_pos', 0)),
                            # Keep gwpp as is (it's already a ratio)
                            'gwpp': float(phone_elem.get('gwpp', 0))
                        }
                        syll_data['phones'].append(phone_data)

                    word_data['phonemes'].append(syll_data)

                result['words'].append(word_data)

            return {"data": result}

        except ET.ParseError as e:
            print(f"XML parsing error: {e}")
            return None
        except Exception as e:
            print(f"Error parsing iFlytek result: {e}")
            return None

    def assess(self, audio_file_path, reference_text):
        """Perform pronunciation assessment"""
        self.result = None
        self.error = None
        self.is_complete = False

        try:
            ws_url = self._generate_auth_url()

            def on_open_wrapper(ws):
                self._on_open(ws, reference_text, audio_file_path)

            websocket.enableTrace(False)
            ws = websocket.WebSocketApp(
                ws_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=on_open_wrapper
            )

            # Run WebSocket in a separate thread
            def run_websocket():
                ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})

            ws_thread = threading.Thread(target=run_websocket)
            ws_thread.daemon = True
            ws_thread.start()

            # Wait for completion with timeout
            timeout = 30  # 30 seconds timeout
            start_time = time.time()
            while not self.is_complete and (time.time() - start_time) < timeout:
                time.sleep(0.1)

            if not self.is_complete:
                return {
                    "status": "error",
                    "message": "Assessment timeout",
                    "data": {}
                }

            if self.error:
                return {
                    "status": "error",
                    "message": self.error,
                    "data": {}
                }

            if self.result:
                # Set the reference text in the result
                self.result["data"]["reference_text"] = reference_text
                self.result["data"]["recognized_text"] = reference_text
                self.result["status"] = "success"
                return self.result

            return {
                "status": "error",
                "message": "No result received",
                "data": {}
            }

        except Exception as e:
            import traceback
            traceback.print_exc()
            return {
                "status": "error",
                "message": f"Assessment failed: {str(e)}",
                "data": {}
            }


def pronunciation_assessment(audio_file_path, reference_text, language="en-US"):
    """
    XFYun pronunciation assessment function with same interface as Azure version

    Args:
        audio_file_path (str): Path to the audio file
        reference_text (str): Text that should be spoken in the audio
        language (str): Language code (currently only en-US supported by XFYun)

    Returns:
        dict: Assessment results with detailed pronunciation scores
    """
    assessor = XFYunPronunciationAssessment()
    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_audio_file:
        # Convert audio file to required format if needed
        if not _convert_audio_file(audio_file_path, temp_audio_file.name, target_format="mp3", sample_rate=16000, channels=1):
            return {
                "status": "error",
                "message": "Audio conversion failed",
                "data": {}
            }
        audio_file_path = temp_audio_file.name
        return assessor.assess(audio_file_path, reference_text)


def continuous_pronunciation_assessment(audio_file_path, reference_text, language="en-US"):
    """
    XFYun continuous pronunciation assessment (currently same as single assessment)

    Args:
        audio_file_path (str): Path to the audio file
        reference_text (str): Text that should be spoken in the audio
        language (str): Language code (currently only en-US supported by XFYun)

    Returns:
        dict: Assessment results with detailed pronunciation scores
    """
    # XFYun doesn't have separate continuous mode, use regular assessment
    return pronunciation_assessment(audio_file_path, reference_text, language)

# convert audio file if it isn't Sampling rate 16K, 16bit, mono MP3 format


def _convert_audio_file(input_path: str, output_path: str, target_format: str = "wav", sample_rate: int = 16000, channels: int = 1) -> bool:
    """
    Convert audio file to specified format and parameters using Python libraries.

    Args:
        input_path: Path to input audio file
        output_path: Path for converted audio file
        target_format: Target audio format (default: wav)
        sample_rate: Target sample rate in Hz (default: 16000)
        channels: Number of audio channels (default: 1 for mono)

    Returns:
        bool: True if conversion successful, False otherwise
    """
    try:
        from pydub import AudioSegment
        import os

        # Load audio file
        audio = AudioSegment.from_file(input_path)

        # Convert to mono if needed
        if channels == 1 and audio.channels > 1:
            audio = audio.set_channels(1)
        elif channels == 2 and audio.channels == 1:
            audio = audio.set_channels(2)

        # Resample if needed
        if audio.frame_rate != sample_rate:
            audio = audio.set_frame_rate(sample_rate)

        # Export in target format
        if target_format.lower() == "wav":
            audio.export(output_path, format="wav")
        elif target_format.lower() == "mp3":
            audio.export(output_path, format="mp3")
        elif target_format.lower() == "flac":
            audio.export(output_path, format="flac")
        else:
            audio.export(output_path, format=target_format.lower())

        # Verify output file exists
        if os.path.exists(output_path):
            print(
                f"Successfully converted {input_path} to {output_path}")
            return True
        else:
            print(
                f"Conversion failed: output file {output_path} not created")
            return False

    except ImportError:
        print("pydub not installed. Run: pip install pydub")
        return False
    except Exception as e:
        print(f"Error during audio conversion: {e}")
        return False


def test_parse_xml_content():
    xml_result = """
  <xml_result>
      <read_sentence lan="en" type="study" version="7.0.0.1020">
          <rec_paper>
              <read_chapter accuracy_score="4.920642" beg_pos="0" content="Hello, world!" end_pos="200" except_info="0" fluency_score="5.000000" integrity_score="5.000000" is_rejected="false" reject_type="0" score_pattern="loose" standard_score="4.095460" total_score="4.944449" word_count="2">
                  <sentence accuracy_score="4.670642" beg_pos="0" content="hello, world" end_pos="200" fluency_score="4.782866" index="0" standard_score="3.922730" total_score="4.704309" word_count="2">
                      <word beg_pos="45" content="hello" dp_message="0" end_pos="94" global_index="0" index="0" pitch="  131.08  131.08  129.52  130.81  131.83  131.49  132.61  134.40  135.47  135.39  134.43  134.31  134.47  134.95  135.33  135.70  136.02  136.38  136.36  136.15  136.23  135.65  135.51  134.87  134.44  133.58  132.26  131.09  129.40  128.28  127.53  127.06  126.37  125.32" pitch_beg="60" pitch_end="94" property="12" total_score="4.917168">
                          <syll beg_pos="45" content="hh eh" end_pos="60" serr_msg="0" syll_accent="0" syll_score="4.987514">
                              <phone beg_pos="45" content="hh" dp_message="0" end_pos="57" gwpp="-0.000498"></phone>
                              <phone beg_pos="57" content="eh" dp_message="0" end_pos="60" gwpp="-0.047096"></phone>
                          </syll>
                          <syll beg_pos="60" content="l ow" end_pos="94" serr_msg="0" syll_accent="1" syll_score="4.997550">
                              <phone beg_pos="60" content="l" dp_message="0" end_pos="70" gwpp="-0.004438"></phone>
                              <phone beg_pos="70" content="ow" dp_message="0" end_pos="94" gwpp="-0.002929"></phone>
                          </syll>
                      </word>
                      <word beg_pos="94" content="world" dp_message="0" end_pos="133" global_index="1" index="1" pitch="  127.94  127.94  130.07  131.23  132.66  132.10  131.54  130.98  130.42  129.86  128.03  126.00  123.79  122.08  120.69  118.85  117.44  115.41  113.47  111.33  109.24  106.92  104.91  103.12  101.73  100.01   98.45   97.21" pitch_beg="94" pitch_end="122" property="0" total_score="4.691565">
                          <syll beg_pos="94" content="w er l d" end_pos="133" serr_msg="0" syll_accent="0" syll_score="4.485420">
                              <phone beg_pos="94" content="w" dp_message="0" end_pos="110" gwpp="-0.005255"></phone>
                              <phone beg_pos="110" content="er" dp_message="0" end_pos="119" gwpp="-0.069693"></phone>
                              <phone beg_pos="119" content="l" dp_message="0" end_pos="125" gwpp="-0.045296"></phone>
                              <phone beg_pos="125" content="d" dp_message="0" end_pos="133" gwpp="-2.193233"></phone>
                          </syll>
                      </word>
                  </sentence>
              </read_chapter>
          </rec_paper>
      </read_sentence>
  </xml_result>
    """

    assessor = XFYunPronunciationAssessment()
    print(json.dumps(assessor._parse_xfyun_result(xml_result)))


def test_main():
    # Example usage
    audio_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/hello,world.wav"
    reference_file = "/Users/<USER>/Downloads/ai_test_data/pronunciation/hello,world.md"

    with open(reference_file, 'r') as f:
        reference_text = f.read().strip()

    result = pronunciation_assessment(audio_file, reference_text)
    print(json.dumps(result, indent=2))


if __name__ == "__main__":
    test_parse_xml_content()
    # test_main()
