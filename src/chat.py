import json
import openai
import os
import time
import uuid
from typing import List, Dict, Any, AsyncGenerator

from src.exceptions import generate_error_response
from src.models import ErrorResponse

# STRONG_MODEL = "deepseek-v3-250324"
STRONG_MODEL = "doubao-1-5-pro-32k-250115"
WEAK_MODEL = "doubao-1-5-lite-32k-250115"
THINKING_MODEL = "deepseek-v3-250324"
SEED_1_6_MODEL = "doubao-seed-1-6-250615"
SEED_1_6_THINKING_MODEL = 'doubao-seed-1-6-thinking-250715'
SEED_1_6_FLASH_MODEL = 'doubao-seed-1-6-flash-250615'


async def generate_response_with_prompt(prompt: str, system_prompt: str = "You are a helpful assistant.", model: str = WEAK_MODEL, temperature: float = 0.7) -> Dict[str, Any]:
    """
    Generate a response using the OpenAI API with a text prompt.

    Args:
        prompt: The text prompt to send to the model
        model: The model to use for completion (default: doubao-1-5-pro-32k-250115)
        temperature: Controls randomness in the response (default: 0.7)

    Returns:
        A dictionary with the OpenAI chat completion response
    """
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": prompt}
    ]
    return await generate_response(messages, model, temperature)


async def generate_response(messages: List[Dict[str, Any]], model: str = WEAK_MODEL, temperature: float = 0.7) -> Dict[str, Any]:
    """
    Generate a response using the OpenAI API.

    Args:
        messages: List of message objects with role and content (content can be text or multimodal)
        model: The model to use for completion (default: doubao-1-5-pro-32k-250115)
        temperature: Controls randomness in the response (default: 0.7)

    Returns:
        A dictionary with the OpenAI chat completion response
    """
    # Check for API key
    api_key = os.environ.get("OPENAI_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE", "https://api.openai.com/v1")
    if not api_key or not api_base:
        raise ValueError(
            "OPENAI_API_KEY or OPENAI_API_BASE environment variable not set")

    client = openai.AsyncOpenAI(api_key=api_key, base_url=api_base)

    try:
        response = await client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature
        )

        # Convert the response object to a dictionary that matches our expected format
        return {
            "id": response.id,
            "object": "chat.completion",
            "created": response.created,
            "model": response.model,
            "choices": [
                {
                    "index": choice.index,
                    "message": {
                        "role": choice.message.role,
                        "content": choice.message.content
                    },
                    "finish_reason": choice.finish_reason
                }
                for choice in response.choices
            ],
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
    except Exception as e:
        # Log the error and return a structured error response
        print(f"Error calling OpenAI API: {str(e)}")
        return generate_error_response(e)


async def generate_stream_response(messages: list[dict], model: str = WEAK_MODEL, temperature: float = 0.7) -> AsyncGenerator[str, None]:
    """
    Generate a streaming response using the OpenAI API.
    Yields chunks received from the API.
    Supports both text and multimodal (image) inputs.
    """
    api_key = os.environ.get("OPENAI_API_KEY")
    api_base = os.environ.get("OPENAI_API_BASE", "https://api.openai.com/v1")
    if not api_key or not api_base:
        raise ValueError(
            "OPENAI_API_KEY or OPENAI_API_BASE environment variable not set")

    client = openai.AsyncOpenAI(api_key=api_key, base_url=api_base)
    # Use the OpenAI ChatCompletion with streaming enabled
    response_stream = await client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        stream=True
    )

    # Yield each received chunk. You may want to process or format each chunk.
    async for chunk in response_stream:
        # Here we simply yield the raw chunk (as a JSON string if needed).
        # or chunk.to_dict() if you prefer dicts
        yield json.dumps(chunk.model_dump())


def get_available_models() -> List[str]:
    """
    Get a list of available models.
    This is a placeholder function. In a real implementation, you would query the OpenAI API
    or your own model registry to get the list of available models.
    """
    return [WEAK_MODEL, STRONG_MODEL, THINKING_MODEL, SEED_1_6_MODEL, SEED_1_6_THINKING_MODEL, SEED_1_6_FLASH_MODEL]
