# This code would go in your main application file
import threading
import time
from src.db import check_for_timed_out_tasks


def timeout_checker_thread():
    """Run periodically to check for timed out tasks."""
    while True:
        try:
            num_timed_out = check_for_timed_out_tasks()
            if num_timed_out > 0:
                print(f"Marked {num_timed_out} timed-out tasks as failed")
        except Exception as e:
            print(f"Error in timeout checker: {e}")

        # Sleep for a minute before checking again
        time.sleep(60)


def start_scheduler():
    # Start the timeout checker in a background thread
    timeout_thread = threading.Thread(
        target=timeout_checker_thread, daemon=True)
    timeout_thread.start()


def stop_scheduler():
    # This function can be used to stop the scheduler if needed
    # However, since the thread is daemonized, it will automatically stop when the main program exits
    pass
