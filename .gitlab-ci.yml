stages:
  - test
  - build
  - deploy
  - notify

include:
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'templates/deploy-k8s.yaml'
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'jobs/notify-success.yaml'
  - project: 'g1/platform/gitlab-ci-templates'
    ref: master
    file: 'jobs/notify-fail.yaml'

variables:
  GITOPS_APP_NAME: foundation-gusto-ai-base-service

# Test stage - runs unit tests
test:
  stage: test
  image: python:3.10-slim
  allow_failure: true
  tags:
    - 4tune
    - beijing
    - prod
  before_script:
    # Install system dependencies
    - apt-get update && apt-get install -y curl build-essential libpq-dev ffmpeg git
    # Install uv package manager
    - pip install -i https://pypi.tuna.tsinghua.edu.cn/simple uv
    # Set up virtual environment and install dependencies
    - uv venv
    - uv sync
    # Install test dependencies
    - uv sync --group test
  script:
    # Run tests with coverage
    - uv run python run_tests.py
  after_script:
    # Always show test results summary
    - echo "Test execution completed"
    - ls -la || true
    - if [ -f coverage.xml ]; then echo "✅ Coverage XML report generated"; else echo "⚠️  No coverage.xml found"; fi
    - if [ -d htmlcov ]; then echo "✅ HTML coverage report generated"; else echo "⚠️  No htmlcov directory found"; fi
    - if [ -f junit.xml ]; then echo "✅ JUnit report generated"; else echo "⚠️  No junit.xml found"; fi
  coverage: '/TOTAL.*\s+(\d+%)$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
      junit: junit.xml
    paths:
      - htmlcov/
      - junit.xml
      - coverage.xml
    expire_in: 1 week
    when: always
    exclude:
      - "**/*.pyc"
      - "**/__pycache__/"
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

workflow:
  # rules的规则：匹配到第一个，后面的短路
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
      variables:
        IMAGE: registry.cn-shenzhen.aliyuncs.com/project5e-test/foundation-gusto-ai-base-service
        GITOPS_ENVIRONMENT: test
        DOCKER_USERNAME: $TEST_DOCKER_USERNAME
        DOCKER_PASSWORD: $TEST_DOCKER_PASSWORD
    - if: '$CI_COMMIT_BRANCH == "main"'
      variables:
        IMAGE: registry.cn-beijing.aliyuncs.com/4tune/foundation-gusto-ai-base-service
        GITOPS_ENVIRONMENT: prod
        DOCKER_USERNAME: $PROD_DOCKER_USERNAME
        DOCKER_PASSWORD: $PROD_DOCKER_PASSWORD

build:
  stage: build
  tags:
    - 4tune
    - beijing
    - prod
  needs:
    - test
  script:
    - docker build -t ${IMAGE} .
    - docker tag ${IMAGE} ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker login --username=${DOCKER_USERNAME} --password=${DOCKER_PASSWORD} ${IMAGE}
    - docker push ${IMAGE}:${CI_COMMIT_SHORT_SHA}
    - docker rmi ${IMAGE}:${CI_COMMIT_SHORT_SHA}
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "main"'

deploy:
  stage: deploy
  tags:
    - 4tune
    - beijing
    - prod
  needs:
    - test
    - build
  extends:
    - .deploy-stable
  environment:
    name: ${GITOPS_ENVIRONMENT}
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_COMMIT_BRANCH == "develop"'

# Test chat-app frontend
test-chat-app:
  stage: test
  allow_failure: true
  tags:
    - 4tune
    - beijing
    - prod
  before_script:
    - cd chat-app
    - yarn install --frozen-lockfile
  script:
    # Run frontend tests
    - yarn test --coverage --ci --watchAll=false --testResultsProcessor=jest-junit
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: chat-app/coverage/cobertura-coverage.xml
      junit: chat-app/junit.xml
    paths:
      - chat-app/coverage/
      - chat-app/junit.xml
    expire_in: 1 week
    when: always
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "main"'
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'

build-chat-app:
  stage: build
  tags:
    - 4tune
    - beijing
    - prod
  needs:
    - test-chat-app
  script:
    - cd chat-app
    - yarn
    - yarn build-dev
    - echo CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN} > .env
    - npx --registry=https://registry.npm.taobao.org wrangler pages deploy ./build --project-name chat-app --branch $CI_COMMIT_BRANCH
    - rm -R build && rm .env
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'


build-chat-app-prod:
  stage: build
  tags:
    - 4tune
    - beijing
    - prod
  needs:
    - test-chat-app
  script:
    - cd chat-app
    - yarn
    - yarn build-prod
    - echo CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN} > .env
    - npx --registry=https://registry.npm.taobao.org wrangler pages deploy ./build --project-name chat-app --branch $CI_COMMIT_BRANCH
    - rm -R build && rm .env
  rules:
    - if: '$CI_COMMIT_BRANCH == "main"'