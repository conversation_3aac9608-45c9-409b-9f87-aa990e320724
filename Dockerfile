FROM registry.cn-beijing.aliyuncs.com/ergedd/python:3.10-slim-bookworm
# FROM python:3.10-slim-bookworm

RUN sed -i 's|http://deb.debian.org|http://mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources; \
  sed -i 's|http://security.debian.org|http://mirrors.aliyun.com|g' /etc/apt/sources.list.d/debian.sources; \
  apt-get update && apt-get install -y curl build-essential libpq-dev ffmpeg;

WORKDIR /app

COPY pyproject.toml uv.lock /app/

RUN pip install -i https://pypi.tuna.tsinghua.edu.cn/simple uv
RUN uv venv && uv sync

COPY . .

EXPOSE 16833

CMD ["uv", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "16833", "--ws-per-message-deflate", "False"]