# -*- coding: utf-8 -*-
import mimetypes
import json
import logging
import os
import subprocess
import tempfile
import uuid
import oss2
import requests

# from dotenv import load_dotenv
# load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')


ALIYUN_OSS_ACCESS_KEY_ID = os.getenv("ALIYUN_OSS_ACCESS_KEY_ID", "")
ALIYUN_OSS_ACCESS_KEY_SECRET = os.getenv(
    "ALIYUN_OSS_ACCESS_KEY_SECRET", "")
ALIYUN_OSS_ENDPOINT = os.getenv(
    "ALIYUN_OSS_ENDPOINT", "https://oss-cn-beijing.aliyuncs.com")
ALIYUN_OSS_BUCKET_NAME = os.getenv(
    "ALIYUN_OSS_BUCKET_NAME", "gusto-ai-base-service")
ALIYUN_OSS_REGION = os.getenv("ALIYUN_OSS_REGION", "cn-beijing")
ALIYUN_OSS_BUCKET_URL = os.getenv(
    "ALIYUN_OSS_BUCKET_URL", "https://gusto-ai-base-oss.wemore.com")

if not ALIYUN_OSS_ACCESS_KEY_ID or not ALIYUN_OSS_ACCESS_KEY_SECRET:
    raise ValueError("Aliyun OSS Access Key ID and Secret are required.")

auth = oss2.AuthV2(
    ALIYUN_OSS_ACCESS_KEY_ID, ALIYUN_OSS_ACCESS_KEY_SECRET)

# 生成唯一的Bucket名称
bucket = oss2.Bucket(auth, ALIYUN_OSS_ENDPOINT, ALIYUN_OSS_BUCKET_NAME)


def upload_file(bucket, object_name, data):
    try:
        result = bucket.put_object(object_name, data)
        logging.info(
            f"File uploaded successfully, status code: {result.status}")
    except oss2.exceptions.OssError as e:
        logging.error(f"Failed to upload file: {e}")
        raise


def upload(local_file_path: str, object_name: str = None) -> str:
    """
    Uploads a local file to Aliyun OSS.
    :param local_file_path: The path to the local file to upload.
    :return: remote_file_url: The URL of the uploaded file in Aliyun OSS.
    :raises: Exception if the upload fails.
    """
    # Create a Bucket object
    if not object_name:
        object_name = uuid.uuid4().hex + os.path.splitext(
            os.path.basename(local_file_path))[1]  # Generate a unique object name

    # Upload the file
    # For large files, consider using resumable_upload or multipart_upload
    try:
        with open(local_file_path, 'rb') as f:
            file_data = f.read()
        upload_file(bucket, object_name, file_data)
    except IOError as e:
        logging.error(f"Failed to read file {local_file_path}: {e}")
        raise
    except Exception as e:
        logging.error(f"Failed to upload file: {e}")
        raise

    return f"{ALIYUN_OSS_BUCKET_URL}/{object_name}"


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def handler(event, context):
    """
    Aliyun Function Compute handler for converting audio file format.

    Args:
        event: The event data containing the input parameters
        context: The runtime context

    Expected event format:
    {
        "file_url": "https://example.com/audio.wav"
    }

    Returns:
        dict: Response containing the converted file URL or error message
    """
    try:
        logger.info("Received event: %s", event)
        # Parse input
        if isinstance(event, str):
            event = json.loads(event)
        if isinstance(event, bytes):
            event = json.loads(event.decode('utf-8'))

        file_url = event.get('file_url')
        if not file_url:
            bodyStr = event.get("body", "{}")
            body = json.loads(bodyStr)
            file_url = body.get('file_url')

        if not file_url:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'file_url parameter is required'
                })
            }

        logger.info(f"Converting audio file: {file_url}")

        # Convert the audio file
        converted_file_url = auto_convert_file_format(file_url)

        logger.info(f"Conversion successful: {converted_file_url}")

        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'converted_file_url': converted_file_url,
                'original_file_url': file_url
            })
        }

    except Exception as e:
        import traceback
        traceback.print_exc()
        logger.error(f"Error converting audio file: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': f'Audio conversion failed: {str(e)}'
            })
        }


def auto_convert_file_format(file_url: str) -> str:
    """
    Convert audio file to mp3 format with 16000Hz, 16bit, mono settings.

    Args:
        file_url (str): URL of the audio file to convert

    Returns:
        str: URL of the converted audio file

    Raises:
        Exception: If download fails or file format is invalid
        ValueError: If file type cannot be detected or is not audio
    """
    if not file_url:
        raise ValueError("file_url must be provided.")

    temp_file_path = None

    try:
        # Download file to temporary location
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            logger.info(f"Downloading file from: {file_url}")
            response = requests.get(file_url, timeout=30)

            if response.status_code != 200:
                raise Exception(
                    f"Failed to download file from {file_url}, status code: {response.status_code}"
                )

            temp_file.write(response.content)
            temp_file.flush()
            temp_file_path = temp_file.name

        logger.info(f"File downloaded to temporary location: {temp_file_path}")

        # Detect file type
        file_type = detect_file_type(temp_file_path)
        if file_type is None:
            raise ValueError(
                f"Could not detect file type for {temp_file_path}. Please check the file format."
            )

        if not file_type.startswith('audio/') and not is_supported_audio_format(file_type):
            supported_formats = get_supported_audio_formats()
            raise ValueError(
                f"File {temp_file_path} is not a supported audio file. "
                f"Detected type: {file_type}. "
                f"Supported formats: {', '.join(supported_formats['mime_types'])}"
            )

        logger.info(f"Detected file type: {file_type}")

        # Get audio properties using ffprobe
        audio_props = get_audio_properties(temp_file_path)
        logger.info(f"Audio properties - Format: {audio_props['format']}, "
                    f"Sample rate: {audio_props['sample_rate']}Hz, "
                    f"Bit depth: {audio_props['bit_depth']}bit, "
                    f"Channels: {audio_props['channels']}, "
                    f"Codec: {audio_props['codec']}")

        # Check if conversion is needed (target: mp3, 16000Hz, 16bit, mono)
        # This handles AAC, WAV, M4A, and other audio formats
        needs_conversion = (
            audio_props['codec'] != 'mp3' or
            audio_props['sample_rate'] != 16000 or
            audio_props['bit_depth'] != 16 or
            audio_props['channels'] != 1
        )

        if needs_conversion:
            codec = audio_props['codec']
            if codec == 'aac':
                logger.info(
                    "Converting AAC audio to mp3, 16000Hz, 16bit, mono format")
            elif codec == 'flac':
                logger.info(
                    "Converting FLAC audio to mp3, 16000Hz, 16bit, mono format")
            elif codec == 'vorbis':
                logger.info(
                    "Converting OGG Vorbis audio to mp3, 16000Hz, 16bit, mono format")
            elif codec == 'opus':
                logger.info(
                    "Converting Opus audio to mp3, 16000Hz, 16bit, mono format")
            elif codec == 'pcm_s16le' or codec == 'pcm_s24le' or codec == 'pcm_s32le':
                logger.info(
                    "Converting PCM/WAV audio to mp3, 16000Hz, 16bit, mono format")
            elif codec == 'wmav1' or codec == 'wmav2':
                logger.info(
                    "Converting WMA audio to mp3, 16000Hz, 16bit, mono format")
            elif codec == 'amr_nb' or codec == 'amr_wb':
                logger.info(
                    "Converting AMR audio to mp3, 16000Hz, 16bit, mono format")
            else:
                logger.info(
                    f"Converting {codec} audio to mp3, 16000Hz, 16bit, mono format")

            # Create output file path for converted audio
            converted_file_path = temp_file_path + "_converted.mp3"

            # Convert audio format using ffmpeg
            convert_audio_with_ffmpeg(
                input_path=temp_file_path,
                output_path=converted_file_path,
                sample_rate=16000,
                channels=1,
                bit_depth=16
            )

            logger.info(f"Audio converted and saved to: {converted_file_path}")

            # Upload converted file
            uploaded_file_url = upload(
                converted_file_path,
                object_name=f"converted_{uuid.uuid4().hex}.mp3"
            )

            # Clean up the converted file
            try:
                os.unlink(converted_file_path)
                logger.info(
                    f"Cleaned up converted file: {converted_file_path}")
            except OSError as e:
                logger.warning(
                    f"Failed to clean up converted file {converted_file_path}: {e}")

            logger.info(f"Converted file uploaded to: {uploaded_file_url}")
            return uploaded_file_url
        else:
            logger.info("File already in correct format, no conversion needed")
            return file_url

    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                logger.info(f"Cleaned up temporary file: {temp_file_path}")
            except OSError as e:
                logger.warning(
                    f"Failed to clean up temporary file {temp_file_path}: {e}")


def detect_file_type(file_path):
    """
    Detect file type using Python's built-in mimetypes module.
    Returns the MIME type of the file based on its extension.
    """
    try:
        if not os.path.exists(file_path):
            return f"Error: File not found: {file_path}"

        # Add custom MIME types for comprehensive audio format support
        mimetypes.add_type('audio/aac', '.aac')
        mimetypes.add_type('audio/mp4', '.m4a')
        mimetypes.add_type('audio/x-aac', '.aac')
        mimetypes.add_type('audio/flac', '.flac')
        mimetypes.add_type('audio/ogg', '.ogg')
        mimetypes.add_type('audio/opus', '.opus')
        mimetypes.add_type('audio/webm', '.webm')
        mimetypes.add_type('audio/amr', '.amr')
        mimetypes.add_type('audio/3gpp', '.3gp')
        mimetypes.add_type('audio/x-ms-wma', '.wma')
        mimetypes.add_type('audio/x-flac', '.flac')
        mimetypes.add_type('audio/vorbis', '.ogg')
        mimetypes.add_type('audio/speex', '.spx')
        mimetypes.add_type('audio/x-wav', '.wav')

        # Get MIME type based on file extension
        mime_type, _ = mimetypes.guess_type(file_path)

        if mime_type:
            return mime_type
        else:
            # Try extension-based detection first
            ext_mime_type = get_audio_format_from_extension(file_path)
            if ext_mime_type:
                return ext_mime_type
            # If extension detection fails, try to read file signature for common types
            return _detect_by_signature(file_path)

    except (OSError, IOError) as e:
        return f"Error detecting file type: {e}"


def _detect_by_signature(file_path):
    """
    Detect file type by reading file signature (magic numbers).
    This is a fallback method for when mimetypes can't determine the type.
    """
    try:
        with open(file_path, 'rb') as f:
            header = f.read(16)  # Read first 16 bytes

        # Common file signatures for various audio formats
        signatures = {
            b'\x89PNG\r\n\x1a\n': 'image/png',
            b'\xff\xd8\xff': 'image/jpeg',
            b'GIF87a': 'image/gif',
            b'GIF89a': 'image/gif',
            b'RIFF': 'audio/wav',  # Could also be video/avi, but we'll assume audio
            b'ID3': 'audio/mpeg',
            b'\xff\xfb': 'audio/mpeg',  # MP3
            b'\xff\xf3': 'audio/mpeg',  # MP3
            b'\xff\xf2': 'audio/mpeg',  # MP3
            b'\xff\xf0': 'audio/aac',   # AAC ADTS
            b'\xff\xf1': 'audio/aac',   # AAC ADTS
            b'\xff\xf8': 'audio/aac',   # AAC ADTS
            b'\xff\xf9': 'audio/aac',   # AAC ADTS
            b'fLaC': 'audio/flac',      # FLAC
            b'OggS': 'audio/ogg',       # OGG Vorbis/Opus
            b'#!AMR': 'audio/amr',      # AMR-NB
            b'#!AMR-WB': 'audio/amr',   # AMR-WB
            b'\x1a\x45\xdf\xa3': 'audio/webm',  # WebM/Matroska
            b'\x30\x26\xb2\x75': 'audio/x-ms-wma',  # WMA
            b'ftyp': 'video/mp4',       # MP4 (starts at offset 4)
            b'%PDF': 'application/pdf',
        }

        # Check for MP4/M4A/AAC/3GP (ftyp signature is at offset 4)
        if len(header) >= 8 and header[4:8] == b'ftyp':
            if len(header) >= 12:
                subtype = header[8:12]
                # Enhanced audio format detection in MP4 container
                if subtype in [b'M4A ', b'mp42', b'isom', b'mp41', b'dash', b'M4V ', b'3gp4', b'3gp5', b'3ge6', b'3ge7', b'3gg6']:
                    return 'audio/mp4'  # M4A/AAC/3GP audio files in MP4 container
                elif subtype in [b'avc1', b'mp4v', b'qt  ']:
                    return 'video/mp4'
                else:
                    # Default to audio for unknown subtypes in mp4 container
                    return 'audio/mp4'

        # Special handling for RIFF files (WAV, but could be other formats)
        if header.startswith(b'RIFF') and len(header) >= 12:
            # Check the format type at offset 8
            format_type = header[8:12]
            if format_type == b'WAVE':
                return 'audio/wav'
            elif format_type == b'AVI ':
                return 'video/avi'
            else:
                return 'audio/wav'  # Default to audio for unknown RIFF types

        # Check other signatures
        for signature, mime_type in signatures.items():
            if header.startswith(signature):
                return mime_type

        # Default fallback
        return 'application/octet-stream'

    except (OSError, IOError) as e:
        return f"Error reading file signature: {e}"


def get_audio_properties(file_path):
    """
    Get audio properties using ffprobe (part of ffmpeg).

    Args:
        file_path (str): Path to the audio file

    Returns:
        dict: Audio properties including sample_rate, channels, bit_depth, format

    Raises:
        Exception: If ffprobe command fails
    """
    try:
        # Use ffprobe to get audio information
        cmd = [
            'ffprobe',
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            file_path
        ]

        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True)
        data = json.loads(result.stdout)

        # Find the audio stream
        audio_stream = None
        for stream in data.get('streams', []):
            if stream.get('codec_type') == 'audio':
                audio_stream = stream
                break

        if not audio_stream:
            raise Exception("No audio stream found in file")

        # Extract relevant properties
        properties = {
            'sample_rate': int(audio_stream.get('sample_rate', 0)),
            'channels': int(audio_stream.get('channels', 0)),
            # Default to 16 if not available
            'bit_depth': int(audio_stream.get('bits_per_sample', 16)),
            'format': data.get('format', {}).get('format_name', ''),
            'codec': audio_stream.get('codec_name', '')
        }

        return properties

    except subprocess.CalledProcessError as e:
        raise Exception(f"ffprobe command failed: {e.stderr}")
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse ffprobe output: {e}")
    except Exception as e:
        raise Exception(f"Error getting audio properties: {e}")


def convert_audio_with_ffmpeg(input_path, output_path, sample_rate=16000, channels=1, bit_depth=16):
    """
    Convert audio file using ffmpeg with enhanced support for multiple formats.

    Args:
        input_path (str): Path to input audio file
        output_path (str): Path to output audio file
        sample_rate (int): Target sample rate (default: 16000)
        channels (int): Target number of channels (default: 1 for mono)
        bit_depth (int): Target bit depth (default: 16)

    Raises:
        Exception: If ffmpeg command fails
    """
    try:
        # Build ffmpeg command with enhanced audio processing
        cmd = [
            'ffmpeg',
            '-i', input_path,
            '-vn',                    # Disable video streams (audio only)
            '-ar', str(sample_rate),  # Audio sample rate
            '-ac', str(channels),     # Audio channels
            '-sample_fmt', 's16',     # 16-bit signed integer samples
            '-acodec', 'libmp3lame',  # Use LAME MP3 encoder for better quality
            '-ab', '128k',            # Audio bitrate
            '-f', 'mp3',              # Output format
            '-y',                     # Overwrite output file
            output_path
        ]

        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True)
        logger.info(f"Audio converted successfully with ffmpeg")

    except subprocess.CalledProcessError as e:
        # Fallback to basic conversion if LAME encoder is not available
        logger.warning(
            f"LAME encoder failed, trying basic conversion: {e.stderr}")
        try:
            cmd_fallback = [
                'ffmpeg',
                '-i', input_path,
                '-vn',                    # Disable video streams
                '-ar', str(sample_rate),  # Audio sample rate
                '-ac', str(channels),     # Audio channels
                '-sample_fmt', 's16',     # 16-bit signed integer samples
                '-f', 'mp3',              # Output format
                '-y',                     # Overwrite output file
                output_path
            ]

            result = subprocess.run(
                cmd_fallback, capture_output=True, text=True, check=True)
            logger.info(f"Audio converted successfully with ffmpeg (fallback)")

        except subprocess.CalledProcessError as e2:
            raise Exception(f"ffmpeg conversion failed: {e2.stderr}")
    except Exception as e:
        raise Exception(f"Error during audio conversion: {e}")


def get_supported_audio_formats():
    """
    Returns a list of supported audio formats that can be converted.

    Returns:
        dict: Dictionary containing supported formats by category
    """
    return {
        'lossy': ['mp3', 'aac', 'ogg', 'opus', 'wma', 'amr'],
        'lossless': ['flac', 'wav', 'pcm'],
        'container': ['mp4', 'm4a', '3gp', 'webm'],
        'mime_types': [
            'audio/mpeg', 'audio/mp3',
            'audio/aac', 'audio/x-aac',
            'audio/mp4', 'audio/m4a',
            'audio/flac', 'audio/x-flac',
            'audio/ogg', 'audio/vorbis',
            'audio/opus',
            'audio/wav', 'audio/x-wav',
            'audio/webm',
            'audio/amr', 'audio/3gpp',
            'audio/x-ms-wma'
        ]
    }


def is_supported_audio_format(mime_type):
    """
    Check if the given MIME type is a supported audio format.

    Args:
        mime_type (str): MIME type to check

    Returns:
        bool: True if supported, False otherwise
    """
    supported = get_supported_audio_formats()
    return mime_type in supported['mime_types'] or mime_type.startswith('audio/')


def get_audio_format_from_extension(file_path):
    """
    Get audio format from file extension as a fallback method.

    Args:
        file_path (str): Path to the audio file

    Returns:
        str: MIME type based on file extension, or None if not recognized
    """
    extension_map = {
        '.mp3': 'audio/mpeg',
        '.aac': 'audio/aac',
        '.m4a': 'audio/mp4',
        '.flac': 'audio/flac',
        '.ogg': 'audio/ogg',
        '.opus': 'audio/opus',
        '.wav': 'audio/wav',
        '.webm': 'audio/webm',
        '.wma': 'audio/x-ms-wma',
        '.amr': 'audio/amr',
        '.3gp': 'audio/3gpp',
        '.spx': 'audio/speex'
    }

    _, ext = os.path.splitext(file_path.lower())
    return extension_map.get(ext)


# For local testing
if __name__ == "__main__":
    # Test events for different audio formats
    test_events = [
        {
            "file_url": "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/d1b46e5d-b10d-4524-a93f-1df948a777f0.aac",
            "format": "AAC"
        },
        {
            "file_url": "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/83182746-77df-4712-af48-d2abc591f7d6.wma",
            "format": "WMA"
        },
        {
            "file_url": "https://community-x.oss-cn-shenzhen.aliyuncs.com/test/caf9d1a3-ac98-402a-a848-1ead21e644a1.flac",
            "format": "FLAC"
        },
        {
            "file_url": "https://a1.easemob.com/1128250120193676/gusto-spot-cn/chatfiles/5e3c0a40-5344-11f0-a261-c57b34d4eed0",
            "format": "UNKNOWN"
        }
    ]

    # Mock context
    class MockContext:
        def __init__(self):
            self.request_id = "test-request-id"
            self.function_name = "convert_audio_format"

    context = MockContext()

    # Test each format
    for i, test_event in enumerate(test_events):
        print(
            f"\n=== Testing {test_event.get('format', 'Unknown')} format ===")
        try:
            result = handler({"file_url": test_event["file_url"]}, context)
            print(json.dumps(result, indent=2))
        except Exception as e:
            print(f"Error testing {test_event.get('format', 'Unknown')}: {e}")

    # Display supported formats
    print(f"\n=== Supported Audio Formats ===")
    supported = get_supported_audio_formats()
    print(f"Lossy formats: {', '.join(supported['lossy'])}")
    print(f"Lossless formats: {', '.join(supported['lossless'])}")
    print(f"Container formats: {', '.join(supported['container'])}")
    print(f"MIME types: {', '.join(supported['mime_types'])}")
