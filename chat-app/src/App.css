* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
}

body {
  background-color: #f7f9fc;
  color: #333;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* App Header */
.app-header {
  background-color: #2d3748;
  color: white;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 100;
  position: relative;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h1 {
  margin: 0;
  font-size: 1.4em;
  font-weight: 600;
}

.collapse-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.collapse-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Header Controls */
.header-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.header-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.header-btn.active {
  background: #38b2ac;
  border-color: #38b2ac;
}

/* Permission Warning and Status */
.permission-warning,
.permission-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 6px;
  margin-right: 8px;
}

.permission-warning {
  background-color: #fed7d7;
  color: #c53030;
}

.permission-status {
  background-color: #c6f6d5;
  color: #2f855a;
}

/* App Container */
.app-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.app-container.bot-panel-collapsed .chat-panel {
  width: 100%;
}

/* Bot Panel Wrapper */
.bot-panel-wrapper {
  width: 25%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.bot-panel-wrapper.collapsed {
  width: 0;
  min-width: 0;
  opacity: 0;
  transform: translateX(-100%);
}

/* Bot Panel */
.bot-panel {
  width: 100%;
  background-color: #2d3748;
  display: flex;
  flex-direction: column;
  color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  height: 100%;
}

/* Chat Panel Layout Override */
.chat-panel {
  flex: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .app {
    height: 100vh;
    overflow: hidden;
  }

  .app-header {
    padding: 12px 16px;
    min-height: 56px;
    flex-shrink: 0;
  }
  
  .header-left {
    gap: 12px;
  }
  
  .header-left h1 {
    font-size: 1.1em;
    white-space: nowrap;
  }
  
  .header-controls {
    gap: 6px;
    flex-wrap: nowrap;
  }
  
  .header-btn, .collapse-toggle {
    min-width: 40px;
    height: 40px;
    padding: 8px;
    font-size: 16px;
    border-radius: 8px;
  }

  .bot-panel-wrapper {
    width: 85vw;
    max-width: 320px;
    position: fixed;
    top: 56px;
    left: 0;
    height: calc(100vh - 56px);
    z-index: 100;
    background-color: #2d3748;
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
    transform: translateX(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .bot-panel-wrapper.collapsed {
    transform: translateX(-100%);
  }

  .app-container {
    position: relative;
    height: calc(100vh - 56px);
    overflow: hidden;
  }

  .chat-panel {
    width: 100%;
    height: 100%;
  }

  .app-container:not(.bot-panel-collapsed)::before {
    content: '';
    position: fixed;
    top: 56px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 50;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
  }

  .app-container.no-bot-panel:not(.bot-panel-collapsed)::before {
    display: none;
  }

  .bot-panel {
    height: 100%;
    position: relative;
    z-index: 101;
  }

  .permission-warning,
  .permission-status {
    font-size: 12px;
    padding: 4px 6px;
    min-width: auto;
  }
}

/* Extra small phones */
@media (max-width: 480px) {
  .app-header {
    padding: 10px 12px;
    min-height: 52px;
  }

  .bot-panel-wrapper {
    width: 90vw;
    max-width: 280px;
    top: 52px;
    height: calc(100vh - 52px);
  }
  
  .app-container {
    height: calc(100vh - 52px);
  }

  .app-container:not(.bot-panel-collapsed)::before {
    top: 52px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .header-left h1 {
    font-size: 1rem;
  }

  .header-btn, .collapse-toggle {
    min-width: 36px;
    height: 36px;
    padding: 6px;
    font-size: 14px;
  }

  .header-controls {
    gap: 4px;
  }
}

/* Landscape mode on phones */
@media (max-width: 768px) and (orientation: landscape) {
  .app-header {
    padding: 8px 16px;
    min-height: 48px;
  }

  .bot-panel-wrapper {
    width: 60vw;
    max-width: 300px;
    top: 48px;
    height: calc(100vh - 48px);
  }

  .app-container {
    height: calc(100vh - 48px);
  }

  .app-container:not(.bot-panel-collapsed)::before {
    top: 48px;
  }

  .header-btn, .collapse-toggle {
    min-width: 32px;
    height: 32px;
    font-size: 14px;
  }
}