export const ENV = process.env.REACT_APP_ENV || "development";
export const IS_PRODUCTION = ENV === "production";
export const API_BASE_URL =
  process.env.REACT_APP_API_BASE_URL ||
  (IS_PRODUCTION
    ? "https://ai-base.wemore.com"
    : "https://ai-base-test.wemore.com");
export const WS_API_BASE_URL =
  process.env.REACT_APP_WS_API_BASE_URL ||
  (IS_PRODUCTION
    ? "wss://ai-base.wemore.com"
    : "wss://ai-base-test.wemore.com");

console.log("Environment:", ENV);
console.log("API Base URL:", API_BASE_URL);
console.log("WebSocket API Base URL:", WS_API_BASE_URL);
