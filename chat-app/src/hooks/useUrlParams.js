import { useSearchParams } from "react-router-dom";
import { useEffect } from "react";
import { getUserAuth, getApiKey } from "../utils/userAuth";

export const useUrlParams = () => {
  const [searchParams] = useSearchParams();

  const getApiKeyFromUrl = () => {
    // First check if we have X-5E-USER auth, then fallback to apikey param
    const userAuth = getUserAuth();
    if (userAuth) {
      return getApiKey();
    }
    return searchParams.get("apikey");
  };

  const getAllParams = () => {
    const params = {};
    for (let [key, value] of searchParams) {
      params[key] = value;
    }
    return params;
  };

  return {
    getApiKeyFromUrl,
    getAllParams,
    searchParams,
  };
};
