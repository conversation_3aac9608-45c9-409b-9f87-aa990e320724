import { useState, useEffect, useRef } from "react";
import {
  checkMissionStatus,
  createMission,
  streamChatRequestViaWebSocket,
  getChatHistory,
  createChatHistoryMessage,
  deleteChatHistoryMessage,
  convertAPIMessageToChatMessage,
  syncChatHistory,
  convertChatToSyncFormat,
} from "../services/apiServices";
import {
  handleASR,
  handleASR_v2,
  handleGrammarCorrection,
  handlePronunciationAssessment,
  handleTranslation,
  handleTTS,
} from "../services/messageServices";
import TTSStreamPlayer from "../services/TTSStreamPlayer";
import { Bot, ChatMessage, Chat, Mission } from "../types";
import { isString } from "../utils/string";
import { getMissionCacheKey } from "../utils/cacheKeys";
import {
  ChatMessageSenderType,
  ChatSystemMessageType,
} from "../types/ChatMessageType";
import { getUserAuth, getApi<PERSON>ey } from "../utils/userAuth";

const INVALID_VOICE_MESSAGE = "[Voice message - transcription failed]";

export const useChatState = (initialBotId = null, initialApiKey = null) => {
  const [lastMessage, setLastMessage] = useState(null);
  const [bots, setBots] = useState(() => {
    const storedBots = JSON.parse(localStorage.getItem("bots")) || [];
    return storedBots.map((botData) => Bot.fromStorage(botData));
  });

  const [currentBotId, setCurrentBotId] = useState(
    initialBotId || localStorage.getItem("currentBotId") || null
  );

  const [chats, setChats] = useState(() => {
    const storedChats = localStorage.getItem("chats");
    if (storedChats) {
      try {
        const parsedChats = JSON.parse(storedChats);
        // Convert stored chat data back to Chat instances
        const restoredChats = {};
        Object.keys(parsedChats).forEach((botId) => {
          restoredChats[botId] = new Chat(parsedChats[botId]);
        });
        return restoredChats;
      } catch (error) {
        console.warn("Failed to parse stored chats:", error);
        return {};
      }
    }
    return {};
  });

  const [autoTTSEnabled, setAutoTTSEnabled] = useState(() => {
    const stored = localStorage.getItem("autoTTSEnabled");
    return stored !== null ? stored === "true" : true;
  });

  const [apiKey, setApiKey] = useState(() => {
    // Initialize from X-5E-USER if available, otherwise fallback to existing apiKey
    const userAuth = getUserAuth();
    if (userAuth) {
      return getApiKey();
    }
    return initialApiKey || localStorage.getItem("apiKey") || "";
  });

  const [chatSuggestionsEnabled, setChatSuggestionsEnabled] = useState(
    localStorage.getItem("chatSuggestionsEnabled") !== "false"
  );

  // Mission state for mission-type bots
  const [mission, setMission] = useState(null);
  const [beforeMission, setBeforeMission] = useState(null);

  const currentBot = bots.find((bot) => bot.id === currentBotId);
  // console.log("Current bot:", currentBot);

  // Current chat state - derived from chats and currentBotId
  const [currentChat, setCurrentChat] = useState(() => {
    return currentBotId && chats[currentBotId] ? chats[currentBotId] : null;
  });

  const ttsStreamPlayer = useRef(
    new TTSStreamPlayer(
      currentBot?.voice,
      apiKey,
      currentBot?.voice_speed || 1.0
    )
  );

  // Update TTS player when API key changes
  useEffect(() => {
    if (ttsStreamPlayer.current) {
      ttsStreamPlayer.current.setApiKey(apiKey);
    }
  }, [apiKey]);

  // Update currentChat when chats or currentBotId changes
  useEffect(() => {
    if (currentBotId && chats[currentBotId]) {
      setCurrentChat(chats[currentBotId]);
    } else if (currentBotId) {
      // Load chat history from backend when switching to a bot without local chat
      loadChatHistoryFromBackend(currentBotId).then((backendChat) => {
        if (backendChat) {
          const updatedChats = { ...chats };
          updatedChats[currentBotId] = backendChat;
          setChats(updatedChats);
          setCurrentChat(backendChat);
        } else {
          // Check if we have local history that needs to be synced to backend
          const localChat = getLocalChatHistory(currentBotId);
          if (localChat && localChat.messages.length > 0) {
            console.log("Found local chat history, syncing to backend...");
            // Sync local history to backend first
            syncLocalChatToBackend(localChat)
              .then(() => {
                console.log(
                  "Local chat history synced to backend successfully"
                );
                // Use the local chat as current chat
                const updatedChats = { ...chats };
                updatedChats[currentBotId] = localChat;
                setChats(updatedChats);
                setCurrentChat(localChat);
              })
              .catch((error) => {
                console.error("Failed to sync local chat to backend:", error);
                // Still use local chat even if sync fails
                const updatedChats = { ...chats };
                updatedChats[currentBotId] = localChat;
                setChats(updatedChats);
                setCurrentChat(localChat);
              });
          } else {
            // Create new empty chat if no backend or local history
            const newChat = new Chat({ botId: currentBotId, messages: [] });
            const updatedChats = { ...chats };
            updatedChats[currentBotId] = newChat;
            setChats(updatedChats);
            setCurrentChat(newChat);
          }
        }
      });
    } else {
      setCurrentChat(null);
    }
  }, [chats, currentBotId]);

  useEffect(() => {
    if (currentChat && currentChat?.messages.length === 0) {
      sendHelloMessage();
    }
  }, [currentChat?.messages]);

  useEffect(() => {
    if (apiKey) {
      const updatedChats = { ...chats };
      let neededUpdate = false;
      for (const botId of Object.keys(updatedChats)) {
        if (updatedChats[botId]) {
          const typingMessages = updatedChats[botId].getTypingMessages();
          if (typingMessages.length > 0) {
            typingMessages.forEach((msg) => msg.markCompleted());
            neededUpdate = true;
          }
        }
      }
      if (neededUpdate) {
        setChats({ ...updatedChats });
      }
    }
  }, [apiKey]); // Remove 'chats' from dependency array to prevent loop

  // Save state to localStorage
  useEffect(() => {
    localStorage.setItem(
      "bots",
      JSON.stringify(bots.map((bot) => bot.toAPIFormat()))
    );
    localStorage.setItem("currentBotId", currentBotId);

    localStorage.setItem("autoTTSEnabled", autoTTSEnabled);
    localStorage.setItem("chatSuggestionsEnabled", chatSuggestionsEnabled);
    localStorage.setItem("apiKey", apiKey);
  }, [bots, currentBotId, autoTTSEnabled, chatSuggestionsEnabled, apiKey]);

  // Save chats to localStorage whenever chats change
  useEffect(() => {
    // Convert Chat instances to plain objects for storage
    const chatsForStorage = {};
    Object.keys(chats).forEach((botId) => {
      if (chats[botId]) {
        chatsForStorage[botId] = chats[botId].toStorage();
      }
    });
    localStorage.setItem("chats", JSON.stringify(chatsForStorage));
  }, [chats]);

  useEffect(() => {
    if (currentBotId) {
      updateLastMessage();
      ttsStreamPlayer.current.setVoice(currentBot?.voice);

      // Create mission for mission-type bots when starting a new chat
      const bot = bots.find((b) => b.id === currentBotId);
      if (bot && bot.bot_type === "mission" && apiKey && !mission) {
        const cachedMission = localStorage.getItem(
          getMissionCacheKey(currentBotId)
        );
        if (cachedMission) {
          const loadedMission = JSON.parse(cachedMission);
          console.log("Loaded mission from cache:", loadedMission);
          // Create a Mission instance from the cached data
          setMission(loadedMission);
        } else {
          createMissionForBot(currentBotId)
            .then((response) => {
              if (response && response.mission) {
                console.log("Created new mission:", response.mission);
                setMission(response.mission);
              }
            })
            .catch((error) => {
              console.error("Error creating mission:", error);
            });
        }
      }
    }
  }, [currentBotId, apiKey]);

  // Sync all chats to the backend
  const syncChatsToBackend = async () => {
    return;
    if (!apiKey) {
      console.log("No API key provided, skipping chat sync");
      return;
    }

    try {
      // Collect all chat history items to sync
      const allChatHistory = [];
      Object.values(chats).forEach((chat) => {
        if (chat && chat.messages && chat.messages.length > 0) {
          const chatItems = convertChatToSyncFormat(chat);
          if (chatItems.length > 0) {
            allChatHistory.push(...chatItems);
          }
        }
      });

      if (allChatHistory.length === 0) {
        console.log("No chat history to sync");
        return;
      }

      console.log(`Syncing ${allChatHistory.length} messages to backend`);
      const result = await syncChatHistory(allChatHistory);
      console.log("Chat sync result:", result);
      return result;
    } catch (error) {
      console.error("Error syncing chats:", error);
    }
  };

  const clearChatHistory = async () => {
    if (!currentBotId) {
      alert("Please select a bot first.");
      return;
    }

    if (
      window.confirm(
        "Are you sure you want to clear the chat history? This cannot be undone."
      )
    ) {
      sendSystemMessage(ChatSystemMessageType.CLEAR_CHAT);
      sendHelloMessage();

      // Create mission for mission-type bots when clearing chat history
      const bot = bots.find((b) => b.id === currentBotId);
      if (bot && bot.bot_type === "mission" && apiKey) {
        clearMissionForBot(currentBotId);
      }
    }
  };

  const sendSystemMessage = (type) => {
    if (!currentBotId) return;
    const updatedChats = { ...chats };
    if (!updatedChats[currentBotId]) {
      updatedChats[currentBotId] = Chat.createEmpty(currentBotId);
    }
    if (updatedChats[currentBotId].length === 0) return;
    const systemMessage = ChatMessage.createSystemMessage(type, currentBotId);
    updatedChats[currentBotId].addMessage(systemMessage);
    setChats({ ...updatedChats });
    setLastMessage(systemMessage);
  };

  const sendHelloMessage = () => {
    if (!currentBotId || !currentBot) return;
    const updatedChats = { ...chats };
    if (!updatedChats[currentBotId]) {
      updatedChats[currentBotId] = Chat.createEmpty(currentBotId);
    }

    const helloMessage =
      currentBot.hello_message || "Hello! How can I assist you today?";
    updatedChats[currentBotId].addMessage(
      ChatMessage.createBotMessage(helloMessage)
    );
    setChats({ ...updatedChats });
    setLastMessage(null);
  };

  const sendMessage = async (message, isResend = false) => {
    if (!message || !currentBotId) return;

    const updatedChats = { ...chats };

    // Ensure chat exists for current bot
    if (!updatedChats[currentBotId]) {
      const bot = bots.find((b) => b.id === currentBotId);
      updatedChats[currentBotId] = Chat.createEmpty(currentBotId, bot);
    }

    let userMessageId = null;
    if (!isResend) {
      // Add user message
      const userMessage = message;
      userMessageId = userMessage.id;
      updatedChats[currentBotId].addMessage(userMessage);
      setChats({ ...updatedChats });
      setLastMessage(userMessage);

      // Sync user message with backend
      // syncMessageWithBackend(userMessage, currentBotId);
    } else {
      // For resend, get the last user message ID
      const lastUserMessage = updatedChats[currentBotId].messages
        .filter((msg) => msg.isFromUser())
        .pop();
      userMessageId = lastUserMessage?.id;
    }

    // Add typing message for bot
    const typingMessage = ChatMessage.createTypingMessage(
      ChatMessageSenderType.BOT
    );
    const tempMessageId = typingMessage.id;
    updatedChats[currentBotId].addMessage(typingMessage);
    setChats({ ...updatedChats });

    // Get the current bot
    const bot = bots.find((b) => b.id === currentBotId);
    if (!bot) return;

    // Prepare messages for API
    const currentChat = updatedChats[currentBotId];
    const recentMessages = currentChat.buildConversationHistory();

    let messageContent = null;
    let asrResult = null;
    let delayCallback = null;

    if (message.type === "audio") {
      // If it's a voice message, extract the audio URL and create voice message
      const audioUrl = message.content;

      // The message should already be created with the correct type in createUserMessage
      // No need to update the message here since it's already properly formatted

      asrResult = await handleASR_v2({
        messageId: userMessageId,
        audioUrl,
        apiKey,
      });
      console.log("ASR result:", asrResult);

      // For voice messages, use ASR text as content if available, otherwise use a placeholder
      if (asrResult.text) {
        messageContent = asrResult.text;

        // Store ASR result in the user message
        if (!isResend) {
          const userMessage =
            updatedChats[currentBotId].getMessage(userMessageId);
          if (userMessage) {
            userMessage.setASR(asrResult.text);
            // Set duration from ASR result if available
            if (asrResult.duration && !userMessage.duration) {
              userMessage.setDuration(asrResult.duration);
            }
            setChats({ ...updatedChats });
          }
        }
      } else {
        // If ASR fails, use a placeholder for the conversation
        messageContent = INVALID_VOICE_MESSAGE;
      }

      await syncMessageWithBackend(
        updatedChats[currentBotId].getMessage(userMessageId),
        currentBotId
      );

      if (messageContent !== INVALID_VOICE_MESSAGE) {
        delayCallback = () => {
          Promise.all([
            handleGrammarCorrection({
              messageId: userMessageId,
              text: messageContent,
              coloquial: true,
              apiKey,
            }).then((grammarResult) => {
              // Store grammar analysis result in the message
              if (grammarResult && grammarResult.suggestions) {
                const userMessage =
                  updatedChats[currentBotId].getMessage(userMessageId);
                if (userMessage) {
                  userMessage.setGrammarAnalysis(grammarResult.suggestions);
                  setChats({ ...updatedChats });
                }
              }
            }),
            handleTranslation({
              messageId: userMessageId,
              text: messageContent,
              apiKey,
            }).then((translationResult) => {
              // Store translation result in the message
              if (translationResult && translationResult.translated_text) {
                const userMessage =
                  updatedChats[currentBotId].getMessage(userMessageId);
                if (userMessage) {
                  userMessage.setTranslation(translationResult.translated_text);
                  setChats({ ...updatedChats });
                }
              }
            }),
            handlePronunciationAssessment({
              messageId: userMessageId,
              audioUrl,
              messageContent,
              apiKey,
            }).then((pronunciationResult) => {
              // Store pronunciation assessment result in the message
              if (pronunciationResult && pronunciationResult.result) {
                const userMessage =
                  updatedChats[currentBotId].getMessage(userMessageId);
                if (userMessage) {
                  userMessage.setPronunciationAssessment(
                    pronunciationResult.result
                  );
                  setChats({ ...updatedChats });
                }
              }
            }),
            checkMissionStatusForBot(currentBotId),
          ]).catch((error) => {
            console.error("Error processing voice message:", error);
          });
        };
      } else {
        console.error("ASR error:", asrResult);
      }
    } else {
      delayCallback = () => {
        Promise.all([
          handleTTS({
            messageId: userMessageId,
            text: messageContent,
            apiKey,
          }).then((ttsResult) => {
            // Store TTS result in the message
            if (ttsResult && (ttsResult.audio_url || ttsResult.audio)) {
              const userMessage =
                updatedChats[currentBotId].getMessage(userMessageId);
              if (userMessage) {
                userMessage.setTTS(ttsResult.audio_url || ttsResult.audio);
                setChats({ ...updatedChats });
              }
            }
          }),
          handleGrammarCorrection({
            messageId: userMessageId,
            text: messageContent,
            apiKey,
          }).then((grammarResult) => {
            // Store grammar analysis result in the message
            if (grammarResult && grammarResult.suggestions) {
              const userMessage =
                updatedChats[currentBotId].getMessage(userMessageId);
              if (userMessage) {
                userMessage.setGrammarAnalysis(grammarResult.suggestions);
                setChats({ ...updatedChats });
              }
            }
          }),
          handleTranslation({
            messageId: userMessageId,
            text: messageContent,
            apiKey,
          }).then((translationResult) => {
            // Store translation result in the message
            if (translationResult && translationResult.translated_text) {
              const userMessage =
                updatedChats[currentBotId].getMessage(userMessageId);
              if (userMessage) {
                userMessage.setTranslation(translationResult.translated_text);
                setChats({ ...updatedChats });
              }
            }
          }),
          checkMissionStatusForBot(currentBotId),
        ]).catch((error) => {
          console.error("Error processing text message:", error);
        });
      };
    }

    delayCallback?.();

    let systemMessageContent = `You are ${bot.name} with persona: 
\`\`\`
${bot.persona}
\`\`\`
`;
    if (bot.bot_type === "mission" && mission) {
      systemMessageContent += `
You are currently talking with user on a mission: 
\`\`\`
${JSON.stringify(mission)}
\`\`\`
Please follow the mission steps and respond to guide the user to finish the mission.
You should play the role of the specific bot persona and help the user to complete the mission.
      `;
    }

    await new Promise((resolve) => {
      setTimeout(() => {
        console.log("Waiting for 1 second before sending message");
        resolve();
      }, 100);
    });

    // Add system message with bot persona
    // Get the current user message after ASR processing
    const currentUserMessage =
      updatedChats[currentBotId].getMessage(userMessageId);
    const messages = [
      {
        role: "system",
        content: systemMessageContent,
      },
      ...recentMessages.slice(0, -1), // Exclude the last user message
      currentUserMessage.toAPIFormat(),
    ];

    try {
      // Create a reference to the latest chat state to avoid stale closures
      let latestChats = { ...updatedChats };
      let accumulatedText = "";

      // Set up audio playback state when TTS is enabled
      if (autoTTSEnabled) {
        console.log("TTS enabled - audio will be handled by WebSocket");
        if (latestChats[currentBotId]) {
          const message = latestChats[currentBotId].getMessage(tempMessageId);
          if (message) {
            console.log("Setting message as playing audio");
            message.setPlayingAudio(true);
            setChats({ ...latestChats });
          }
        }
      }

      await streamChatRequestViaWebSocket(
        messages,
        apiKey,
        currentBotId || "", // Pass the bot ID
        bot.voice || "en_female_amanda_mars_bigtts", // Use bot's voice
        bot.voice_speed || 1.0, // Use bot's voice speed
        (chunk) => {
          // Handle server-generated user message ID
          if (chunk.type === "user_message_id") {
            console.log(
              "Received user message ID from server:",
              chunk.user_message_id
            );
            // Update the user message with the server-generated ID
            latestChats = { ...latestChats };
            if (latestChats[currentBotId]) {
              const userMessage =
                latestChats[currentBotId].getMessage(userMessageId);
              if (userMessage) {
                userMessage.setId(chunk.user_message_id);
                setChats({ ...latestChats });
              }
            }
            return;
          }

          // Process each chunk of text as it arrives
          if (
            chunk &&
            chunk.choices &&
            chunk.choices[0] &&
            chunk.choices[0].delta &&
            chunk.choices[0].delta.content
          ) {
            let content = chunk.choices[0].delta.content;
            // fix: "sci-fi" has been sent as "sci - fi", etc.
            if (content.trim() === "-" || accumulatedText.endsWith("-")) {
              content = content.trim();
            }
            accumulatedText += content;

            // Get the latest state of chats
            latestChats = { ...latestChats };
            if (latestChats[currentBotId]) {
              const message =
                latestChats[currentBotId].getMessage(tempMessageId);
              if (message) {
                message.updateContent(accumulatedText);
                setChats({ ...latestChats });
              }
            }
          }
        },
        (audioChunk) => {
          // Handle audio chunks from WebSocket
          if (autoTTSEnabled) {
            // Decode base64 audio chunk and play it
            try {
              const audioBuffer = Uint8Array.from(atob(audioChunk), (c) =>
                c.charCodeAt(0)
              );
              ttsStreamPlayer.current.playAudioChunk(audioBuffer);
            } catch (error) {
              console.error("Error playing audio chunk:", error);
            }
          }
          // If TTS is disabled, we just ignore audio chunks
        },
        (error) => {
          // Handle errors
          console.error("Error during streaming:", error);

          // Update the bot message to show the error
          latestChats = { ...latestChats };
          if (latestChats[currentBotId]) {
            const message = latestChats[currentBotId].getMessage(tempMessageId);
            if (message) {
              message
                .updateContent(`Sorry, there was an error: ${error.message}`)
                .markCompleted();
              setChats({ ...latestChats });
            }
          }
        },
        ({ full_response, full_audio_response, bot_message_id }) => {
          // When streaming is complete, finalize the message
          latestChats = { ...latestChats };
          delayCallback?.();
          if (latestChats[currentBotId]) {
            const message = latestChats[currentBotId].getMessage(tempMessageId);
            if (message) {
              // Store the TTS audio response in the message
              if (full_audio_response) {
                message.setTTS(full_audio_response);
              }
              message.updateContent(full_response).markCompleted();
              setChats({ ...latestChats });

              // Sync bot message with backend when completed
              // syncMessageWithBackend(message, currentBotId);
            }
            setLastMessage({ ...message });
          }
          ttsStreamPlayer.current.finishPlayAudioTrunks(full_response, () => {
            const message = latestChats[currentBotId].getMessage(tempMessageId);
            if (message) {
              // message.updateContent(accumulatedText).setTyping(false);
              message.setPlayingAudio(false);
              message.setId(bot_message_id); // Use server-generated bot message ID
              setChats({ ...latestChats });
            }
            setLastMessage({ ...message });
          });
        }
      );
    } catch (error) {
      console.error("Error during API call:", error);

      // Update the bot message to show the error
      const updatedChatsSnapshot = { ...chats };
      if (updatedChatsSnapshot[currentBotId]) {
        const message =
          updatedChatsSnapshot[currentBotId].getMessage(tempMessageId);
        if (message) {
          message
            .updateContent(`Sorry, there was an error: ${error.message}`)
            .markCompleted();
          setChats({ ...updatedChatsSnapshot });
        }
      }
    }
  };

  // Function to sync message with backend
  const syncMessageWithBackend = async (message, botId) => {
    if (getUserAuth()) {
      try {
        await createChatHistoryMessage(message.id, botId, message);
      } catch (error) {
        console.error("Failed to sync message with backend:", error);
      }
    }
  };

  // Function to load chat history from backend
  const loadChatHistoryFromBackend = async (botId) => {
    if (!getUserAuth() || !botId) {
      return null;
    }

    try {
      const response = await getChatHistory(botId);
      if (response.status === "success" && response.data) {
        // Convert API messages to ChatMessage objects
        const messages = response.data.map(convertAPIMessageToChatMessage);

        // Create chat with messages
        const chat = new Chat({
          botId,
          messages: messages.reverse(), // API returns newest first, we want oldest first
        });

        return chat;
      }
    } catch (error) {
      console.error("Failed to load chat history from backend:", error);
    }

    return null;
  };

  // Function to get local chat history from localStorage
  const getLocalChatHistory = (botId) => {
    try {
      const storedChats = localStorage.getItem("chats");
      if (storedChats) {
        const parsedChats = JSON.parse(storedChats);
        if (parsedChats[botId]) {
          // Convert stored chat data back to Chat instance
          return new Chat(parsedChats[botId]);
        }
      }
    } catch (error) {
      console.error("Failed to get local chat history:", error);
    }
    return null;
  };

  // Function to sync local chat history to backend
  const syncLocalChatToBackend = async (localChat) => {
    if (
      !getUserAuth() ||
      !localChat ||
      !localChat.messages ||
      localChat.messages.length === 0
    ) {
      return;
    }

    try {
      // Convert local chat to sync format
      const chatItems = convertChatToSyncFormat(localChat);
      if (chatItems.length > 0) {
        console.log(`Syncing ${chatItems.length} local messages to backend`);
        const result = await syncChatHistory(chatItems);
        console.log("Local chat sync result:", result);
        return result;
      }
    } catch (error) {
      console.error("Error syncing local chat to backend:", error);
      throw error;
    }
  };

  const updateLastMessage = () => {
    // Use currentChat if available, otherwise fall back to chats[currentBotId]
    const chat = currentChat || (chats[currentBotId] && chats[currentBotId]);
    const currentChatLastMessage = chat && chat.getLastMessage();
    // console.log("Current chat last message:", currentChatLastMessage);
    if (!currentChatLastMessage) {
      setLastMessage(null);
      return;
    }
    setLastMessage({ ...currentChatLastMessage });
  };

  const resendMessage = (message) => {
    if (!message || !currentBotId) return;
    sendMessage(message, true);
  };

  const deleteMessage = async (messageId) => {
    if (!messageId) return;

    try {
      // Call API to delete the message from backend
      await deleteChatHistoryMessage(messageId);

      // If API call succeeds, remove from local state
      const updatedChats = { ...chats };
      if (updatedChats[currentBotId]) {
        const messageToDelete =
          updatedChats[currentBotId].getMessage(messageId);

        if (messageToDelete) {
          updatedChats[currentBotId].removeMessage(messageId);
        }

        if (updatedChats[currentBotId].messages.length === 0) {
          sendHelloMessage();
        }

        updateLastMessage();
      }

      setChats({ ...updatedChats });
    } catch (error) {
      console.error("Failed to delete message:", error);
      // Show user-friendly error message
      const errorMessage = error.message.includes("404")
        ? "Message not found or already deleted"
        : `Failed to delete message: ${error.message}`;
      alert(errorMessage);
    }
  };

  const toggleAutoTTS = () => {
    setAutoTTSEnabled(!autoTTSEnabled);
  };

  const toggleChatSuggestions = () => {
    setChatSuggestionsEnabled(!chatSuggestionsEnabled);
  };

  // Helper function to clear mission for mission-type bots
  const clearMissionForBot = (botId) => {
    if (!botId || !mission) return;

    mission.steps.forEach((step) => {
      step.status = "pending"; // Reset step status
    });
    setMission({ ...mission });
  };

  // Helper function to create mission for mission-type bots
  const createMissionForBot = async (botId) => {
    const bot = bots.find((b) => b.id === botId);
    if (!bot || bot.bot_type !== "mission" || !bot.hasMissions()) {
      return null;
    }

    try {
      const missionDetails = bot.getMissions();
      const response = await createMission(missionDetails, apiKey);

      if (response.mission) {
        setMission({ ...response.mission });
        return response;
      }
    } catch (error) {
      console.error("Error creating mission:", error);
    }
    return null;
  };

  // Helper function to check mission status
  const checkMissionStatusForBot = async (botId) => {
    const bot = bots.find((b) => b.id === botId);

    if (!bot || bot.bot_type !== "mission" || !currentChat || !mission) {
      console.log("No valid mission found for bot:", {
        botId,
        bot,
        currentChat,
        mission,
      });
      return null;
    }

    try {
      const conversationHistory = currentChat.buildConversationHistory(-1);
      const checkMission = JSON.parse(JSON.stringify(mission));
      checkMission.steps?.forEach((step) => {
        step.status = "pending"; // Reset step status for checking
      });

      const response = await checkMissionStatus(
        checkMission,
        JSON.stringify(conversationHistory),
        apiKey
      );

      if (response && response.updated_mission) {
        // Update mission status if changed
        const updatedMissions = { ...response.updated_mission };
        setBeforeMission({ ...mission });
        setMission(updatedMissions);
      }

      return response;
    } catch (error) {
      console.error("Error checking mission status:", error);
    }
    return null;
  };

  // Save missions to localStorage when missions state changes
  useEffect(() => {
    if (mission && currentBotId) {
      const cacheKey = getMissionCacheKey(currentBotId);
      localStorage.setItem(cacheKey, JSON.stringify(mission));
    }
  }, [mission]);

  // Periodically sync chats to backend
  useEffect(() => {
    if (!apiKey) return;

    // Sync chats every 5 minutes
    const syncInterval = setInterval(() => {
      syncChatsToBackend();
    }, 5 * 60 * 1000); // 5 minutes

    // Also sync on initial load if we have chat data
    if (Object.keys(chats).length > 0) {
      syncChatsToBackend();
    }

    return () => {
      clearInterval(syncInterval);
    };
  }, [apiKey, chats]);

  // Add event listener for before unload to sync chats
  useEffect(() => {
    const syncBeforeUnload = async (e) => {
      // This won't actually await the sync as beforeunload doesn't wait, but it will start the process
      syncChatsToBackend();
    };

    window.addEventListener("beforeunload", syncBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", syncBeforeUnload);
    };
  }, [chats, apiKey]);

  // Custom function to set current bot ID with sync
  const setCurrentBotWithSync = (botId) => {
    // If we have a current bot ID, sync its chat before switching
    if (currentBotId && chats[currentBotId] && apiKey) {
      syncChatsToBackend();
    }
    setCurrentBotId(botId);
  };

  return {
    bots,
    setBots: (newBots) =>
      setBots(newBots.map((bot) => (bot instanceof Bot ? bot : new Bot(bot)))),
    currentBotId,
    setCurrentBotId: setCurrentBotWithSync,
    chats,
    setChats,
    currentChat,
    setCurrentChat,
    autoTTSEnabled,
    setAutoTTSEnabled,
    chatSuggestionsEnabled,
    setChatSuggestionsEnabled,
    apiKey,
    setApiKey,
    clearChatHistory,
    sendMessage,
    resendMessage,
    deleteMessage,
    toggleAutoTTS,
    toggleChatSuggestions,
    lastMessage,
    setLastMessage,
    // Mission-related functions and state
    mission,
    setMission,
    beforeMission,
    setBeforeMission,
    createMissionForBot,
    checkMissionStatusForBot,
    // Backend sync functions
    syncMessageWithBackend,
    loadChatHistoryFromBackend,
    syncChatsToBackend,
    getLocalChatHistory,
    syncLocalChatToBackend,
  };
};
