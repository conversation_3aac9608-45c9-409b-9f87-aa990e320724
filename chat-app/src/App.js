import React, { useState, useEffect, useRef } from "react";
import { useUrlParams } from "./hooks/useUrlParams";
import BotList from "./components/BotList/BotList";
import ChatPanel from "./components/ChatPanel/ChatPanel";
import AddBotModal from "./components/Modal/AddBotModal";
import ApiKeyModal from "./components/Modal/ApiKeyModal";
import PermissionModal from "./components/Modal/PermissionModal";
import { useChatState } from "./hooks/useChatState";
import {
  fetchChatbots,
  saveChatbot,
  deleteChatbot,
} from "./services/apiServices";
import { uniqueId } from "./utils/uniqueId";
import ErrorBoundary from "./components/ErrorBoundary";
import { Bo<PERSON>, Chat, ChatMessage } from "./types";
import "./App.css";

const App = () => {
  const { getApiKeyFromUrl } = useUrlParams();

  const {
    bots,
    setBots,
    currentBotId,
    setCurrentBotId,
    chats,
    setChats,
    currentChat,
    setCurrentChat,
    autoTTSEnabled,
    chatSuggestionsEnabled,
    apiKey,
    setApiKey,
    clearChatHistory,
    sendMessage,
    resendMessage,
    deleteMessage,
    toggleAutoTTS,
    toggleChatSuggestions,
    lastMessage,
    mission,
    beforeMission,
    syncChatsToBackend,
  } = useChatState();

  // Debug function to manually sync chats (for development)
  const handleManualSync = async () => {
    try {
      console.log("Manually triggering chat sync...");
      const result = await syncChatsToBackend();
      console.log("Manual sync completed:", result);
      alert("Chat sync completed! Check console for details.");
    } catch (error) {
      console.error("Manual sync failed:", error);
      alert("Chat sync failed! Check console for error details.");
    }
  };

  // Add global debug function for development
  useEffect(() => {
    if (
      typeof window !== "undefined" &&
      process.env.NODE_ENV === "development"
    ) {
      window.manualSyncChats = handleManualSync;
    }
  }, [syncChatsToBackend]);

  const [showAddBotModal, setShowAddBotModal] = useState(false);
  const [showApiKeyModal, setShowApiKeyModal] = useState(false);
  const [editBot, setEditBot] = useState(null);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [microphonePermission, setMicrophonePermission] = useState(null);
  const [isBotPanelCollapsed, setIsBotPanelCollapsed] = useState(false);
  const fileInputRef = useRef(null);

  // Set API key from URL parameter on component mount
  useEffect(() => {
    const urlApiKey = getApiKeyFromUrl();
    if (urlApiKey && urlApiKey !== apiKey) {
      setApiKey(urlApiKey);
    }
  }, [getApiKeyFromUrl, apiKey, setApiKey]);

  // Fetch bots from API if API key is available
  useEffect(() => {
    if (apiKey) {
      const loadBots = async () => {
        try {
          const botsList = await fetchChatbots(apiKey);
          setBots(botsList);
        } catch (error) {
          console.error("Failed to load chatbots:", error);
          // Fall back to local storage if API fails
          setBots(JSON.parse(localStorage.getItem("bots")) || []);
        }
      };
      loadBots();
    }
  }, [apiKey]); // Remove setBots from dependency array

  // Permission modal logic
  useEffect(() => {
    // Check if we should show the permission modal
    const hasAskedPermission = localStorage.getItem(
      "microphone-permission-asked"
    );

    if (!hasAskedPermission) {
      // Check current permission status
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.permissions
          .query({ name: "microphone" })
          .then((result) => {
            setMicrophonePermission(result.state);
            if (result.state === "prompt") {
              setShowPermissionModal(true);
            }
          })
          .catch(() => {
            // Fallback for browsers that don't support permissions API
            setShowPermissionModal(true);
          });
      }
    } else {
      // Check current permission status
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        navigator.permissions
          .query({ name: "microphone" })
          .then((result) => {
            setMicrophonePermission(result.state);
          })
          .catch(() => {
            setMicrophonePermission("unknown");
          });
      }
    }
  }, []);

  const requestMicrophonePermission = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicrophonePermission("granted");
      localStorage.setItem("microphone-permission-asked", "true");
      setShowPermissionModal(false);
    } catch (error) {
      setMicrophonePermission("denied");
      localStorage.setItem("microphone-permission-asked", "true");
      setShowPermissionModal(false);
    }
  };

  const closePermissionModal = () => {
    localStorage.setItem("microphone-permission-asked", "true");
    setShowPermissionModal(false);
  };

  const openAddBotModal = () => {
    setEditBot(null);
    setShowAddBotModal(true);
  };

  const openEditBotModal = (bot) => {
    setEditBot(bot);
    setShowAddBotModal(true);
  };

  const closeModal = () => {
    setShowAddBotModal(false);
    setShowApiKeyModal(false);
    setEditBot(null);
  };

  const saveBotData = async (
    name,
    persona,
    botType,
    imageUrl,
    voice,
    voiceSpeed,
    helloMessage,
    extraData
  ) => {
    if (!name) {
      alert("Please enter a bot name");
      return;
    }

    try {
      let bot;
      if (editBot) {
        // Update existing bot
        bot = editBot.update({
          name,
          persona,
          bot_type: botType || "normal", // Include bot type
          image_url: imageUrl || null,
          voice: voice || "en_female_amanda_mars_bigtts",
          voice_speed: voiceSpeed || 1.0, // Include voice speed
          hello_message: helloMessage || "", // Include hello message
          extra_data: extraData, // Include extra data
        });
      } else {
        // Create new bot
        bot = new Bot({
          name,
          persona,
          bot_type: botType || "normal", // Include bot type
          image_url: imageUrl || null,
          voice: voice || "en_female_amanda_mars_bigtts",
          voice_speed: voiceSpeed || 1.0, // Include voice speed
          hello_message: helloMessage || "", // Include hello message
          extra_data: extraData, // Include extra data
        });
      }

      // Save to API
      if (apiKey) {
        console.log("Saving bot to API:", bot.toAPIFormat());
        const savedBotData = await saveChatbot(bot.toAPIFormat(), apiKey);
        const savedBot = Bot.fromAPI(savedBotData);

        if (editBot) {
          // Replace the edited bot
          const updatedBots = bots.map((b) =>
            b.id === editBot.id ? savedBot : b
          );
          setBots(updatedBots);
        } else {
          // Add the new bot
          setBots([...bots, savedBot]);

          // Initialize chat history for this bot
          const updatedChats = { ...chats };
          updatedChats[savedBot.id] = Chat.createEmpty(savedBot.id, savedBot);
          setChats(updatedChats);

          if (!currentBotId) {
            setCurrentBotId(savedBot.id);
          }
        }
      } else {
        // Local-only storage
        if (editBot) {
          const updatedBots = bots.map((b) => (b.id === editBot.id ? bot : b));
          setBots(updatedBots);
        } else {
          setBots([...bots, bot]);

          // Initialize chat history
          const updatedChats = { ...chats };
          updatedChats[bot.id] = Chat.createEmpty(bot.id, bot);
          setChats(updatedChats);

          if (!currentBotId) {
            setCurrentBotId(bot.id);
          }
        }
      }
      closeModal();
    } catch (error) {
      console.error("Error saving chatbot:", error);
      alert(`Failed to save chatbot: ${error.message}`);
    }
  };

  const saveApiKey = (key) => {
    setApiKey(key);
    closeModal();
  };

  const exportChat = () => {
    if (!currentBotId || !currentChat || currentChat.isEmpty()) {
      alert("No chat history to export.");
      return;
    }

    const bot = bots.find((b) => b.id === currentBotId);
    if (!bot) return;

    const chatData = {
      botName: bot.name,
      botPersona: bot.persona,
      messages: currentChat.toStorageFormat(),
      exportDate: new Date().toISOString(),
    };

    const dataStr = JSON.stringify(chatData, null, 2);
    const dataUri =
      "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);
    const exportFileDefaultName = `chat-with-${bot.name.replace(
      /\s+/g,
      "-"
    )}-${new Date().toISOString().slice(0, 10)}.json`;

    const linkElement = document.createElement("a");
    linkElement.setAttribute("href", dataUri);
    linkElement.setAttribute("download", exportFileDefaultName);
    linkElement.click();
  };

  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      importChat(file);
    }
    // Reset the input value to allow selecting the same file again
    event.target.value = "";
  };

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    } else {
      alert("File input not available. Please check your browser settings.");
    }
  };

  const importChat = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedData = JSON.parse(e.target.result);

        if (!importedData.messages || !Array.isArray(importedData.messages)) {
          throw new Error("Invalid chat format");
        }

        // Convert imported messages to ChatMessage instances
        const messagesWithIds = importedData.messages.map((msgData) => {
          return ChatMessage.fromStorage({
            ...msgData,
            id: msgData.id || uniqueId(msgData.sender),
          });
        });

        if (
          window.confirm(`Import chat with ${messagesWithIds.length} messages?`)
        ) {
          let botId = currentBotId;

          if (!botId || window.confirm("Import as a new bot?")) {
            // Create new bot
            const newBot = new Bot({
              name: importedData.botName || "Imported Bot",
              persona:
                importedData.botPersona || "A bot with imported chat history.",
            });

            setBots([...bots, newBot]);
            botId = newBot.id;
          }

          // Create chat with imported messages
          const importedChat = Chat.createEmpty(botId);
          messagesWithIds.forEach((msg) => importedChat.addMessage(msg));

          // Import the chat
          setChats({
            ...chats,
            [botId]: importedChat,
          });

          // Select the bot
          setCurrentBotId(botId);

          alert("Chat imported successfully!");
        }
      } catch (error) {
        alert(`Error importing chat: ${error.message}`);
      }
    };

    reader.readAsText(file);
  };

  const deleteBotData = async (botId) => {
    if (window.confirm("Are you sure you want to delete this bot?")) {
      try {
        if (apiKey) {
          // Delete from API
          await deleteChatbot(botId, apiKey);
        }

        // Remove from local state
        const updatedBots = bots.filter((bot) => bot.id !== botId);
        setBots(updatedBots);

        // If the current bot is deleted, select another bot or set to null
        if (currentBotId === botId) {
          setCurrentBotId(updatedBots.length > 0 ? updatedBots[0].id : null);
        }

        // Remove chat history
        const updatedChats = { ...chats };
        delete updatedChats[botId];
        setChats(updatedChats);
      } catch (error) {
        console.error("Error deleting chatbot:", error);
        alert(`Failed to delete chatbot: ${error.message}`);
      }
    }
  };

  // Auto-collapse bot panel on mobile when a bot is selected
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth <= 768) {
        // On mobile, auto-collapse when bot is selected
        if (currentBotId) {
          setIsBotPanelCollapsed(true);
        }
      } else {
        // On desktop, always show bot panel
        setIsBotPanelCollapsed(false);
      }
    };

    handleResize(); // Check on mount
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [currentBotId]);

  // Close bot panel when clicking overlay on mobile
  const handleOverlayClick = (e) => {
    if (window.innerWidth <= 768 && e.target === e.currentTarget) {
      setIsBotPanelCollapsed(true);
    }
  };

  const toggleBotPanel = () => {
    setIsBotPanelCollapsed(!isBotPanelCollapsed);
  };

  // Enhanced bot selection for mobile
  const handleBotSelect = (botId) => {
    setCurrentBotId(botId);
    // Auto-collapse on mobile after selection
    if (window.innerWidth <= 768) {
      setIsBotPanelCollapsed(true);
    }
  };

  return (
    <div className="app">
      <ErrorBoundary>
        <div className="app-header">
          <div className="header-left">
            <button
              className="header-btn collapse-toggle"
              onClick={toggleBotPanel}
              title={isBotPanelCollapsed ? "Show Bot Panel" : "Hide Bot Panel"}
            >
              {isBotPanelCollapsed ? "☰" : "✕"}
            </button>
            <h1>🤖 Chat Bots</h1>
          </div>
          <div className="header-controls">
            {/* Permission status indicator */}
            {microphonePermission === "denied" && (
              <div
                className="permission-warning"
                title="Microphone permission required for voice messages"
              >
                🎤❌
              </div>
            )}
            {microphonePermission === "granted" && (
              <div
                className="permission-status"
                title="Microphone permission granted"
              >
                🎤✅
              </div>
            )}

            <button
              className={`header-btn ${autoTTSEnabled ? "active" : ""}`}
              onClick={toggleAutoTTS}
              title="Toggle Auto Text-to-Speech"
            >
              {autoTTSEnabled ? "🔊" : "🔇"}
            </button>
          </div>
        </div>

        <div
          className={`app-container ${
            isBotPanelCollapsed ? "bot-panel-collapsed" : ""
          }`}
          onClick={handleOverlayClick}
        >
          <div
            className={`bot-panel-wrapper ${
              isBotPanelCollapsed ? "collapsed" : ""
            }`}
          >
            <BotList
              bots={bots}
              currentBotId={currentBotId}
              onSelectBot={handleBotSelect}
              onAddBot={openAddBotModal}
              onEditBot={openEditBotModal}
              onDeleteBot={deleteBotData}
              onApiKeySettings={() => setShowApiKeyModal(true)}
            />
          </div>

          <ChatPanel
            currentBot={bots.find((bot) => bot.id === currentBotId)}
            currentChat={
              currentChat ||
              Chat.createEmpty(
                currentBotId,
                bots.find((bot) => bot.id === currentBotId)
              )
            }
            autoTTSEnabled={autoTTSEnabled}
            onSendMessage={sendMessage}
            onResendMessage={resendMessage}
            onClearHistory={clearChatHistory}
            onSyncChatHistory={handleManualSync}
            // onExportChat={exportChat}
            // onImportChat={importChat}
            // onToggleAutoTTS={toggleAutoTTS}
            onToggleChatSuggestions={toggleChatSuggestions}
            chatSuggestionsEnabled={chatSuggestionsEnabled}
            onDeleteMessage={deleteMessage}
            apiKey={apiKey}
            lastMessage={lastMessage}
            missionData={mission}
            beforeMissionData={beforeMission}
          />

          {showAddBotModal && (
            <AddBotModal
              onClose={closeModal}
              onSave={saveBotData}
              editBot={editBot}
              apiKey={apiKey}
            />
          )}

          {showApiKeyModal && (
            <ApiKeyModal
              onClose={closeModal}
              onSave={saveApiKey}
              apiKey={apiKey}
            />
          )}

          {showPermissionModal && (
            <PermissionModal
              onRequestPermission={requestMicrophonePermission}
              onClose={closePermissionModal}
            />
          )}
        </div>
      </ErrorBoundary>
    </div>
  );
};

export default App;
