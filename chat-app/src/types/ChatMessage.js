import { uniqueId } from "../utils/uniqueId";
import { Chat } from "./Chat";

import { ChatMessageSenderType } from "./ChatMessageType";

export class ChatMessage {
  constructor({
    id = null,
    role = null, // Maps to role field in ChatMessageItem
    content = "", // Maps to content field in ChatMessageItem
    sender = ChatMessageSenderType.USER, // Kept for backward compatibility
    text = "", // Kept for backward compatibility, maps to content
    type = "text", // Maps to type field (text, audio, image)
    timestamp = null,
    isTyping = false,
    isPlayingAudio = false,
    metadata = {},
    // New fields from ChatMessageItem
    asr = null, // ASR transcription result
    duration = null, // Duration of audio message (if applicable)
    tts = null, // TTS audio URL or data
    translation = null, // Translation result
    grammar_analysis = null, // Grammar analysis results
    pronunciation_assessment = null, // Pronunciation assessment results
  } = {}) {
    this.id = id || uniqueId(sender);

    // Handle backward compatibility for role/sender mapping
    if (role) {
      this.role = role;
      this.sender =
        role === "user"
          ? ChatMessageSenderType.USER
          : role === "assistant"
          ? ChatMessageSenderType.BOT
          : sender;
    } else {
      this.sender = sender;
      this.role =
        sender === ChatMessageSenderType.USER
          ? "user"
          : sender === ChatMessageSenderType.BOT
          ? "assistant"
          : "system";
    }

    // Handle content/text mapping for backward compatibility
    this.content = content || text;
    this.text = this.content; // Keep text for backward compatibility
    this.duration = duration || null; // Duration for audio messages

    this.type = type;
    this.timestamp = timestamp || new Date().toISOString();
    this.isTyping = isTyping;
    this.isPlayingAudio = isPlayingAudio;
    this.metadata = metadata;

    // New ChatMessageItem fields
    this.asr = asr;
    this.tts = tts;
    this.translation = translation;
    this.grammar_analysis = grammar_analysis;
    this.pronunciation_assessment = pronunciation_assessment;
  }

  // Validation methods
  isValid() {
    return (
      [ChatMessageSenderType.USER, ChatMessageSenderType.BOT].includes(
        this.sender
      ) && this.content.trim().length > 0
    );
  }

  // Check if this is a voice message
  isVoice() {
    return this.type === "audio";
  }

  // Get audio URL if it's a voice message
  getAudioUrl() {
    // Check TTS field first, then fallback to parsing content
    if (this.tts) {
      return this.tts;
    }
    return this.isVoice() ? this.content : null;
  }

  // Get audio duration if it's a voice message
  getAudioDuration() {
    return this.isVoice() ? this.duration : null;
  }

  // Check if message is from user
  isFromUser() {
    return this.sender === ChatMessageSenderType.USER;
  }

  // Check if message is from bot
  isFromBot() {
    return this.sender === ChatMessageSenderType.BOT;
  }

  isChat() {
    return this.isFromBot() || this.isFromUser();
  }

  // Check if message is currently being typed
  isCurrentlyTyping() {
    return this.isTyping;
  }

  // Update message content (for streaming)
  updateContent(newText) {
    this.content = newText;
    this.text = newText; // Keep backward compatibility
    this.timestamp = new Date().toISOString();
    return this;
  }

  // Mark message as completed (stop typing)
  markCompleted() {
    this.isTyping = false;
    this.timestamp = new Date().toISOString();
    return this;
  }

  // Set typing state
  setTyping(isTyping) {
    this.isTyping = isTyping;
    return this;
  }

  // Set audio playing state
  setPlayingAudio(isPlaying) {
    this.isPlayingAudio = isPlaying;
    return this;
  }

  // Add metadata
  addMetadata(key, value) {
    this.metadata[key] = value;
    return this;
  }

  // Get metadata
  getMetadata(key) {
    return this.metadata[key];
  }

  // New methods for ChatMessageItem fields

  // Set ASR result
  setASR(asrResult) {
    this.asr = asrResult;
    return this;
  }

  // Get ASR result
  getASR() {
    return this.asr;
  }

  setDuration(duration) {
    this.duration = duration;
    return this;
  }

  getDuration() {
    return this.duration;
  }

  // Set TTS audio URL/data
  setTTS(ttsData) {
    this.tts = ttsData;
    return this;
  }

  // Get TTS audio URL/data
  getTTS() {
    return this.tts;
  }

  // Set translation
  setTranslation(translationResult) {
    this.translation = translationResult;
    return this;
  }

  // Get translation
  getTranslation() {
    return this.translation;
  }

  // Set grammar analysis
  setGrammarAnalysis(grammarAnalysis) {
    this.grammar_analysis = grammarAnalysis;
    return this;
  }

  // Get grammar analysis
  getGrammarAnalysis() {
    return this.grammar_analysis;
  }

  // Set pronunciation assessment
  setPronunciationAssessment(pronunciationAssessment) {
    this.pronunciation_assessment = pronunciationAssessment;
    return this;
  }

  // Get pronunciation assessment
  getPronunciationAssessment() {
    return this.pronunciation_assessment;
  }

  // Check if message has ASR result
  hasASR() {
    return this.asr !== null && this.asr !== undefined;
  }

  // Check if message has TTS
  hasTTS() {
    return this.tts !== null && this.tts !== undefined;
  }

  // Check if message has translation
  hasTranslation() {
    return this.translation !== null && this.translation !== undefined;
  }

  // Check if message has grammar analysis
  hasGrammarAnalysis() {
    return (
      this.grammar_analysis !== null && this.grammar_analysis !== undefined
    );
  }

  // Check if message has pronunciation assessment
  hasPronunciationAssessment() {
    return (
      this.pronunciation_assessment !== null &&
      this.pronunciation_assessment !== undefined
    );
  }

  // Format timestamp for display
  getFormattedTimestamp() {
    if (!this.timestamp) return "";
    const date = new Date(this.timestamp);
    return date.toLocaleTimeString();
  }

  // Convert to storage format
  toStorageFormat() {
    return {
      id: this.id,
      role: this.role,
      content: this.content,
      sender: this.sender,
      text: this.text, // Keep for backward compatibility
      type: this.type,
      timestamp: this.timestamp,
      metadata: this.metadata,
      duration: this.duration, // Include duration for voice messages
      asr: this.asr,
      tts: this.tts,
      translation: this.translation,
      grammar_analysis: this.grammar_analysis,
      pronunciation_assessment: this.pronunciation_assessment,
    };
  }

  // Convert to API format (ChatMessageItem)
  toAPIFormat() {
    return {
      role: this.role,
      content: this.content,
      id: this.id,
      type: this.type,
      // Include optional fields if they exist
      ...(this.duration && { duration: this.duration }),
      ...(this.asr && { asr: this.asr }),
      ...(this.tts && { tts: this.tts }),
      ...(this.translation && { translation: this.translation }),
      ...(this.grammar_analysis && { grammar_analysis: this.grammar_analysis }),
      ...(this.pronunciation_assessment && {
        pronunciation_assessment: this.pronunciation_assessment,
      }),
    };
  }

  setId(id) {
    this.id = id;
    return this;
  }

  // Create from storage data
  static fromStorage(storageData) {
    return new ChatMessage(storageData);
  }

  // Create from API data (ChatMessageItem format)
  static fromAPI(apiData) {
    return new ChatMessage({
      role: apiData.role,
      content: apiData.content,
      type: apiData.type || "text",
      duration: apiData.duration,
      asr: apiData.asr,
      tts: apiData.tts,
      translation: apiData.translation,
      grammar_analysis: apiData.grammar_analysis,
      pronunciation_assessment: apiData.pronunciation_assessment,
    });
  }

  // Create a typing message
  static createTypingMessage(sender = ChatMessageSenderType.BOT) {
    return new ChatMessage({
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Use temporary ID that will be replaced by server
      sender,
      content: "",
      text: "",
      isTyping: true,
    });
  }

  // Create a user message
  static createUserMessage(content, type = null) {
    // Auto-detect voice messages and set appropriate type
    const detectedType = type || (content.type === "audio" ? "audio" : "text");

    // For voice messages, extract the audio URL as the main content but preserve duration
    let actualContent = content;
    let duration = null;
    if (detectedType === "audio" && content.type === "audio") {
      actualContent = content.content;
      duration = content.duration;
    }

    return new ChatMessage({
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Use temporary ID that will be replaced by server
      sender: ChatMessageSenderType.USER,
      content: actualContent,
      text: actualContent, // Backward compatibility
      type: detectedType,
      duration: duration, // Preserve duration for voice messages
    });
  }

  // Create a bot message
  static createBotMessage(content, isTyping = false, type = "text") {
    return new ChatMessage({
      sender: ChatMessageSenderType.BOT,
      content,
      text: content, // Backward compatibility
      type,
      isTyping,
    });
  }

  // Create a voice message (user)
  static createUserVoiceMessage(audioUrl, duration, asrTranscription = null) {
    return new ChatMessage({
      sender: ChatMessageSenderType.USER,
      content: audioUrl,
      text: audioUrl,
      duration, // Set duration for voice messages
      type: "audio",
      asr: asrTranscription,
    });
  }

  // Create a voice message (bot)
  static createBotVoiceMessage(content, ttsAudioUrl) {
    return new ChatMessage({
      sender: ChatMessageSenderType.BOT,
      content,
      text: content,
      type: "audio",
      tts: ttsAudioUrl,
    });
  }

  static createSystemMessage(messageType) {
    return new ChatMessage({
      sender: ChatMessageSenderType.SYSTEM,
      text: "",
      metadata: { messageType },
    });
  }
}
