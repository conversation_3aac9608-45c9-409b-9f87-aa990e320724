import { uniqueId } from "../utils/uniqueId";
import { ChatMessageSenderType } from "./ChatMessageType";

export class Bot {
  constructor({
    id = null,
    name = "",
    persona = "",
    bot_type = "normal", // Add bot type property with default value
    image_url = null,
    voice = "en_female_amanda_mars_bigtts",
    voice_speed = 1.0, // Add voice speed property with default value
    model = "doubao-1-5-pro-32k-250115", // Add model property with default value
    hello_message = "", // Add hello message property
    extra_data = null, // Add extra data property
    created_at = null,
    updated_at = null,
  } = {}) {
    this.id = id || uniqueId(ChatMessageSenderType.BOT);
    this.name = name;
    this.persona = persona;
    this.bot_type = bot_type; // Add bot type
    this.image_url = image_url;
    this.voice = voice;
    this.voice_speed = voice_speed; // Add voice speed
    this.model = model; // Add model
    this.hello_message = hello_message; // Add hello message
    this.extra_data = extra_data; // Add extra data
    this.created_at = created_at || new Date().toISOString();
    this.updated_at = updated_at || new Date().toISOString();
  }

  // Validation methods
  isValid() {
    return this.name.trim().length > 0 && this.persona.trim().length > 0;
  }

  // Get avatar display (either image or first letter of name)
  getAvatarDisplay() {
    return this.image_url || this.name.charAt(0).toUpperCase();
  }

  // Get truncated persona for display
  getTruncatedPersona(maxLength = 30) {
    if (this.persona.length <= maxLength) {
      return this.persona;
    }
    return this.persona.substring(0, maxLength) + "...";
  }

  // Update bot properties
  update(updates) {
    Object.assign(this, updates);
    this.updated_at = new Date().toISOString();
    return this;
  }

  // Get missions for mission-type bots
  getMissions() {
    if (this.bot_type === "mission" && this.extra_data?.missions) {
      return this.extra_data.missions;
    }
    return "";
  }

  // Set missions for mission-type bots
  setMissions(missions) {
    if (this.bot_type === "mission") {
      this.extra_data = { ...this.extra_data, missions };
      this.updated_at = new Date().toISOString();
    }
  }

  // Get title from extra_data
  getTitle() {
    return this.extra_data?.title || "";
  }

  // Set title in extra_data
  setTitle(title) {
    this.extra_data = { ...this.extra_data, title };
    this.updated_at = new Date().toISOString();
  }

  // Check if bot has a title
  hasTitle() {
    return this.extra_data?.title && this.extra_data.title.trim().length > 0;
  }

  // Check if bot has missions defined
  hasMissions() {
    return (
      this.bot_type === "mission" &&
      this.extra_data?.missions &&
      this.extra_data.missions.trim().length > 0
    );
  }

  // Convert to API format
  toAPIFormat() {
    return {
      id: this.id,
      name: this.name,
      persona: this.persona,
      bot_type: this.bot_type, // Include bot type in API format
      image_url: this.image_url,
      voice: this.voice,
      voice_speed: this.voice_speed, // Include voice speed in API format
      model: this.model, // Include model in API format
      hello_message: this.hello_message, // Include hello message in API format
      extra_data: this.extra_data, // Include extra data in API format
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }

  // Create from API response
  static fromAPI(apiData) {
    return new Bot({
      id: apiData.id,
      name: apiData.name,
      persona: apiData.persona,
      bot_type: apiData.bot_type || "normal", // Include bot type
      image_url: apiData.image_url,
      voice: apiData.voice,
      voice_speed: apiData.voice_speed || 1.0, // Include voice speed
      model: apiData.model || "doubao-1-5-pro-32k-250115", // Include model
      hello_message: apiData.hello_message || "", // Include hello message
      extra_data: apiData.extra_data, // Include extra data
      created_at: apiData.created_at,
      updated_at: apiData.updated_at,
    });
  }

  // Create from localStorage data
  static fromStorage(storageData) {
    return new Bot({
      id: storageData.id,
      name: storageData.name,
      persona: storageData.persona,
      bot_type: storageData.bot_type || "normal", // Include bot type
      image_url: storageData.image_url,
      voice: storageData.voice,
      voice_speed: storageData.voice_speed || 1.0, // Include voice speed
      model: storageData.model || "doubao-1-5-pro-32k-250115", // Include model
      hello_message: storageData.hello_message || "", // Include hello message
      extra_data: storageData.extra_data, // Include extra data
      created_at: storageData.created_at,
      updated_at: storageData.updated_at,
    });
  }

  // Get hello message with default fallback
  getHelloMessage() {
    if (this.hello_message && this.hello_message.trim()) {
      return this.hello_message.trim();
    }
    // Default hello message based on bot name
    return `Hello! I'm ${this.name}. How can I help you today?`;
  }
}
