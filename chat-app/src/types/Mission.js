import { uniqueId } from "../utils/uniqueId";

// Mission step status constants
export const MissionStepStatus = {
  PENDING: "pending",
  IN_PROGRESS: "in_progress",
  COMPLETED: "completed",
  FAILED: "failed",
};

// Mission status constants
export const MissionStatus = {
  ACTIVE: "active",
  COMPLETED: "completed",
  FAILED: "failed",
  PAUSED: "paused",
};

export class MissionStep {
  constructor({
    id = null,
    action = "",
    status = MissionStepStatus.PENDING,
    description = "",
    created_at = null,
    updated_at = null,
  } = {}) {
    this.id = id || uniqueId("step");
    this.action = action;
    this.status = status;
    this.description = description;
    this.created_at = created_at || new Date().toISOString();
    this.updated_at = updated_at || new Date().toISOString();
  }

  // Update step status
  updateStatus(newStatus) {
    if (Object.values(MissionStepStatus).includes(newStatus)) {
      this.status = newStatus;
      this.updated_at = new Date().toISOString();
    } else {
      throw new Error(`Invalid step status: ${newStatus}`);
    }
  }

  // Check if step is completed
  isCompleted() {
    return this.status === MissionStepStatus.COMPLETED;
  }

  // Check if step is in progress
  isInProgress() {
    return this.status === MissionStepStatus.IN_PROGRESS;
  }

  // Check if step has failed
  hasFailed() {
    return this.status === MissionStepStatus.FAILED;
  }

  // Get status icon for UI
  getStatusIcon() {
    switch (this.status) {
      case MissionStepStatus.COMPLETED:
        return "✅";
      case MissionStepStatus.IN_PROGRESS:
        return "🔄";
      case MissionStepStatus.FAILED:
        return "❌";
      default:
        return "⏳";
    }
  }

  // Get status color for UI
  getStatusColor() {
    switch (this.status) {
      case MissionStepStatus.COMPLETED:
        return "#28a745";
      case MissionStepStatus.IN_PROGRESS:
        return "#ffc107";
      case MissionStepStatus.FAILED:
        return "#dc3545";
      default:
        return "#6c757d";
    }
  }

  // Convert to plain object for API calls
  toJSON() {
    return {
      id: this.id,
      action: this.action,
      status: this.status,
      description: this.description,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }
}

export class Mission {
  constructor({
    id = null,
    title = "",
    description = "",
    objectives = [],
    steps = [],
    bot_id = null,
    created_at = null,
    updated_at = null,
  } = {}) {
    this.id = id || uniqueId("mission");
    this.title = title;
    this.description = description;
    this.objectives = Array.isArray(objectives) ? objectives : [];
    this.steps = Array.isArray(steps)
      ? steps.map((step) =>
          step instanceof MissionStep ? step : new MissionStep(step)
        )
      : [];
    this.bot_id = bot_id;
    this.created_at = created_at || new Date().toISOString();
    this.updated_at = updated_at || new Date().toISOString();
  }

  // Add a new objective
  addObjective(objective) {
    if (typeof objective === "string" && objective.trim()) {
      this.objectives.push(objective.trim());
      this.updated_at = new Date().toISOString();
    }
  }

  // Remove an objective by index
  removeObjective(index) {
    if (index >= 0 && index < this.objectives.length) {
      this.objectives.splice(index, 1);
      this.updated_at = new Date().toISOString();
    }
  }

  // Add a new step
  addStep(stepData) {
    const step =
      stepData instanceof MissionStep ? stepData : new MissionStep(stepData);
    this.steps.push(step);
    this.updated_at = new Date().toISOString();
    return step;
  }

  // Remove a step by ID
  removeStep(stepId) {
    const index = this.steps.findIndex((step) => step.id === stepId);
    if (index !== -1) {
      this.steps.splice(index, 1);
      this.updated_at = new Date().toISOString();
    }
  }

  // Update step status by ID
  updateStepStatus(stepId, newStatus) {
    const step = this.steps.find((step) => step.id === stepId);
    if (step) {
      step.updateStatus(newStatus);
      this.updated_at = new Date().toISOString();
      this.updateMissionStatus();
    }
  }

  // Auto-update mission status based on step completion
  updateMissionStatus() {
    if (this.steps.length === 0) {
      return;
    }
    this.updated_at = new Date().toISOString();
  }

  // Get progress percentage
  getProgressPercentage() {
    if (this.steps.length === 0) return 0;
    const completedSteps = this.steps.filter((step) =>
      step.isCompleted()
    ).length;
    return Math.round((completedSteps / this.steps.length) * 100);
  }

  // Check if mission is completed
  isCompleted() {
    return this.getStatus() === MissionStatus.COMPLETED;
  }

  // Check if mission has failed
  hasFailed() {
    return this.getStatus() === MissionStatus.FAILED;
  }

  // Check if mission is active
  isActive() {
    return this.getStatus() === MissionStatus.ACTIVE;
  }

  getStatus() {
    if (this.steps.every((step) => step.isCompleted())) {
      return MissionStatus.COMPLETED;
    } else if (this.steps.some((step) => step.hasFailed())) {
      return MissionStatus.FAILED;
    }
    return MissionStatus.ACTIVE;
  }

  reset() {
    this.steps.forEach((step) => step.updateStatus(MissionStepStatus.PENDING));
    this.updated_at = new Date().toISOString();
  }

  // Update mission from API response
  updateFromApiResponse(apiData) {
    if (apiData.title) this.title = apiData.title;
    if (apiData.description) this.description = apiData.description;
    if (Array.isArray(apiData.objectives)) this.objectives = apiData.objectives;

    if (Array.isArray(apiData.steps)) {
      this.steps = apiData.steps.map((stepData) =>
        stepData instanceof MissionStep ? stepData : new MissionStep(stepData)
      );
    }

    this.updated_at = new Date().toISOString();
  }

  // Convert to plain object for localStorage or API calls
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      objectives: this.objectives,
      steps: this.steps.map((step) => step.toJSON()),
      status: this.getStatus(),
      bot_id: this.bot_id,
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }

  // Create mission from localStorage data
  static fromLocalStorage(data) {
    try {
      const parsed = typeof data === "string" ? JSON.parse(data) : data;
      return new Mission(parsed);
    } catch (error) {
      console.error("Error parsing mission from localStorage:", error);
      return null;
    }
  }

  // Save mission to localStorage
  saveToLocalStorage(botId) {
    if (botId) {
      localStorage.setItem(`mission-${botId}`, JSON.stringify(this.toJSON()));
    }
  }

  // Load mission from localStorage
  static loadFromLocalStorage(botId) {
    if (!botId) return null;

    const savedData = localStorage.getItem(`mission-${botId}`);
    if (savedData) {
      return Mission.fromLocalStorage(savedData);
    }
    return null;
  }

  // Remove mission from localStorage
  static removeFromLocalStorage(botId) {
    if (botId) {
      localStorage.removeItem(`mission-${botId}`);
    }
  }
}

// Validation utilities
export const isMission = (obj) => obj instanceof Mission;
export const isMissionStep = (obj) => obj instanceof MissionStep;

// Factory functions
export const createMission = (data) => new Mission(data);
export const createMissionStep = (data) => new MissionStep(data);

// Helper function to validate mission status
export const isValidMissionStatus = (status) => {
  return Object.values(MissionStatus).includes(status);
};

// Helper function to validate step status
export const isValidStepStatus = (status) => {
  return Object.values(MissionStepStatus).includes(status);
};
