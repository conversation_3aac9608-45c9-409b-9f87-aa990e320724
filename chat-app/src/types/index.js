export { Bot } from "./Bot";
export { ChatMessage } from "./ChatMessage";
export { Chat } from "./Chat";
export {
  Mission,
  MissionStep,
  MissionStatus,
  MissionStepStatus,
  isMission,
  isMissionStep,
  createMission,
  createMissionStep,
  isValidMissionStatus,
  isValidStepStatus,
} from "./Mission";

// Type validation utilities
export const isBot = (obj) => obj instanceof Bot;
export const isChatMessage = (obj) => obj instanceof ChatMessage;
export const isChat = (obj) => obj instanceof Chat;

// Factory functions
export const createBot = (data) => new Bot(data);
export const createChatMessage = (data) => new ChatMessage(data);
export const createChat = (data) => new Chat(data);
