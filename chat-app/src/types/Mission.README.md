# Mission Management System

This document describes the Mission types and utilities for managing mission information in the chat application.

## Overview

The Mission management system provides structured types for creating, tracking, and updating missions and their associated steps. It includes localStorage integration and UI helpers for status display.

## Types

### Mission

The main Mission class that represents a mission with objectives and steps.

```javascript
import { Mission, createMission } from '../types';

// Create a new mission
const mission = createMission({
  title: "Learn JavaScript",
  description: "Master JavaScript fundamentals",
  objectives: ["Variables", "Functions", "Async programming"],
  bot_id: "bot-123"
});
```

#### Properties
- `id`: Unique mission identifier
- `title`: Mission title
- `description`: Detailed mission description  
- `objectives`: Array of objective strings
- `steps`: Array of MissionStep instances
- `status`: Mission status (active, completed, failed, paused)
- `bot_id`: Associated bot ID
- `created_at`: ISO timestamp
- `updated_at`: ISO timestamp

#### Methods
- `addObjective(objective)`: Add a new objective
- `removeObjective(index)`: Remove objective by index
- `addStep(stepData)`: Add a new step
- `removeStep(stepId)`: Remove step by ID
- `updateStepStatus(stepId, status)`: Update step status
- `getProgressPercentage()`: Get completion percentage
- `isCompleted()`: Check if mission is completed
- `isActive()`: Check if mission is active
- `saveToLocalStorage(botId)`: Save to localStorage
- `toJSON()`: Convert to plain object

#### Static Methods
- `loadFromLocalStorage(botId)`: Load mission from localStorage
- `removeFromLocalStorage(botId)`: Remove from localStorage
- `fromLocalStorage(data)`: Create from localStorage data

### MissionStep

Represents individual steps within a mission.

```javascript
import { MissionStep, MissionStepStatus } from '../types';

const step = new MissionStep({
  action: "Complete tutorial",
  status: MissionStepStatus.IN_PROGRESS,
  description: "Go through the onboarding tutorial"
});
```

#### Properties
- `id`: Unique step identifier
- `action`: Step action description
- `status`: Step status (pending, in_progress, completed, failed)
- `description`: Additional step description
- `created_at`: ISO timestamp
- `updated_at`: ISO timestamp

#### Methods
- `updateStatus(newStatus)`: Update step status
- `isCompleted()`: Check if completed
- `isInProgress()`: Check if in progress
- `hasFailed()`: Check if failed
- `getStatusIcon()`: Get status emoji icon
- `getStatusColor()`: Get status color
- `toJSON()`: Convert to plain object

## Constants

### MissionStatus
- `ACTIVE`: "active"
- `COMPLETED`: "completed"
- `FAILED`: "failed"
- `PAUSED`: "paused"

### MissionStepStatus
- `PENDING`: "pending"
- `IN_PROGRESS`: "in_progress"
- `COMPLETED`: "completed"
- `FAILED`: "failed"

## Usage Examples

### Creating a Mission

```javascript
import { createMission, MissionStepStatus } from '../types';

const mission = createMission({
  title: "User Onboarding",
  description: "Help new user get started",
  objectives: [
    "Create account",
    "Set up profile", 
    "Complete tutorial"
  ]
});

// Add steps
mission.addStep({
  action: "Register with email",
  status: MissionStepStatus.COMPLETED
});

mission.addStep({
  action: "Upload profile picture",
  status: MissionStepStatus.IN_PROGRESS
});
```

### Updating Mission Status

```javascript
// Update a specific step
mission.updateStepStatus(stepId, MissionStepStatus.COMPLETED);

// Check progress
console.log(`Mission is ${mission.getProgressPercentage()}% complete`);

// Update from API response
mission.updateFromApiResponse(apiResponseData);
```

### localStorage Integration

```javascript
// Save mission
mission.saveToLocalStorage(botId);

// Load mission
const savedMission = Mission.loadFromLocalStorage(botId);

// Remove mission
Mission.removeFromLocalStorage(botId);
```

### UI Integration

```javascript
// Get status display helpers
const icon = step.getStatusIcon(); // ✅, 🔄, ❌, ⏳
const color = step.getStatusColor(); // #28a745, #ffc107, etc.

// Display progress
<div>Progress: {mission.getProgressPercentage()}%</div>
<div>Status: {mission.status}</div>
```

## Integration with MissionModal

The `MissionModal` component has been updated to use these types:

1. Automatically converts API responses to Mission instances
2. Uses Mission methods for localStorage operations
3. Displays progress bars and status indicators
4. Handles step status updates through the Mission class

## API Integration

The Mission types work seamlessly with the existing API:

```javascript
// Create mission via API
const apiResponse = await createMission(missionDetails, apiKey);
const mission = createMission({
  ...apiResponse.mission,
  bot_id: currentBot.id
});

// Check mission status via API  
const statusResponse = await checkMissionStatus(
  mission.toJSON(), // Convert to plain object for API
  conversationHistory,
  apiKey
);

// Update mission with response
mission.updateFromApiResponse(statusResponse.updated_mission);
```

## Benefits

1. **Type Safety**: Structured data with validation
2. **Consistency**: Standardized mission and step handling
3. **UI Helpers**: Built-in methods for status display
4. **localStorage**: Automatic persistence management
5. **Progress Tracking**: Built-in progress calculation
6. **API Ready**: Easy serialization for API calls
7. **Extensible**: Easy to add new features and methods
