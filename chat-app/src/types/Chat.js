import { ChatMessage } from "./ChatMessage";
import {
  ChatMessageSenderType,
  ChatSystemMessage,
  ChatSystemMessageType,
} from "./ChatMessageType";

export class Chat {
  constructor({
    botId = null,
    helloMessage = "",
    messages = [],
    created_at = null,
    updated_at = null,
  } = {}) {
    this.botId = botId;
    this.helloMessage = helloMessage;
    this.messages = messages.map((msg) =>
      msg instanceof ChatMessage ? msg : ChatMessage.fromStorage(msg)
    );
    this.created_at = created_at || new Date().toISOString();
    this.updated_at = updated_at || new Date().toISOString();
  }

  // Add a message to the chat
  addMessage(message) {
    // console.log("Adding message to chat:", message);
    if (!(message instanceof ChatMessage)) {
      throw new Error("Message must be an instance of ChatMessage");
    }
    this.messages.push(message);
    this.updated_at = new Date().toISOString();
    return this;
  }

  // Remove a message by ID
  removeMessage(messageId) {
    this.messages = this.messages.filter((msg) => msg.id !== messageId);
    this.updated_at = new Date().toISOString();
    return this;
  }

  // Get message by ID
  getMessage(messageId) {
    return this.messages.find((msg) => msg.id === messageId);
  }

  // Update a message
  updateMessage(messageId, updates) {
    const messageIndex = this.messages.findIndex((msg) => msg.id === messageId);
    if (messageIndex !== -1) {
      Object.assign(this.messages[messageIndex], updates);
      this.updated_at = new Date().toISOString();
    }
    return this;
  }

  // Get the last message
  getLastMessage() {
    return this.messages.length > 0
      ? this.messages[this.messages.length - 1]
      : null;
  }

  // Get messages by sender
  getMessagesBySender(sender) {
    return this.messages.filter((msg) => msg.sender === sender);
  }

  // Get user messages
  getUserMessages() {
    return this.getMessagesBySender(ChatMessageSenderType.USER);
  }

  // Get bot messages
  getBotMessages() {
    return this.getMessagesBySender(ChatMessageSenderType.BOT);
  }

  // Get completed messages (not typing)
  getCompletedMessages() {
    return this.messages.filter((msg) => !msg.isTyping);
  }

  // Get typing messages
  getTypingMessages() {
    return this.messages.filter((msg) => msg.isTyping);
  }

  // Check if chat is empty
  isEmpty() {
    return this.messages.length === 0;
  }

  // Check if bot is currently typing
  isBotTyping() {
    const lastMessage = this.getLastMessage();
    return lastMessage && lastMessage.isFromBot() && lastMessage.isTyping;
  }

  // Clear all messages
  clear() {
    this.messages = [];
    this.updated_at = new Date().toISOString();
    return this;
  }

  // Get recent messages for context (default last 10)
  getRecentMessages(count = 10) {
    return this.messages.slice(-count);
  }

  getRecentDialogue(count = 10) {
    if (this.isEmpty()) {
      return [];
    }

    // console.log("Getting recent dialogue for context...", this.messages);

    const result = [];
    for (let i = this.messages.length - 1; i >= 0; i--) {
      const msg = this.messages[i];
      if (
        msg.sender === ChatMessageSenderType.SYSTEM &&
        msg.metadata?.messageType === ChatSystemMessageType.CLEAR_CHAT
      ) {
        break;
      }

      if (count > 0 && result.length >= count) {
        break;
      }

      if (!msg.isTyping && msg.isChat()) {
        result.unshift(msg);
      }
    }

    if ((result.length < count && this.helloMessage.length > 0) || count <= 0) {
      result.unshift(ChatMessage.createBotMessage(this.helloMessage));
    }

    // console.log("Recent dialogue for context:", result);

    return result;
  }

  buildConversationHistory = (limit = 10) => {
    if (this.isEmpty()) {
      return [];
    }
    // Get previous messages (up to 10) for context
    const recentMessages =
      this.getRecentDialogue(limit).map((msg) => msg.toAPIFormat()) || [];
    return recentMessages;
  };

  // Convert messages to API format
  toAPIFormat() {
    return this.getCompletedMessages().map((msg) => ({
      role:
        msg.role ||
        (msg.sender === ChatMessageSenderType.USER ? "user" : "assistant"),
      content: msg.content || msg.text,
      id: msg.id, // Include message ID
      type: msg.type || "text", // Include message type
      // Include optional fields if they exist
      ...(msg.asr && { asr: msg.asr }),
      ...(msg.tts && { tts: msg.tts }),
      ...(msg.translation && { translation: msg.translation }),
      ...(msg.grammar_analysis && { grammar_analysis: msg.grammar_analysis }),
      ...(msg.pronunciation_assessment && {
        pronunciation_assessment: msg.pronunciation_assessment,
      }),
    }));
  }

  // Convert to storage format
  toStorageFormat() {
    return this.messages.map((msg) => msg.toStorageFormat());
  }

  // Convert to complete storage format
  toStorage() {
    return {
      botId: this.botId,
      helloMessage: this.helloMessage,
      messages: this.messages.map((msg) => msg.toStorageFormat()),
      created_at: this.created_at,
      updated_at: this.updated_at,
    };
  }

  // Create from storage data
  static fromStorage(botId, storageData) {
    return new Chat({
      botId,
      messages: storageData || [],
    });
  }

  // Create empty chat for a bot
  static createEmpty(botId) {
    return new Chat({ botId });
  }
}
