import React from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import "./index.css";
import App from "./App";
import ChatPanelApp from "./components/ChatPanel/ChatPanelApp";
import reportWebVitals from "./reportWebVitals";

// Add these imports for UI enhancements
import { marked } from "marked";
import "highlight.js/styles/github.css";

// Configure marked options
marked.setOptions({
  breaks: true,
  gfm: true,
});

const root = ReactDOM.createRoot(document.getElementById("root"));
root.render(
  <React.StrictMode>
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<App />} />
        <Route path="/chat/:botId" element={<ChatPanelApp />} />
      </Routes>
    </BrowserRouter>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
