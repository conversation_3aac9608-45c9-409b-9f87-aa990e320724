import { buildCacheKey } from "./hash";

/**
 * Centralized cache key builders for the chat application
 * All cache keys should be generated through these functions to ensure consistency
 */

/**
 * Text-to-Speech (TTS) cache key
 * @param {string} text - The text to synthesize
 * @param {string} voice - The voice to use (optional)
 * @returns {string} Cache key for TTS
 */
export const getTTSCacheKey = (text, voice = null) => {
  const cacheData = { text };
  if (voice) {
    cacheData.voice = voice;
  }
  return buildCacheKey("tts", cacheData);
};

/**
 * Automatic Speech Recognition (ASR) cache key
 * @param {string} audioUrl - The audio URL to transcribe
 * @returns {string} Cache key for ASR
 */
export const getASRCacheKey = (audioUrl) => {
  return buildCacheKey("asr", { audioUrl });
};

/**
 * Grammar correction cache key
 * @param {string} text - The text to analyze for grammar
 * @returns {string} Cache key for grammar correction
 */
export const getGrammarCacheKey = (text) => {
  const result = buildCacheKey("grammar", { text });
  // console.log("Grammar cache key:", result, text);
  return result;
};

/**
 * Translation cache key
 * @param {string} text - The text to translate
 * @param {string} beforeText - Optional context text
 * @returns {string} Cache key for translation
 */
export const getTranslationCacheKey = (text, beforeText = null) => {
  const cacheData = { text };
  if (beforeText) {
    cacheData.beforeText = beforeText;
  }
  return buildCacheKey("translation", cacheData);
};

/**
 * Pronunciation assessment cache key
 * @param {string} audioUrl - The audio URL for pronunciation assessment
 * @param {string} referenceText - Optional reference text
 * @param {string} language - Optional language
 * @returns {string} Cache key for pronunciation assessment
 */
export const getPronunciationCacheKey = (
  audioUrl,
  referenceText = null,
  language = null
) => {
  const cacheData = { audioUrl };
  if (referenceText) {
    cacheData.referenceText = referenceText;
  }
  if (language) {
    cacheData.language = language;
  }
  return buildCacheKey("pronunciation", cacheData);
};

/**
 * Chat suggestions cache key
 * @param {Array} conversationHistory - The conversation history
 * @param {string} context - Optional context
 * @returns {string} Cache key for chat suggestions
 */
export const getChatSuggestionsCacheKey = (
  conversationHistory,
  context = null
) => {
  const cacheData = { conversationHistory };
  if (context) {
    cacheData.context = context;
  }
  return buildCacheKey("chat_suggestions", cacheData);
};

/**
 * Conversation summary cache key
 * @param {Array} messages - The conversation messages
 * @param {Object} grammarResults - Optional grammar analysis results
 * @param {Object} pronunciationResults - Optional pronunciation results
 * @returns {string} Cache key for conversation summary
 */
export const getConversationSummaryCacheKey = (
  messages,
  grammarResults = null,
  pronunciationResults = null
) => {
  const cacheData = { messages };
  if (grammarResults) {
    cacheData.grammarResults = grammarResults;
  }
  if (pronunciationResults) {
    cacheData.pronunciationResults = pronunciationResults;
  }
  return buildCacheKey("conversation_summary", cacheData);
};

/**
 * Generic cache key builder for custom cache types
 * @param {string} type - The cache type/prefix
 * @param {Object} data - The data to hash
 * @returns {string} Cache key
 */
export const getCustomCacheKey = (type, data) => {
  return buildCacheKey(type, data);
};

/**
 * Cache key prefixes used throughout the application
 */
export const CACHE_PREFIXES = {
  TTS: "tts",
  ASR: "asr",
  GRAMMAR: "grammar",
  TRANSLATION: "translation",
  PRONUNCIATION: "pronunciation",
  CHAT_SUGGESTIONS: "chat_suggestions",
  CONVERSATION_SUMMARY: "conversation_summary",
};

/**
 * Clear all cache entries for a specific prefix
 * @param {string} prefix - The cache prefix to clear
 */
export const clearCacheByPrefix = (prefix) => {
  const keys = Object.keys(localStorage);
  keys.forEach((key) => {
    if (key.startsWith(`${prefix}-`)) {
      localStorage.removeItem(key);
    }
  });
};

/**
 * Clear all application cache
 */
export const clearAllCache = () => {
  Object.values(CACHE_PREFIXES).forEach((prefix) => {
    clearCacheByPrefix(prefix);
  });
};

/**
 * Get cache statistics
 * @returns {Object} Cache statistics by type
 */
export const getCacheStats = () => {
  const stats = {};
  const keys = Object.keys(localStorage);

  Object.values(CACHE_PREFIXES).forEach((prefix) => {
    stats[prefix] = {
      count: 0,
      totalSize: 0,
    };
  });

  keys.forEach((key) => {
    Object.values(CACHE_PREFIXES).forEach((prefix) => {
      if (key.startsWith(`${prefix}-`)) {
        stats[prefix].count++;
        stats[prefix].totalSize += localStorage.getItem(key).length;
      }
    });
  });

  return stats;
};

export const getMissionCacheKey = (botId) => {
  return buildCacheKey("mission", { botId });
};
