// Utility functions for managing user authentication
import { WS_API_BASE_URL } from "../config";

/**
 * Get user ID from X-5E-USER parameter or localStorage
 * @returns {string|null} User ID if found
 */
export const getUserId = () => {
  // First check URL parameters for X-5E-USER
  const urlParams = new URLSearchParams(window.location.search);
  const userAuthParam = urlParams.get("X-5E-USER");

  if (userAuthParam) {
    try {
      const userAuth = JSON.parse(decodeURIComponent(userAuthParam));
      if (userAuth.id) {
        // Store in localStorage for future use
        localStorage.setItem("userAuth", JSON.stringify(userAuth));
        console.log("User authenticated via X-5E-USER parameter:", userAuth.id);
        return userAuth.id;
      }
    } catch (e) {
      console.warn("Invalid X-5E-USER parameter:", e);
    }
  }

  // Check localStorage for stored user auth
  const storedUserAuth = localStorage.getItem("userAuth");
  if (storedUserAuth) {
    try {
      const userAuth = JSON.parse(storedUserAuth);
      return userAuth.id || null;
    } catch (e) {
      console.warn("Invalid stored user auth:", e);
    }
  }

  return null;
};

/**
 * Get user auth object for API requests
 * @returns {object|null} User auth object if found
 */
export const getUserAuth = () => {
  // First check URL parameters for X-5E-USER
  const urlParams = new URLSearchParams(window.location.search);
  const userAuthParam = urlParams.get("X-5E-USER");

  if (userAuthParam) {
    try {
      const userAuth = JSON.parse(decodeURIComponent(userAuthParam));
      if (userAuth.id) {
        // Store in localStorage for future use
        localStorage.setItem("userAuth", JSON.stringify(userAuth));
        return userAuth;
      }
    } catch (e) {
      console.warn("Invalid X-5E-USER parameter:", e);
    }
  }

  // Check localStorage for stored user auth
  const storedUserAuth = localStorage.getItem("userAuth");
  if (storedUserAuth) {
    try {
      const userAuth = JSON.parse(storedUserAuth);
      return userAuth.id ? userAuth : null;
    } catch (e) {
      console.warn("Invalid stored user auth:", e);
    }
  }

  return null;
};

/**
 * Get API key, prioritizing X-5E-USER from URL if present, then stored API key
 * @returns {string} API key to use
 */
export const getApiKey = () => {
  // First check URL parameters for X-5E-USER to use as API key
  const urlParams = new URLSearchParams(window.location.search);
  const userAuthParam = urlParams.get("X-5E-USER");

  if (userAuthParam) {
    try {
      const userAuth = JSON.parse(decodeURIComponent(userAuthParam));
      if (userAuth.id) {
        // Store user auth but also save it as API key for backward compatibility
        localStorage.setItem("userAuth", JSON.stringify(userAuth));
        localStorage.setItem("apiKey", userAuthParam); // Store the original X-5E-USER as API key
        return userAuthParam;
      }
    } catch (e) {
      console.warn("Invalid X-5E-USER parameter:", e);
    }
  }

  // Check if we have stored user auth to use as API key
  const storedUserAuth = localStorage.getItem("userAuth");
  if (storedUserAuth) {
    try {
      const userAuth = JSON.parse(storedUserAuth);
      if (userAuth.id) {
        return storedUserAuth;
      }
    } catch (e) {
      console.warn("Invalid stored user auth:", e);
    }
  }

  // Fall back to regular API key
  return localStorage.getItem("apiKey") || "";
};

/**
 * Get headers for API requests with proper authentication
 * @param {string} contentType - Content type for the request
 * @returns {object} Headers object
 */
export const getApiHeaders = (contentType = "application/json") => {
  const headers = {
    "Content-Type": contentType,
  };

  const apiKey = getApiKey();

  const userAuth = getUserAuth();
  if (userAuth) {
    headers["X-5E-USER"] = JSON.stringify(userAuth);
    // console.log("Adding X-5E-USER to headers:", userAuth);
  } else if (apiKey) {
    headers["X-API-KEY"] = apiKey;
  }

  // console.log("API headers created:", headers);
  return headers;
};

/**
 * Get WebSocket URL with proper authentication parameters
 * @param {string} endpoint - WebSocket endpoint
 * @returns {string} WebSocket URL with auth parameters
 */
export const getWebSocketUrl = (endpoint, baseUrl, searchParams = {}) => {
  console.log(
    "Generating WebSocket URL for endpoint:",
    endpoint,
    "baseUrl:",
    baseUrl,
    "searchParams:",
    searchParams
  );
  if (!baseUrl) {
    baseUrl = WS_API_BASE_URL;
  }
  const url = new URL(endpoint, baseUrl);

  const apiKey = getApiKey();

  const userAuth = getUserAuth();
  if (userAuth) {
    url.searchParams.set("X-5E-USER", JSON.stringify(userAuth));
  } else if (apiKey) {
    url.searchParams.set("X-API-KEY", apiKey);
  }
  for (const [key, value] of Object.entries(searchParams)) {
    url.searchParams.set(key, value);
  }
  console.log("WebSocket URL created:", url.toString());
  return url.toString();
};

/**
 * Get headers for file uploads (without Content-Type to let browser set multipart boundary)
 * @returns {object} Headers object for file uploads
 */
export const getFileUploadHeaders = () => {
  const headers = {};

  const apiKey = getApiKey();

  const userAuth = getUserAuth();
  if (userAuth) {
    headers["X-5E-USER"] = JSON.stringify(userAuth);
    console.log("Adding X-5E-USER to file upload headers:", userAuth);
  } else if (apiKey) {
    headers["X-API-KEY"] = apiKey;
  }

  console.log("File upload headers created:", headers);
  return headers;
};
