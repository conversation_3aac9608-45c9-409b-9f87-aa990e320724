/**
 * Test utility for the chat synchronization feature
 */

import {
  syncChatHistory,
  convertChatToSyncFormat,
} from "../services/apiServices";
import { Chat, ChatMessage } from "../types";
import { ChatMessageSenderType } from "../types/ChatMessageType";

/**
 * Test function to verify chat sync functionality
 * This can be called from the browser console during development
 */
export const testChatSyncFeature = async (apiKey) => {
  try {
    console.log("Testing chat sync feature...");

    // Create a sample chat with test messages
    const testChat = new Chat({
      botId: "test-bot-id",
      messages: [
        new ChatMessage({
          id: "test-msg-1",
          role: "user",
          content: "Hello, this is a test message",
          sender: ChatMessageSenderType.USER,
          type: "text",
          timestamp: new Date().toISOString(),
        }),
        new ChatMessage({
          id: "test-msg-2",
          role: "assistant",
          content: "Hello! This is a test response.",
          sender: ChatMessageSenderType.BOT,
          type: "text",
          timestamp: new Date().toISOString(),
        }),
      ],
    });

    // Convert to sync format
    const syncData = convertChatToSyncFormat(testChat);
    console.log("Converted sync data:", syncData);

    // Test the sync API call
    const result = await syncChatHistory(syncData);
    console.log("Sync result:", result);

    return {
      success: true,
      syncData,
      result,
    };
  } catch (error) {
    console.error("Chat sync test failed:", error);
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Make the test function available in the global scope for development
 */
if (typeof window !== "undefined") {
  window.testChatSyncFeature = testChatSyncFeature;
}
