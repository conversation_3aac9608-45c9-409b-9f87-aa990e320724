// Simple hash function for cache keys
export const md5 = (string) => {
  return String(string)
    .split("")
    .reduce((hash, char) => {
      hash = (hash << 5) - hash + char.charCodeAt(0);
      return hash & hash; // Convert to 32bit integer
    }, 0)
    .toString(16);
};

export const buildCacheKey = (prefix, values) => {
  const hash = md5(JSON.stringify(values));
  return `${prefix}-${hash}`;
};
