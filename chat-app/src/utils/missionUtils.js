// Mission Types Test and Examples
import {
  Mission,
  MissionStep,
  MissionStatus,
  MissionStepStatus,
  createMission,
  createMissionStep,
} from "../types/Mission";

// Example usage and tests for Mission types
export const testMissionTypes = () => {
  console.log("Testing Mission Types...");

  // Create a sample mission
  const mission = createMission({
    title: "Learn JavaScript",
    description: "Master the fundamentals of JavaScript programming",
    objectives: [
      "Understand variables and data types",
      "Learn functions and scope",
      "Master async programming",
    ],
    bot_id: "bot-123",
  });

  // Add some steps
  mission.addStep({
    action: "Study JavaScript variables",
    status: MissionStepStatus.COMPLETED,
  });

  mission.addStep({
    action: "Practice writing functions",
    status: MissionStepStatus.IN_PROGRESS,
  });

  mission.addStep({
    action: "Learn about promises and async/await",
    status: MissionStepStatus.PENDING,
  });

  console.log("Created mission:", mission);
  console.log("Progress:", mission.getProgressPercentage() + "%");
  console.log("Is completed:", mission.isCompleted());
  console.log("Is active:", mission.isActive());

  // Test status updates
  const firstStep = mission.steps[0];
  console.log("First step icon:", firstStep.getStatusIcon());
  console.log("First step color:", firstStep.getStatusColor());

  // Test JSON serialization
  const jsonData = mission.toJSON();
  console.log("Mission as JSON:", jsonData);

  // Test loading from JSON
  const loadedMission = new Mission(jsonData);
  console.log("Loaded mission:", loadedMission);

  // Test localStorage operations
  mission.saveToLocalStorage("test-bot");
  const savedMission = Mission.loadFromLocalStorage("test-bot");
  console.log("Mission from localStorage:", savedMission);

  Mission.removeFromLocalStorage("test-bot");

  return mission;
};

// Example API response transformation
export const transformApiResponse = (apiResponse) => {
  // Example API response format
  const exampleApiResponse = {
    mission: {
      title: "Complete onboarding",
      description: "Help user complete the onboarding process",
      objectives: ["Register account", "Set up profile", "Take tutorial"],
      steps: [
        { action: "Create account", status: "completed" },
        { action: "Upload profile picture", status: "in_progress" },
        { action: "Complete tutorial", status: "pending" },
      ],
    },
  };

  // Transform to Mission instance
  if (apiResponse.mission) {
    return createMission(apiResponse.mission);
  }

  return null;
};

// Example conversation analysis
export const analyzeConversationProgress = (mission, messages) => {
  if (!mission || !messages.length) {
    return { progress: 0, suggestions: [] };
  }

  const conversationText = messages
    .filter((msg) => !msg.isTyping)
    .map((msg) => msg.text.toLowerCase())
    .join(" ");

  const suggestions = [];

  // Simple keyword analysis for each objective
  mission.objectives.forEach((objective, index) => {
    const keywords = objective.toLowerCase().split(" ");
    const mentioned = keywords.some((keyword) =>
      conversationText.includes(keyword)
    );

    if (mentioned) {
      suggestions.push(`Objective "${objective}" has been discussed`);
    }
  });

  return {
    progress: mission.getProgressPercentage(),
    suggestions,
    status: mission.status,
  };
};

export default {
  testMissionTypes,
  transformApiResponse,
  analyzeConversationProgress,
};
