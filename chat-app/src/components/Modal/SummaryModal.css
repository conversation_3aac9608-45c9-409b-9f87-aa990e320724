.summary-modal {
  max-width: 700px;
  max-height: 90vh;
  overflow-y: auto;
}

.summary-content {
  padding: 0;
}

.summary-section {
  margin-bottom: 24px;
  padding: 20px;
  border-radius: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

.summary-section h3 {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.level-assessment {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.level-assessment h3 {
  color: white;
}

.level-display {
  display: flex;
  align-items: center;
  gap: 24px;
}

.level-circle {
  text-align: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.level-score {
  font-size: 2rem;
  font-weight: bold;
  line-height: 1;
}

.level-label {
  font-size: 0.9rem;
  margin-top: 4px;
  opacity: 0.9;
}

.score-description {
  flex: 1;
}

.score-description strong {
  font-size: 1.2rem;
  display: block;
  margin-bottom: 12px;
}

.score-bar {
  width: 100%;
  height: 12px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  overflow: hidden;
}

.score-fill {
  height: 100%;
  border-radius: 6px;
  transition: width 0.8s ease;
}

.overview-text {
  line-height: 1.6;
  color: #4a5568;
  margin: 0;
}

.quality-badge {
  display: inline-block;
  background: #38b2ac;
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  margin-top: 12px;
}

.topics-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.topic-tag {
  background: #e2e8f0;
  color: #4a5568;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Analysis sections */
.grammar-analysis {
  border-left: 4px solid #10b981;
}

.pronunciation-analysis {
  border-left: 4px solid #3b82f6;
}

.analysis-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 16px;
}

.stat-item {
  text-align: center;
  flex: 1;
  padding: 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
}

.stat-number {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #2d3748;
  line-height: 1;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: #718096;
  margin-top: 4px;
}

.insights-list {
  margin-top: 12px;
}

.insight-item {
  padding: 8px 12px;
  margin-bottom: 6px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  font-size: 0.9rem;
  color: #2d3748;
}

.strengths {
  border-left: 4px solid #10b981;
}

.weaknesses {
  border-left: 4px solid #f59e0b;
}

.suggestions {
  border-left: 4px solid #3b82f6;
}

.next-steps {
  border-left: 4px solid #8b5cf6;
}

.summary-section ul {
  margin: 0;
  padding-left: 20px;
}

.summary-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #4a5568;
}

.encouragement {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border: none;
  text-align: center;
}

.encouragement-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #744210;
  margin: 0;
  font-weight: 500;
}

.loading-spinner {
  text-align: center;
  padding: 40px 20px;
}

.loading-detail {
  font-size: 0.9rem;
  color: #718096;
  margin-top: 8px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-left: 4px solid #38b2ac;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: transparent;
  border: none;
  color: #4a5568;
  font-size: 1.5rem;
  cursor: pointer;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .summary-modal {
    max-width: 95%;
    margin: 20px auto;
  }
  
  .level-display {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .level-circle {
    width: 100px;
    height: 100px;
  }
  
  .level-score {
    font-size: 1.6rem;
  }
  
  .analysis-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .stat-item {
    padding: 8px;
  }
  
  .stat-number {
    font-size: 1.4rem;
  }
  
  .summary-section {
    padding: 16px;
    margin-bottom: 16px;
  }
}