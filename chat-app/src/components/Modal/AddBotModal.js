import React, { useState, useEffect, useRef } from "react";
import { improvePersonaWithAI } from "../../services/apiServices";
import { API_BASE_URL } from "../../config";
import { getApiHeaders, getFileUploadHeaders } from "../../utils/userAuth";
import "./Modal.css";

const AddBotModal = ({ onClose, onSave, editBot, apiKey }) => {
  const [name, setName] = useState("");
  const [persona, setPersona] = useState("");
  const [botType, setBotType] = useState("normal"); // Add bot type state
  const [imageUrl, setImageUrl] = useState("");
  const [voice, setVoice] = useState("en_female_amanda_mars_bigtts");
  const [voiceSpeed, setVoiceSpeed] = useState(1.0); // Add voice speed state
  const [helloMessage, setHelloMessage] = useState(""); // Add hello message state
  const [title, setTitle] = useState(""); // Add title state
  const [missions, setMissions] = useState(""); // Add missions state
  const [isImproving, setIsImproving] = useState(false);
  const [availableVoices, setAvailableVoices] = useState([]);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const fileInputRef = useRef(null);

  useEffect(() => {
    if (editBot) {
      setName(editBot.name || "");
      setPersona(editBot.persona || "");
      setImageUrl(editBot.image_url || "");
      setVoice(editBot.voice || "en_female_amanda_mars_bigtts");
      setVoiceSpeed(editBot.voice_speed || 1.0);
      setHelloMessage(editBot.hello_message || "");

      // Set bot type and missions together to avoid race condition
      const newBotType = editBot.bot_type || "normal";
      const missionsValue = editBot.extra_data?.missions || "";
      const titleValue = editBot.extra_data?.title || "";

      setBotType(newBotType);
      setMissions(missionsValue);
      setTitle(titleValue);
    }
  }, [editBot]);

  // Clear missions and title when bot type changes to normal (but not during initial edit load)
  useEffect(() => {
    if (botType === "normal" && !editBot) {
      setMissions("");
      setTitle("");
    }
  }, [botType]);

  useEffect(() => {
    // Fetch available voices
    const fetchVoices = async () => {
      try {
        const response = await fetch(API_BASE_URL + "/api/voices", {
          headers: getApiHeaders(),
        });
        if (response.ok) {
          const data = await response.json();
          setAvailableVoices(data.voices);
        }
      } catch (error) {
        console.error("Failed to fetch voices:", error);
        // Fallback to default voices
        setAvailableVoices([
          {
            id: "en_female_amanda_mars_bigtts",
            name: "Amanda (Female, English)",
          },
          {
            id: "en_male_jackson_mars_bigtts",
            name: "Jackson (Male, English)",
          },
        ]);
      }
    };

    if (apiKey) {
      fetchVoices();
    }
  }, [apiKey]);

  const handleSave = () => {
    // Prepare extra_data based on bot type and include title
    let extraData = {};

    // Always include title if provided
    if (title.trim()) {
      extraData.title = title.trim();
    }

    // Include missions for mission-type bots
    if (botType === "mission" && missions.trim()) {
      extraData.missions = missions.trim();
    }

    // Only pass extraData if it has content
    const finalExtraData = Object.keys(extraData).length > 0 ? extraData : null;

    onSave(
      name,
      persona,
      botType,
      imageUrl,
      voice,
      voiceSpeed,
      helloMessage,
      finalExtraData
    ); // Include extra data in onSave
  };

  const handleImprovePersona = async () => {
    if (!name) {
      alert("Please enter a bot name first");
      return;
    }

    setIsImproving(true);

    try {
      const improvedPersona = await improvePersonaWithAI(name, persona, apiKey);
      setPersona(improvedPersona);
    } catch (error) {
      console.error("Error improving persona:", error);
      alert(`Failed to improve persona: ${error.message}`);
    } finally {
      setIsImproving(false);
    }
  };

  const handleImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];
    if (!allowedTypes.includes(file.type)) {
      alert("Please select a valid image file (JPEG, PNG, GIF, or WebP)");
      return;
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      alert("Image file size must be less than 5MB");
      return;
    }

    setIsUploadingImage(true);

    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch(`${API_BASE_URL}/api/upload`, {
        method: "POST",
        headers: getFileUploadHeaders(),
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }

      const data = await response.json();
      setImageUrl(data.url);
    } catch (error) {
      console.error("Error uploading image:", error);
      alert(`Failed to upload image: ${error.message}`);
    } finally {
      setIsUploadingImage(false);
      // Reset the file input
      event.target.value = "";
    }
  };

  const handleImageUrlChange = (e) => {
    setImageUrl(e.target.value);
  };

  const handleRemoveImage = () => {
    setImageUrl("");
  };

  const triggerFileUpload = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="modal">
      <div className="modal-content">
        <h2>{editBot ? "Edit Bot" : "Add New Bot"}</h2>

        <div className="form-group">
          <label htmlFor="botName">Bot Name</label>
          <input
            type="text"
            id="botName"
            placeholder="Enter bot name"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
        </div>

        <div className="form-group">
          <label htmlFor="botTitle">Bot Title</label>
          <input
            type="text"
            id="botTitle"
            placeholder="Enter bot title (optional)"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
          <div className="bot-type-description">
            <small>A descriptive title or subtitle for the bot</small>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="botType">Bot Type</label>
          <select
            id="botType"
            value={botType}
            onChange={(e) => setBotType(e.target.value)}
          >
            <option value="normal">Normal - Focus on topics</option>
            <option value="mission">Mission - Complete tasks</option>
          </select>
          <div className="bot-type-description">
            {botType === "normal" ? (
              <small>
                Normal bots focus on specific topics and general conversation
              </small>
            ) : (
              <small>
                Mission bots focus on completing specific tasks or missions
              </small>
            )}
          </div>
        </div>

        {/* Add missions field for mission-type bots */}
        {botType === "mission" && (
          <div className="form-group">
            <label htmlFor="botMissions">Missions</label>
            <textarea
              id="botMissions"
              placeholder="Describe the specific tasks or missions this bot should complete"
              value={missions}
              onChange={(e) => setMissions(e.target.value)}
              rows="4"
            />
            <div className="bot-type-description">
              <small>
                Define the specific goals, tasks, or missions this bot should
                help users accomplish
              </small>
            </div>
          </div>
        )}

        <div className="form-group">
          <label htmlFor="botImage">Bot Image</label>

          <div className="image-upload-section">
            <div className="image-upload-controls">
              <button
                type="button"
                className="upload-button"
                onClick={triggerFileUpload}
                disabled={isUploadingImage || !apiKey}
              >
                {isUploadingImage ? "Uploading..." : "Upload Image"}
              </button>

              {imageUrl && (
                <button
                  type="button"
                  className="remove-image-button"
                  onClick={handleRemoveImage}
                  title="Remove image"
                >
                  ×
                </button>
              )}
            </div>

            <input
              type="file"
              ref={fileInputRef}
              style={{ display: "none" }}
              accept="image/jpeg,image/jpg,image/png,image/gif,image/webp"
              onChange={handleImageUpload}
            />
          </div>

          {!apiKey && (
            <p className="upload-warning">
              Please set your API key to enable image upload
            </p>
          )}

          {imageUrl && (
            <div className="image-preview">
              <img
                src={imageUrl}
                alt="Bot preview"
                onError={(e) => (e.target.style.display = "none")}
              />
            </div>
          )}
        </div>

        <div className="form-group">
          <label htmlFor="botVoice">Voice</label>
          <select
            id="botVoice"
            value={voice}
            onChange={(e) => setVoice(e.target.value)}
          >
            {availableVoices.map((voiceOption) => (
              <option key={voiceOption.id} value={voiceOption.id}>
                {voiceOption.name}
              </option>
            ))}
          </select>
        </div>

        {/* Add voice speed control */}
        <div className="form-group">
          <label htmlFor="botVoiceSpeed">Voice Speed</label>
          <div className="voice-speed-container">
            <input
              type="range"
              id="botVoiceSpeed"
              min="0.5"
              max="2.0"
              step="0.1"
              value={voiceSpeed}
              onChange={(e) => setVoiceSpeed(parseFloat(e.target.value))}
              className="voice-speed-slider"
            />
            <span className="voice-speed-value">{voiceSpeed}x</span>
          </div>
          <div className="voice-speed-labels">
            <span>Slow</span>
            <span>Normal</span>
            <span>Fast</span>
          </div>
        </div>

        <div className="form-group">
          <label htmlFor="botHelloMessage">Hello Message</label>
          <textarea
            id="botHelloMessage"
            className="hello-message-textarea"
            placeholder="Enter a greeting message that the bot will send when starting a new conversation (optional)"
            value={helloMessage}
            onChange={(e) => setHelloMessage(e.target.value)}
            rows={2}
          />
        </div>

        <div className="form-group">
          <label htmlFor="botPersona">Bot Persona</label>
          <textarea
            id="botPersona"
            className="persona-textarea"
            placeholder="Describe the bot's personality and capabilities"
            value={persona}
            onChange={(e) => setPersona(e.target.value)}
            rows={10}
          />
        </div>

        <div className="form-group">
          <button
            id="improvePersonaBtn"
            className="improve-button"
            onClick={handleImprovePersona}
            disabled={isImproving || !name}
          >
            <span className="toolbar-icon animated">✨</span>{" "}
            {isImproving ? "Improving..." : "Improve Persona with AI"}
          </button>
        </div>

        {editBot && (
          <a
            href={`/chat/${editBot.id}?apikey=${editBot.apikey || apiKey}`}
            target="_blank"
            rel="noopener noreferrer"
            className="chat-bot-link"
          >
            Chat with Bot
          </a>
        )}

        <div className="modal-buttons">
          <button className="cancel" onClick={onClose}>
            Cancel
          </button>
          <button className="save" onClick={handleSave} disabled={!name}>
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddBotModal;
