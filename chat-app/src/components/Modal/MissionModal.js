import React, { useState, useEffect } from "react";
import { createMission, checkMissionStatus } from "../../services/apiServices";
import { Mission, createMission as createMissionInstance } from "../../types";
import { getMissionCacheKey } from "../../utils/cacheKeys";
import "./MissionModal.css";

const MissionModal = ({
  onClose,
  currentBot,
  messages,
  apiKey,
  missionData,
}) => {
  const [mission, setMission] = useState(null);
  const [missionDetails, setMissionDetails] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(false);

  // Load mission from localStorage on mount
  // useEffect(() => {
  //   if (currentBot?.id) {
  //     const loadedMission = Mission.fromLocalStorage(
  //       localStorage.getItem(getMissionCacheKey(currentBot.id))
  //     );
  //     if (loadedMission) {
  //       setMission(loadedMission);
  //     }
  //   }
  // }, [currentBot?.id]);

  useEffect(() => {
    if (missionData) {
      setMission(Mission.fromLocalStorage(missionData));
    }
  }, [missionData]);

  // // Save mission to localStorage whenever it changes
  // useEffect(() => {
  //   if (mission && currentBot?.id) {
  //     mission.saveToLocalStorage(currentBot.id);
  //   }
  // }, [mission, currentBot?.id]);

  const handleCreateMission = async () => {
    if (!missionDetails.trim()) {
      alert("Please enter mission details");
      return;
    }

    setIsLoading(true);
    try {
      const data = await createMission(missionDetails, apiKey);
      if (data.mission) {
        // Create Mission instance from API response
        const newMission = createMissionInstance({
          ...data.mission,
          bot_id: currentBot?.id,
        });
        setMission(newMission);
        setMissionDetails("");
        setIsCreatingNew(false);
      }
    } catch (error) {
      console.error("Error creating mission:", error);
      alert(`Failed to create mission: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCheckMissionStatus = async () => {
    if (!mission || !messages.length) {
      alert("No mission or conversation history available");
      return;
    }

    setCheckingStatus(true);
    try {
      // Convert messages to conversation history string
      const conversationHistory = messages
        .filter((msg) => !msg.isTyping)
        .map((msg) => `${msg.sender}: ${msg.text}`)
        .join("\n");

      const data = await checkMissionStatus(
        mission.toJSON(), // Convert Mission instance to plain object for API
        conversationHistory,
        apiKey
      );
      if (data.updated_mission) {
        // Update existing mission instance with new data
        mission.updateFromApiResponse(data.updated_mission);
        setMission(new Mission(mission.toJSON())); // Force re-render
      }
    } catch (error) {
      console.error("Error checking mission status:", error);
      alert(`Failed to check mission status: ${error.message}`);
    } finally {
      setCheckingStatus(false);
    }
  };

  const handleDeleteMission = () => {
    if (window.confirm("Are you sure you want to delete this mission?")) {
      setMission(null);
      if (currentBot?.id) {
        Mission.removeFromLocalStorage(currentBot.id);
      }
    }
  };

  const handleResetMission = () => {
    if (
      window.confirm(
        "Are you sure you want to reset the mission status? This will reset all progress to the initial state."
      )
    ) {
      // Reset mission to initial state
      mission.reset();
      setMission(new Mission(mission.toJSON())); // Force re-render
    }
  };

  const getStepStatusIcon = (step) => {
    return step.getStatusIcon
      ? step.getStatusIcon()
      : step.getStatusIcon || "⏳";
  };

  const getStepStatusColor = (step) => {
    return step.getStatusColor
      ? step.getStatusColor()
      : step.getStatusColor || "#6c757d";
  };

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div
        className="modal-content mission-modal"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-header">
          <h2>Mission Manager</h2>
          <button className="modal-close" onClick={onClose}>
            ×
          </button>
        </div>

        <div className="modal-body">
          {!mission && !isCreatingNew ? (
            <div className="no-mission">
              <p>No mission created for {currentBot?.name || "this bot"}.</p>
              <button
                className="btn btn-primary"
                onClick={() => setIsCreatingNew(true)}
              >
                Create New Mission
              </button>
            </div>
          ) : isCreatingNew ? (
            <div className="create-mission">
              <h3>Create New Mission</h3>
              <textarea
                value={missionDetails}
                onChange={(e) => setMissionDetails(e.target.value)}
                placeholder="Describe the mission objectives, goals, and what you want to achieve through this conversation..."
                rows={6}
                className="mission-textarea"
              />
              <div className="mission-actions">
                <button
                  className="btn btn-secondary"
                  onClick={() => {
                    setIsCreatingNew(false);
                    setMissionDetails("");
                  }}
                >
                  Cancel
                </button>
                <button
                  className="btn btn-primary"
                  onClick={handleCreateMission}
                  disabled={isLoading || !missionDetails.trim()}
                >
                  {isLoading ? "Creating..." : "Create Mission"}
                </button>
              </div>
            </div>
          ) : (
            <div className="mission-display">
              <div className="mission-header">
                <h3>{mission.title}</h3>
              </div>

              <div className="mission-description">
                {mission.steps && mission.steps.length > 0 && (
                  <div className="mission-progress">
                    <div className="progress-header">
                      <span>Progress: {mission.getProgressPercentage()}%</span>
                      <span className="mission-status">
                        {mission.getStatus().toUpperCase()}
                      </span>
                    </div>
                    <div className="progress-bar">
                      <div
                        className="progress-fill"
                        style={{
                          width: `${mission.getProgressPercentage()}%`,
                          backgroundColor: mission.isCompleted()
                            ? "#28a745"
                            : mission.hasFailed()
                            ? "#dc3545"
                            : "#007bff",
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              {mission.objectives && mission.objectives.length > 0 && (
                <div className="mission-objectives">
                  <h4>Objectives:</h4>
                  <ul>
                    {mission.objectives.map((objective, index) => (
                      <li key={index}>{objective}</li>
                    ))}
                  </ul>
                </div>
              )}

              {mission.steps && mission.steps.length > 0 && (
                <div className="mission-steps">
                  <h4>Steps:</h4>
                  <div className="steps-list">
                    {mission.steps.map((step, index) => (
                      <div
                        key={step.id || index}
                        className="step-item"
                        style={{
                          borderLeftColor: getStepStatusColor(step),
                        }}
                      >
                        <div className="step-header">
                          <span className="step-status">
                            {getStepStatusIcon(step)}
                          </span>
                          <span className="step-action">{step.action}</span>
                        </div>
                        <div className="step-status-text">
                          Status:{" "}
                          <span style={{ color: getStepStatusColor(step) }}>
                            {step.status.replace("_", " ").toUpperCase()}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="mission-footer">
                <button
                  className="btn btn-secondary"
                  onClick={() => setIsCreatingNew(true)}
                >
                  Create New Mission
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MissionModal;
