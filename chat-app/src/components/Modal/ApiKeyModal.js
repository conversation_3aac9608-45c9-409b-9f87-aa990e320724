import React, { useState } from "react";
import "./Modal.css";

const ApiKeyModal = ({ onClose, onSave, apiKey }) => {
  const [key, setKey] = useState(apiKey || "");

  const handleSave = () => {
    onSave(key);
  };

  return (
    <div className="modal">
      <div className="modal-content">
        <h2>API Key Settings</h2>
        <p className="api-key-info">
          Enter your API key to access all features including chatbot saving and
          synchronization.
        </p>
        <div className="form-group">
          <label htmlFor="apiKey">API Key</label>
          <input
            type="password"
            id="apiKey"
            placeholder="Enter your API key"
            value={key}
            onChange={(e) => setKey(e.target.value)}
          />
        </div>
        <div className="modal-buttons">
          <button className="cancel" onClick={onClose}>
            Cancel
          </button>
          <button className="save" onClick={handleSave}>
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApiKeyModal;
