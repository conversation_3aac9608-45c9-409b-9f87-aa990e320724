import React from "react";
import ReactMarkdown from "react-markdown";
import "./Modal.css";
import "./SummaryModal.css";

const SummaryModal = ({ onClose, summary, isLoading }) => {
  if (isLoading) {
    return (
      <div className="modal-overlay" onClick={onClose}>
        <div
          className="modal-content summary-modal"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="modal-header">
            <h2>📊 Analyzing Your Conversation...</h2>
            <button className="close-btn" onClick={onClose}>
              ×
            </button>
          </div>
          <div className="modal-body">
            <div className="loading-spinner">
              <div className="spinner"></div>
              <p>Generating your personalized English learning summary...</p>
              <p className="loading-detail">
                Including grammar and pronunciation analysis from your
                conversation history.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!summary) {
    return (
      <div className="modal-overlay" onClick={onClose}>
        <div
          className="modal-content summary-modal"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="modal-header">
            <h2>📊 Conversation Summary</h2>
            <button className="close-btn" onClick={onClose}>
              ×
            </button>
          </div>
          <div className="modal-body">
            <p>No summary available. Please try again.</p>
            <button className="modal-button" onClick={onClose}>
              Close
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Handle case when no user messages found
  if (
    summary.english_level_score === 0 &&
    summary.english_level === "Unable to assess"
  ) {
    return (
      <div className="modal-overlay" onClick={onClose}>
        <div
          className="modal-content summary-modal"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="modal-header">
            <h2>📊 Conversation Summary</h2>
            <button className="close-btn" onClick={onClose}>
              ×
            </button>
          </div>
          <div className="modal-body summary-content">
            <div className="summary-section">
              <h3>💬 No Analysis Available</h3>
              <p className="overview-text">{summary.overall_summary}</p>
              <p className="overview-text">{summary.feedback}</p>
            </div>
            <div className="summary-section encouragement">
              <h3>🌟 Start Your Learning Journey!</h3>
              <p className="encouragement-text">
                Send some messages to begin your English learning analysis. The
                more you chat, the better insights we can provide!
              </p>
            </div>
          </div>
          <div className="modal-buttons">
            <button className="modal-button primary" onClick={onClose}>
              Start Chatting
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div
        className="modal-content summary-modal"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="modal-header">
          <h2>📊 Conversation Summary</h2>
          <button className="close-btn" onClick={onClose}>
            ×
          </button>
        </div>
        <div className="modal-body summary-content">
          {/* English Level Assessment */}
          <div className="summary-section level-assessment">
            <h3>🎯 English Level Assessment</h3>
            <div className="level-display">
              <div className="level-circle">
                <div
                  className="level-score"
                  style={{ color: getScoreColor(summary.english_level_score) }}
                >
                  {summary.english_level_score}
                </div>
                <div className="level-label">{summary.english_level}</div>
              </div>
              <div className="score-description">
                <strong>{getScoreLabel(summary.english_level_score)}</strong>
                <div className="score-bar">
                  <div
                    className="score-fill"
                    style={{
                      width: `${summary.english_level_score}%`,
                      backgroundColor: getScoreColor(
                        summary.english_level_score
                      ),
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </div>

          {/* Conversation Overview */}
          {false && (
            <div className="summary-section">
              <h3>💬 Conversation Overview</h3>
              <div className="overview-text">
                <ReactMarkdown>{summary.overall_summary}</ReactMarkdown>
              </div>
              {summary.conversation_quality && (
                <div className="quality-badge">
                  Quality: <strong>{summary.conversation_quality}</strong>
                </div>
              )}
            </div>
          )}

          {/* Grammar Analysis */}
          {false && summary.grammar && summary.grammar.length > 0 && (
            <div className="summary-section grammar-analysis">
              <h3>📝 Grammar Analysis</h3>
              <div className="insights-list">
                {summary.grammar.map((item, index) => (
                  <div key={index} className="insight-item">
                    ✓{" "}
                    <ReactMarkdown components={{ p: "span" }}>
                      {item}
                    </ReactMarkdown>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Pronunciation Analysis */}
          {false &&
            summary.pronunciation &&
            summary.pronunciation.length > 0 && (
              <div className="summary-section pronunciation-analysis">
                <h3>🎤 Pronunciation Analysis</h3>
                <div className="insights-list">
                  {summary.pronunciation.map((item, index) => (
                    <div key={index} className="insight-item">
                      🎯{" "}
                      <ReactMarkdown components={{ p: "span" }}>
                        {item}
                      </ReactMarkdown>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {/* Topic Relevance */}
          {false &&
            summary.topic_relevance &&
            summary.topic_relevance.length > 0 && (
              <div className="summary-section">
                <h3>🔑 Topic Relevance</h3>
                <div className="insights-list">
                  {summary.topic_relevance.map((item, index) => (
                    <div key={index} className="insight-item">
                      📌{" "}
                      <ReactMarkdown components={{ p: "span" }}>
                        {item}
                      </ReactMarkdown>
                    </div>
                  ))}
                </div>
              </div>
            )}

          {/* Feedback */}
          {summary.feedback && (
            <div className="summary-section">
              <h3>💭 Feedback</h3>
              <div className="overview-text">
                {summary.feedback.map((feedback, index) => (
                  <div key={index} className="feedback-item">
                    <ReactMarkdown components={{ p: "span" }}>
                      {feedback}
                    </ReactMarkdown>
                    <br />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="modal-buttons">
          <button className="modal-button primary" onClick={onClose}>
            Continue Learning
          </button>
        </div>
      </div>
    </div>
  );
};

const getScoreColor = (score) => {
  if (score >= 80) return "#10b981";
  if (score >= 60) return "#f59e0b";
  if (score >= 40) return "#f97316";
  return "#ef4444";
};

const getScoreLabel = (score) => {
  if (score >= 90) return "Excellent";
  if (score >= 80) return "Very Good";
  if (score >= 70) return "Good";
  if (score >= 60) return "Fair";
  if (score >= 40) return "Needs Improvement";
  return "Keep Practicing";
};

export default SummaryModal;
