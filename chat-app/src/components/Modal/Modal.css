.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 30% 70%, rgba(56, 178, 172, 0.15), transparent 50%),
    radial-gradient(circle at 70% 30%, rgba(128, 90, 213, 0.15), transparent 50%),
    rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modalFade 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

@keyframes modalFade {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    -webkit-backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }
}

/* Enhanced Modal Styles for AddBotModal */
.modal-content {
  background: 
    linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 36px;
  border-radius: 24px;
  width: 90%;
  max-width: 580px;
  max-height: 85vh;
  overflow-y: auto;
  box-shadow: 
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 16px 32px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: modalSlide 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
  border: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
  /* Remove conflicting overflow: hidden */
  /* Add smooth scrolling */
  scroll-behavior: smooth;
}

.modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(56, 178, 172, 0.5), 
    rgba(128, 90, 213, 0.5), 
    transparent
  );
  z-index: 1;
}

@keyframes modalSlide {
  from {
    transform: translateY(-30px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.modal-content h2 {
  margin-bottom: 32px;
  color: #1a202c;
  font-weight: 700;
  font-size: 1.75rem;
  text-align: center;
  position: relative;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.modal-content h2::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #38b2ac, #805ad5, #e53e3e);
  border-radius: 2px;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% { 
    background: linear-gradient(90deg, #38b2ac, #805ad5);
  }
  50% { 
    background: linear-gradient(90deg, #805ad5, #38b2ac);
  }
}

.form-group {
  margin-bottom: 20px;
  position: relative;
  transition: all 0.3s ease;
}

/* Special spacing for persona field */
.form-group:has(textarea) {
  margin-bottom: 24px;
}

.form-group:hover {
  transform: translateY(-1px);
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
  position: relative;
  padding-left: 16px;
  transition: color 0.3s ease;
}

/* Special styling for persona label */
.form-group:has(textarea) label {
  font-size: 1rem;
  margin-bottom: 10px;
}

.form-group label::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 18px;
  background: linear-gradient(180deg, #38b2ac, #805ad5);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.form-group:focus-within label::before {
  width: 6px;
  height: 20px;
  background: linear-gradient(180deg, #319795, #6b46c1);
  box-shadow: 0 0 12px rgba(56, 178, 172, 0.4);
}

.form-group:focus-within label {
  color: #1a202c;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  font-family: inherit;
  position: relative;
}

/* Special styling for persona textarea to make it bigger */
.form-group textarea {
  padding: 16px 18px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #38b2ac;
  box-shadow: 
    0 0 0 4px rgba(56, 178, 172, 0.1),
    0 8px 25px rgba(56, 178, 172, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(248, 250, 252, 1) 100%);
}

.form-group input:hover:not(:focus),
.form-group textarea:hover:not(:focus),
.form-group select:hover:not(:focus) {
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.form-group textarea {
  /* height: 180px; */
  resize: vertical;
  /* min-height: 160px; */
  /* max-height: 300px; */
  font-family: inherit;
  line-height: 1.6;
}

/* Enhanced Select Dropdown */
.form-group select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 16px center;
  background-repeat: no-repeat;
  background-size: 18px;
  padding-right: 48px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.image-upload-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  background: 
    linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  border: 2px dashed #cbd5e0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.image-upload-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(56, 178, 172, 0.05), 
    transparent
  );
  transition: left 0.6s ease;
}

.image-upload-section:hover {
  border-color: #38b2ac;
  background: 
    linear-gradient(135deg, #f0fff4 0%, #e6fffa 100%);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(56, 178, 172, 0.1),
    0 0 0 1px rgba(56, 178, 172, 0.1);
}

.image-upload-section:hover::before {
  left: 100%;
}

.image-upload-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.upload-separator {
  font-size: 0.9em;
  color: #718096;
  font-style: italic;
  padding: 0 8px;
}

.upload-button {
  padding: 10px 18px;
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 3px 12px rgba(56, 178, 172, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 6px;
}

.upload-button::before {
  content: '📎';
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.upload-button::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.25), 
    transparent
  );
  transition: left 0.6s ease;
}

.upload-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #319795 0%, #2c7a7b 100%);
  transform: translateY(-3px);
  box-shadow: 
    0 12px 35px rgba(56, 178, 172, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.upload-button:hover:not(:disabled)::before {
  transform: rotate(15deg) scale(1.1);
}

.upload-button:hover:not(:disabled)::after {
  left: 100%;
}

.upload-button:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.remove-image-button {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 3px 10px rgba(229, 62, 62, 0.3);
}

.remove-image-button:hover {
  background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%);
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

.image-preview {
  margin-top: 16px;
  text-align: center;
  position: relative;
  padding: 10px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.image-preview img {
  max-width: 180px;
  max-height: 180px;
  border-radius: 12px;
  border: 2px solid #ffffff;
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 3px 12px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  object-fit: cover;
}

.image-preview img:hover {
  transform: scale(1.05) rotate(1deg);
  box-shadow: 
    0 20px 48px rgba(0, 0, 0, 0.18),
    0 8px 24px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: #38b2ac;
}

/* Enhanced Warning Styles */
.upload-warning {
  font-size: 0.85em;
  color: #e53e3e;
  margin-top: 8px;
  margin-bottom: 0;
  padding: 8px 12px;
  background: rgba(229, 62, 62, 0.1);
  border-radius: 6px;
  border-left: 3px solid #e53e3e;
}

/* Enhanced Improve Persona Button */
.improve-button {
  margin-top: 8px;
  background: linear-gradient(135deg, #805ad5 0%, #6b46c1 100%);
  color: white;
  border: none;
  width: 100%;
  padding: 12px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 15px rgba(128, 90, 213, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.improve-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.25), 
    transparent
  );
  transition: left 0.6s ease;
}

.improve-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #6b46c1 0%, #553c9a 100%);
  transform: translateY(-3px);
  box-shadow: 
    0 12px 35px rgba(128, 90, 213, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.improve-button:hover:not(:disabled)::before {
  left: 100%;
}

.improve-button:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
}

.toolbar-icon.animated {
  font-size: 1.2em;
  animation: sparkle 2.5s ease-in-out infinite;
  display: inline-block;
}

@keyframes sparkle {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    filter: brightness(1) drop-shadow(0 0 2px rgba(255, 215, 0, 0.3));
  }
  25% { 
    transform: scale(1.1) rotate(90deg);
    filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  }
  50% { 
    transform: scale(1.15) rotate(180deg);
    filter: brightness(1.3) drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
  }
  75% { 
    transform: scale(1.1) rotate(270deg);
    filter: brightness(1.2) drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
  }
}

/* Enhanced Chat Bot Link */
.chat-bot-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  text-decoration: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 20px;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 4px 15px rgba(72, 187, 120, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.chat-bot-link::before {
  content: '💬';
  font-size: 1.1em;
  animation: chatPulse 2s ease-in-out infinite;
}

.chat-bot-link::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.25), 
    transparent
  );
  transition: left 0.6s ease;
}

.chat-bot-link:hover {
  background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
  transform: translateY(-3px);
  box-shadow: 
    0 12px 35px rgba(72, 187, 120, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  text-decoration: none;
  color: white;
}

.chat-bot-link:hover::after {
  left: 100%;
}

@keyframes chatPulse {
  0%, 100% { 
    transform: scale(1);
  }
  50% { 
    transform: scale(1.1);
  }
}

.modal-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 28px;
  padding-top: 20px;
  border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.modal-buttons button {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-width: 90px;
}

.modal-buttons .cancel {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: #4a5568;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(74, 85, 104, 0.1);
}

.modal-buttons .cancel:hover {
  background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 85, 104, 0.15);
  border-color: #cbd5e0;
}

.modal-buttons .save {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
  color: white;
  box-shadow: 
    0 6px 20px rgba(56, 178, 172, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.modal-buttons .save:hover:not(:disabled) {
  background: linear-gradient(135deg, #319795 0%, #2c7a7b 100%);
  transform: translateY(-3px);
  box-shadow: 
    0 12px 35px rgba(56, 178, 172, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.modal-buttons .save:disabled {
  background: #cbd5e0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Enhanced Mobile responsive styles */
@media (max-width: 768px) {
  .modal-content {
    padding: 24px;
    margin: 16px;
    border-radius: 20px;
    max-height: 95vh;
    width: calc(100% - 32px);
  }

  .modal-content h2 {
    font-size: 1.5rem;
    margin-bottom: 24px;
  }

  .form-group {
    margin-bottom: 24px;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    padding: 16px;
    font-size: 1rem;
  }

  .image-upload-section {
    padding: 20px;
  }

  .image-upload-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .upload-separator {
    text-align: center;
    padding: 12px 0;
  }
  
  .upload-button,
  .remove-image-button {
    align-self: center;
  }
  
  .image-preview img {
    max-width: 200px;
    max-height: 200px;
  }

  .voice-speed-container {
    padding: 14px;
  }

  .modal-buttons {
    flex-direction: column;
    gap: 16px;
    margin-top: 28px;
  }

  .modal-buttons button {
    width: 100%;
    padding: 16px 24px;
    font-size: 1.1rem;
  }

  .improve-button {
    padding: 18px;
    font-size: 1.1rem;
  }

  .chat-bot-link {
    padding: 16px 24px;
    font-size: 1.1rem;
  }
}

/* Enhanced scrollbar styling for better visual feedback */
.modal-content::-webkit-scrollbar {
  width: 8px;
  background: transparent;
}

.modal-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #38b2ac, #805ad5);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #319795, #6b46c1);
}

/* Responsive improvements for smaller screens */
@media (max-height: 700px) {
  .modal-content {
    max-height: 95vh;
    padding: 24px;
  }
  
  .modal-content h2 {
    margin-bottom: 20px;
    font-size: 1.5rem;
  }
  
  .form-group {
    margin-bottom: 16px;
  }
}

@media (max-height: 600px) {
  .modal-content {
    max-height: 98vh;
    padding: 20px;
  }
  
  .modal-content h2 {
    margin-bottom: 16px;
    font-size: 1.3rem;
  }
  
  .form-group {
    margin-bottom: 12px;
  }
  
  .form-group:has(textarea) {
    margin-bottom: 16px;
  }
}

/* Mobile responsive improvements */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-width: none;
    margin: 10px;
    padding: 24px 20px;
  }
  
  .modal {
    align-items: flex-start;
    padding-top: 20px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    padding: 20px 16px;
  }
  
  .modal-content h2 {
    font-size: 1.4rem;
    margin-bottom: 20px;
  }
  
  .form-group label {
    font-size: 0.85rem;
  }
  
  textarea, input[type="text"], select {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Permission Warning and Status */
.permission-warning,
.permission-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  cursor: help;
}

.permission-warning {
  color: #e53e3e;
}

.permission-status {
  color: #38a169;
}

/* Permission Modal Specific Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
  animation: modalFade 0.3s ease-out;
  backdrop-filter: blur(4px);
}

/* Enhanced styles for Permission Modal */
.permission-modal {
  background-color: white;
  padding: 30px;
  border-radius: 16px;
  width: 90%;
  max-width: 420px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: permissionModalSlide 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  text-align: center;
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

@keyframes permissionModalSlide {
  from {
    transform: translateY(-30px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* Existing header and paragraph styles */
.permission-modal h2 {
  margin-bottom: 20px;
  color: #2d3748;
  font-weight: 600;
  font-size: 1.5rem;
  line-height: 1.3;
}

.permission-modal p {
  margin-bottom: 30px;
  color: #4a5568;
  font-size: 1rem;
  line-height: 1.6;
  text-align: center;
}

/* Mobile improvements for Permission Modal */
@media (max-width: 480px) {
  .permission-modal {
    padding: 20px 16px;
    border-radius: 12px;
    font-size: 0.95rem;
    max-width: 90%;
  }
  
  .permission-modal h2 {
    font-size: 1.3rem;
    margin-bottom: 16px;
  }
  
  .permission-modal p {
    font-size: 0.9rem;
    margin-bottom: 20px;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 10px;
  }
  
  .primary-btn,
  .secondary-btn {
    width: 100%;
    padding: 14px 20px;
    font-size: 1rem;
    min-height: 48px;
    border-radius: 8px;
  }
}

/* Landscape orientation on phones */
@media (max-width: 768px) and (orientation: landscape) {
  .permission-modal {
    max-height: 90vh;
    overflow-y: auto;
    padding: 20px;
  }

  .modal-actions {
    flex-direction: row;
    justify-content: center;
  }

  .primary-btn,
  .secondary-btn {
    width: auto;
    min-width: 120px;
    padding: 12px 20px;
    min-height: 44px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .permission-modal {
    background-color: #2d3748;
    color: white;
    border-color: #4a5568;
  }

  .permission-modal h2 {
    color: #f7fafc;
  }

  .permission-modal p {
    color: #e2e8f0;
  }

  .secondary-btn {
    background-color: #4a5568;
    color: #f7fafc;
    border-color: #718096;
  }

  .secondary-btn:hover {
    background-color: #718096;
    border-color: #a0aec0;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .permission-modal {
    border: 3px solid #000;
  }

  .primary-btn,
  .secondary-btn {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .permission-modal {
    animation: none;
  }

  .modal-overlay {
    animation: none;
  }

  .primary-btn,
  .secondary-btn {
    transition: none;
  }

  .primary-btn:before {
    display: none;
  }
}

/* Focus styles for accessibility */
.primary-btn:focus-visible,
.secondary-btn:focus-visible {
  outline: 3px solid #63b3ed;
  outline-offset: 2px;
}

/* Touch device improvements */
@media (hover: none) and (pointer: coarse) {
  .primary-btn:hover,
  .secondary-btn:hover {
    transform: none;
  }

  .primary-btn:active,
  .secondary-btn:active {
    transform: scale(0.98);
  }
}

.modal-actions {
    display: flex;
    flex-direction: row; /* stacks items horizontally */
    gap: 12px;
}

.voice-speed-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
  padding: 12px;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  border-radius: 10px;
  border: 1px solid #e2e8f0;
}

.voice-speed-slider {
  flex: 1;
  height: 8px;
  background: 
    linear-gradient(to right, 
      #e2e8f0 0%, 
      #cbd5e0 50%, 
      #e2e8f0 100%
    );
  border-radius: 4px;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  cursor: pointer;
  position: relative;
}

.voice-speed-slider::-webkit-slider-thumb {
  appearance: none;
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: 
    linear-gradient(135deg, #38b2ac 0%, #319795 100%);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 3px 10px rgba(56, 178, 172, 0.3),
    0 0 0 2px rgba(255, 255, 255, 1),
    0 0 0 3px rgba(56, 178, 172, 0.1);
  position: relative;
}

.voice-speed-slider::-webkit-slider-thumb:hover {
  background: 
    linear-gradient(135deg, #319795 0%, #2c7a7b 100%);
  transform: scale(1.1);
  box-shadow: 
    0 4px 15px rgba(56, 178, 172, 0.4),
    0 0 0 2px rgba(255, 255, 255, 1),
    0 0 0 4px rgba(56, 178, 172, 0.15);
}

.voice-speed-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: 
    linear-gradient(135deg, #38b2ac 0%, #319795 100%);
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 3px 10px rgba(56, 178, 172, 0.3),
    0 0 0 2px rgba(56, 178, 172, 0.1);
}

.voice-speed-slider::-moz-range-thumb:hover {
  background: 
    linear-gradient(135deg, #319795 0%, #2c7a7b 100%);
  transform: scale(1.1);
  box-shadow: 
    0 4px 15px rgba(56, 178, 172, 0.4),
    0 0 0 3px rgba(56, 178, 172, 0.15);
}

.voice-speed-value {
  font-weight: 700;
  color: #2d3748;
  min-width: 40px;
  text-align: center;
  font-size: 1rem;
  background: linear-gradient(135deg, #38b2ac, #805ad5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.voice-speed-labels {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #718096;
  margin-top: 8px;
  font-weight: 500;
}

/* Bot Type Description Styles */
.bot-type-description {
  margin-top: 8px;
}

.bot-type-description small {
  color: #718096;
  font-size: 0.875rem;
  font-style: italic;
  display: block;
  background: linear-gradient(135deg, #f7fafc, #edf2f7);
  padding: 8px 12px;
  border-radius: 8px;
  border-left: 3px solid #38b2ac;
  transition: all 0.3s ease;
}

.bot-type-description small:hover {
  background: linear-gradient(135deg, #e6fffa, #b2f5ea);
  color: #2d3748;
}

