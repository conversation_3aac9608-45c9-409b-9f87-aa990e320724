.mission-modal {
  max-width: 700px;
  max-height: 80vh;
  overflow-y: auto;
}

.no-mission {
  text-align: center;
  padding: 2rem;
}

.no-mission p {
  margin-bottom: 1rem;
  color: #666;
}

.create-mission {
  padding: 1rem;
}

.create-mission h3 {
  margin-bottom: 1rem;
  color: #333;
}

.mission-textarea {
  width: 100%;
  min-height: 120px;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 1rem;
}

.mission-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Mission Progress Styles */
.mission-progress {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 14px;
  font-weight: 500;
}

.mission-status {
  padding: 0.25rem 0.5rem;
  background-color: #007bff;
  color: white;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.mission-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.mission-display {
  padding: 1rem;
}

.mission-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eee;
}

.mission-header h3 {
  margin: 0;
  color: #333;
  flex: 1;
}

.mission-header .mission-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.mission-description {
  margin-bottom: 1.5rem;
}

.mission-description p {
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.mission-objectives {
  margin-bottom: 1.5rem;
}

.mission-objectives h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.mission-objectives ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mission-objectives li {
  padding: 0.5rem 0;
  border-left: 3px solid #007bff;
  padding-left: 1rem;
  margin-bottom: 0.5rem;
  background: #f8f9fa;
  border-radius: 0 4px 4px 0;
}

.mission-steps {
  margin-bottom: 1.5rem;
}

.mission-steps h4 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.step-item {
  border-left: 4px solid #6c757d;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 0 8px 8px 0;
  transition: all 0.2s ease;
}

.step-item:hover {
  background: #e9ecef;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.step-status {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.step-action {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.step-status-text {
  font-size: 0.875rem;
  color: #666;
}

.mission-footer {
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
  text-align: center;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #545b62;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #c82333;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .mission-modal {
    max-width: 95vw;
    margin: 1rem;
  }

  .mission-header {
    flex-direction: column;
    gap: 1rem;
  }

  .mission-header .mission-actions {
    margin-left: 0;
    width: 100%;
    justify-content: center;
  }

  .mission-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .step-status {
    align-self: flex-start;
  }
}
