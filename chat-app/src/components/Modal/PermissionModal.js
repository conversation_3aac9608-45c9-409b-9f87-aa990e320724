import React from "react";
import "./Modal.css";

const PermissionModal = ({ onRequestPermission, onClose }) => {
  return (
    <div className="modal-overlay">
      <div className="modal permission-modal">
        <h2>🎤 Microphone Permission</h2>
        <p>
          This app uses voice recording for voice messages and speech
          recognition features. Would you like to grant microphone permission?
        </p>
        <div className="modal-actions">
          <button onClick={onRequestPermission} className="primary-btn">
            Grant Permission
          </button>
          <button onClick={onClose} className="secondary-btn">
            Skip for Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default PermissionModal;
