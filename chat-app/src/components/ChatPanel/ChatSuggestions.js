import React, { useState, useEffect } from "react";
import { getChatSuggestions } from "../../services/apiServices";
import "./ChatSuggestions.css";
import { ChatMessageSenderType } from "../../types/ChatMessageType";

const ChatSuggestions = ({
  currentBot,
  currentChat,
  lastMessage,
  onSuggestionClick,
  apiKey,
  scrollToBottom = null,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    scrollToBottom?.();
  }, [suggestions]);

  const fetchSuggestions = (refresh = false) => {
    if (!currentBot || !currentChat || !apiKey) {
      setIsLoading(false);
      setSuggestions([]);
      return;
    }
    setIsLoading(true);
    setSuggestions([]);

    const conversationHistory = currentChat?.buildConversationHistory() || [];
    if (conversationHistory.length === 0) {
      setIsLoading(false);
      setSuggestions([]);
      return;
    }

    getChatSuggestions({
      conversationHistory,
      context: "",
      apiKey,
      refresh,
    })
      .then((chatSuggestions) => {
        if (chatSuggestions) {
          setSuggestions(chatSuggestions);
          setIsLoading(false);
          scrollToBottom?.();
        }
      })
      .catch((error) => {
        console.error("Error fetching chat suggestions:", error);
        // Fallback suggestions in case of error
        setIsLoading(false);
        setSuggestions([]);
      });
  };

  useEffect(() => {
    if (!lastMessage || lastMessage.sender !== ChatMessageSenderType.BOT) {
      setSuggestions([]);
      setIsLoading(false);
      return;
    }

    if (lastMessage.isTyping) {
      setSuggestions([]);
      setIsLoading(true);
    } else {
      fetchSuggestions();
    }
  }, [lastMessage, lastMessage?.isTyping]);

  const handleRefresh = () => {
    fetchSuggestions(true);
  };

  if (isLoading) {
    return null;
  }

  if (!currentBot || !currentChat || currentChat.length === 0) {
    return null;
  }

  if (!suggestions || suggestions.length === 0) {
    return null;
  }

  const handleSuggestionClick = (suggestion) => {
    if (onSuggestionClick) {
      onSuggestionClick(suggestion);
    }

    setIsLoading(true);
    setSuggestions([]);
  };

  return (
    <div className="chat-suggestions-container">
      <div className="suggestions-header">
        <span>💡 Suggested responses:</span>
        <button
          className="refresh-button"
          onClick={handleRefresh}
          disabled={isLoading}
          title="Refresh suggestions"
        >
          🔄
        </button>
      </div>
      <div className="suggestions-list">
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            className="suggestion-button"
            onClick={() => handleSuggestionClick(suggestion)}
          >
            {(index == 0 ? "简单" : "地道") + ": " + suggestion}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ChatSuggestions;
