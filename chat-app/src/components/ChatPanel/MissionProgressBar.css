.mission-progress-bar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 8px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mission-progress-bar:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.mission-progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.mission-title {
  font-weight: 600;
  font-size: 0.9em;
  flex: 1;
  margin-right: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.mission-status {
  font-size: 0.75em;
  font-weight: 700;
  padding: 2px 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mission-progress-track {
  position: relative;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  overflow: hidden;
}

.mission-progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
  background: rgba(255, 255, 255, 0.9) !important;
}

.mission-progress-text {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.7em;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-right: 4px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .mission-progress-bar {
    margin: 8px 12px;
    padding: 10px 14px;
  }
  
  .mission-title {
    font-size: 0.85em;
  }
  
  .mission-status {
    font-size: 0.7em;
    padding: 1px 6px;
  }
}
