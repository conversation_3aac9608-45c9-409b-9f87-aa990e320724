import React, { useState, useRef, useEffect } from "react";
import Message from "../Message/Message";
import ChatInput from "./ChatInput";
import ChatToolbar from "./ChatToolbar";
import ChatSuggestions from "./ChatSuggestions";
import ChatSettingsMenu from "./ChatSettingsMenu";
import SummaryModal from "../Modal/SummaryModal";
import MissionModal from "../Modal/MissionModal";
import MissionProgressBar from "./MissionProgressBar";
import MissionCompletionEffect from "./MissionCompletionEffect";
import { handleConversationSummary } from "../../services/messageServices";
import "./ChatPanel.css";
import { ChatMessageSenderType } from "../../types/ChatMessageType";
import { ChatMessage } from "../../types/ChatMessage";
import { Mission } from "../../types";

const ChatPanel = ({
  currentBot,
  currentChat,
  lastMessage,
  autoTTSEnabled,
  onSendMessage,
  onResendMessage,
  onClearHistory,
  onSyncChatHistory = null,
  onExportChat = null,
  onImportChat = null,
  onToggleAutoTTS = null,
  onToggleChatSuggestions = null,
  chatSuggestionsEnabled = true,
  onDeleteMessage,
  apiKey,
  missionData = null,
  beforeMissionData = null,
}) => {
  const messagesEndRef = useRef(null);
  const fileInputRef = useRef(null);
  const [showSummaryModal, setShowSummaryModal] = useState(false);
  const [summaryData, setSummaryData] = useState(null);
  const [summaryLoading, setSummaryLoading] = useState(false);
  const [showSettingsMenu, setShowSettingsMenu] = useState(false);
  const [showMissionModal, setShowMissionModal] = useState(false);
  const [completionEffect, setCompletionEffect] = useState(null);
  const settingsMenuRef = useRef(null);

  useEffect(() => {
    scrollToBottom();
  }, [currentChat?.messages, lastMessage?.isTyping]);

  // Close settings menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        settingsMenuRef.current &&
        !settingsMenuRef.current.contains(event.target)
      ) {
        setShowSettingsMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Monitor mission status changes and show completion effects
  useEffect(() => {
    if (missionData) {
      const currentMission = Mission.fromLocalStorage(missionData);
      const prevMission = Mission.fromLocalStorage(beforeMissionData);

      if (currentMission && prevMission) {
        if (currentMission.isCompleted() && !prevMission.isCompleted()) {
          setCompletionEffect({
            mission: currentMission,
            type: "completed",
          });
        } else if (currentMission.hasFailed() && !prevMission.hasFailed()) {
          setCompletionEffect({
            mission: currentMission,
            type: "failed",
          });
        }
      }
    }
  }, [missionData, beforeMissionData]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleSendMessage = (message) => {
    // If message is a string (from suggestions), convert it to ChatMessage instance
    if (typeof message === "string") {
      const chatMessage = ChatMessage.createUserMessage(message);
      onSendMessage(chatMessage);
    } else if (message instanceof ChatMessage) {
      // If it's already a ChatMessage instance, pass it through
      onSendMessage(message);
    } else {
      // Handle other cases by trying to create a ChatMessage
      const chatMessage = ChatMessage.createUserMessage(message);
      onSendMessage(chatMessage);
    }
  };

  const handleImportChat = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        onImportChat(file);
      };
      reader.readAsText(file);
    }
    // Reset the input value to allow selecting the same file again
    event.target.value = "";
  };

  const handleDeleteMessage = (messageId) => {
    if (onDeleteMessage) {
      onDeleteMessage(messageId);
    }
  };

  const isLastUserMessage = (index) => {
    // Check if this is the last user message in the conversation
    const messages = currentChat?.messages || [];
    if (
      index === messages.length - 1 &&
      messages[index].sender === ChatMessageSenderType.USER
    ) {
      return true;
    }
    return false;
  };

  const handleResendMessage = (text) => {
    if (text) {
      // Use the specialized resend function if available, otherwise fall back to regular send
      if (onResendMessage) {
        onResendMessage(text);
      } else {
        // Convert text to ChatMessage instance before sending
        const chatMessage = ChatMessage.createUserMessage(text);
        onSendMessage(chatMessage);
      }
    }
  };

  const handleClearHistory = () => {
    setShowSettingsMenu(false);
    onClearHistory();
  };

  const handleSummary = async () => {
    setShowSettingsMenu(false);

    const messages = currentChat?.messages || [];
    if (!currentBot || !messages || messages.length === 0) {
      alert("No conversation to summarize. Start chatting first!");
      return;
    }

    // Filter out typing messages
    const validMessages = messages.filter((msg) => !msg.isTyping);

    if (validMessages.length === 0) {
      alert("No completed messages to summarize.");
      return;
    }

    setShowSummaryModal(true);
    setSummaryLoading(true);
    setSummaryData(null);

    try {
      const summary = await handleConversationSummary(
        validMessages,
        currentBot.name,
        apiKey
      );
      setSummaryData(summary);
    } catch (error) {
      console.error("Summary error:", error);
      alert(`Failed to generate summary: ${error.message}`);
      setShowSummaryModal(false);
    } finally {
      setSummaryLoading(false);
    }
  };

  const closeSummaryModal = () => {
    setShowSummaryModal(false);
    setSummaryData(null);
    setSummaryLoading(false);
  };

  const handleSyncChatHistory = async () => {
    setShowSettingsMenu(false);

    if (!onSyncChatHistory) {
      alert("Sync functionality is not available.");
      return;
    }

    if (!apiKey) {
      alert("API key is required for sync functionality.");
      return;
    }

    try {
      await onSyncChatHistory();
    } catch (error) {
      console.error("Sync error:", error);
      alert(`Failed to sync chat history: ${error.message}`);
    }
  };

  const toggleSettingsMenu = () => {
    setShowSettingsMenu(!showSettingsMenu);
  };

  const handleToggleChatSuggestions = () => {
    setShowSettingsMenu(false);
    if (onToggleChatSuggestions) {
      onToggleChatSuggestions();
    }
  };

  const handleToggleMission = () => {
    setShowSettingsMenu(false);
    setShowMissionModal(true);
  };

  const closeMissionModal = () => {
    setShowMissionModal(false);
  };

  const handleEffectDismiss = () => {
    setCompletionEffect(null);
  };

  const handleEffectClick = () => {
    setShowMissionModal(true);
  };

  const handleProgressBarClick = () => {
    setShowMissionModal(true);
  };

  const renderBotHeader = () => {
    if (!currentBot) {
      return <h2>Select a bot to start chatting</h2>;
    }
    // add image if available
    if (currentBot.image_url) {
      return (
        <div className="bot-header">
          <img
            src={currentBot.image_url}
            alt={currentBot.name}
            className="bot-image"
          />
          <div className="bot-header-text">
            <h2>{currentBot.name}</h2>
            {currentBot.hasTitle && currentBot.hasTitle() && (
              <div className="bot-title">{currentBot.getTitle()}</div>
            )}
          </div>
        </div>
      );
    }
    return (
      <div className="bot-header-text">
        <h2>{currentBot.name}</h2>
        {currentBot.hasTitle && currentBot.hasTitle() && (
          <div className="bot-title">{currentBot.getTitle()}</div>
        )}
      </div>
    );
  };

  return (
    <div className="chat-panel">
      <div className="chat-header">
        <div className="chat-header-content">
          {renderBotHeader()}
          <div className="chat-header-actions">
            <button
              className="settings-button"
              onClick={toggleSettingsMenu}
              title="Settings"
            >
              ⚙️
            </button>
            <ChatSettingsMenu
              isVisible={showSettingsMenu}
              onClearHistory={handleClearHistory}
              onSummary={handleSummary}
              onSyncChatHistory={handleSyncChatHistory}
              onToggleChatSuggestions={handleToggleChatSuggestions}
              onToggleMission={handleToggleMission}
              chatSuggestionsEnabled={chatSuggestionsEnabled}
              currentBot={currentBot}
              messagesLength={currentChat?.messages?.length || 0}
              menuRef={settingsMenuRef}
            />
          </div>
        </div>
      </div>

      {/* Mission Progress Bar */}
      {missionData && (
        <MissionProgressBar
          mission={Mission.fromLocalStorage(missionData)}
          onClick={handleProgressBarClick}
        />
      )}

      <div className="chat-messages">
        {!currentBot ? (
          <div className="message bot">
            Please select or create a bot to start chatting.
          </div>
        ) : (
          <>
            {/* Always show hello message when a bot is selected */}
            {false && (
              <div className="message bot">
                {currentBot.hello_message
                  ? currentBot.hello_message
                  : `Start chatting with ${currentBot.name}!`}
              </div>
            )}
            {/* Show all conversation messages */}
            {(currentChat?.messages || []).map((message, index) => (
              <Message
                key={`${message.id || index}`}
                message={message}
                autoTTSEnabled={autoTTSEnabled}
                apiKey={apiKey}
                onDelete={handleDeleteMessage}
                onResend={handleResendMessage}
                isLastMessage={isLastUserMessage(index)}
              />
            ))}
          </>
        )}
        <div ref={messagesEndRef} className="messages-end" />
      </div>

      {chatSuggestionsEnabled && (
        <ChatSuggestions
          currentBot={currentBot}
          currentChat={currentChat}
          lastMessage={lastMessage}
          onSuggestionClick={handleSendMessage}
          apiKey={apiKey}
          scrollToBottom={scrollToBottom}
        />
      )}

      {false && (
        <ChatToolbar
          onClearHistory={onClearHistory}
          onExportChat={onExportChat}
          onImportChat={onImportChat && handleImportChat}
          onToggleAutoTTS={onToggleAutoTTS}
          onSummary={handleSummary}
          autoTTSEnabled={autoTTSEnabled}
        />
      )}

      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={!currentBot}
        apiKey={apiKey}
      />

      <input
        type="file"
        ref={fileInputRef}
        style={{ display: "none" }}
        accept=".json"
        onChange={handleFileChange}
      />

      {showSummaryModal && (
        <SummaryModal
          onClose={closeSummaryModal}
          summary={summaryData}
          isLoading={summaryLoading}
        />
      )}

      {showMissionModal && (
        <MissionModal
          onClose={closeMissionModal}
          currentBot={currentBot}
          messages={currentChat?.messages || []}
          apiKey={apiKey}
          missionData={missionData}
        />
      )}

      {/* Mission Completion Effect */}
      {completionEffect && (
        <MissionCompletionEffect
          mission={completionEffect.mission}
          effectType={completionEffect.type}
          onDismiss={handleEffectDismiss}
          onClick={handleEffectClick}
          autoHideDelay={10000}
        />
      )}
    </div>
  );
};

export default ChatPanel;
