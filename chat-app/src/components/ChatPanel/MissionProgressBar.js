import React from "react";
import "./MissionProgressBar.css";

const MissionProgressBar = ({ mission, onClick }) => {
  if (!mission) return null;

  const progressPercentage = mission.getProgressPercentage();
  const isCompleted = mission.isCompleted();
  const hasFailed = mission.hasFailed();

  const getStatusColor = () => {
    if (isCompleted) return "#28a745";
    if (hasFailed) return "#dc3545";
    return "#007bff";
  };

  const getStatusText = () => {
    if (isCompleted) return "COMPLETED";
    if (hasFailed) return "FAILED";
    return "IN PROGRESS";
  };

  return (
    <div className="mission-progress-bar" onClick={onClick}>
      <div className="mission-progress-header">
        <span className="mission-title">{mission.title}</span>
        <span className="mission-status" style={{ color: getStatusColor() }}>
          {getStatusText()}
        </span>
      </div>
      <div className="mission-progress-track">
        <div
          className="mission-progress-fill"
          style={{
            width: `${progressPercentage}%`,
            backgroundColor: getStatusColor(),
          }}
        />
        <span className="mission-progress-text">{progressPercentage}%</span>
      </div>
    </div>
  );
};

export default MissionProgressBar;
