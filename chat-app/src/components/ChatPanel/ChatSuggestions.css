.chat-suggestions-container {
  margin: 16px 0;
  padding: 16px;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem;
  color: #4a5568;
  margin-bottom: 12px;
  font-weight: 500;
}

.refresh-button {
  background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
  border: none;
  border-radius: 6px;
  padding: 6px 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  box-shadow: 0 2px 4px rgba(56, 178, 172, 0.2);
  min-width: 32px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #319795 0%, #2c7a7b 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(56, 178, 172, 0.3);
}

.refresh-button:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(56, 178, 172, 0.2);
}

.refresh-button:disabled {
  background: #a0aec0;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: spin 1s linear infinite;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.suggestion-button {
  padding: 10px 14px;
  background-color: white;
  border: 1px solid #cbd5e0;
  border-radius: 8px;
  color: #2d3748;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  line-height: 1.4;
}

.suggestion-button:hover {
  background-color: #38b2ac;
  color: white;
  border-color: #38b2ac;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(56, 178, 172, 0.2);
}

.suggestions-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #718096;
  font-size: 0.9rem;
}

.loading-dots {
  display: flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background-color: #38b2ac;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .chat-suggestions-container {
    margin: 12px 0;
    padding: 12px;
  }
  
  .suggestion-button {
    padding: 12px;
    font-size: 0.85rem;
  }
}