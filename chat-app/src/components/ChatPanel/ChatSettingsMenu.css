.settings-menu {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  min-width: 160px;
  overflow: hidden;
}

.settings-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #4a5568;
  transition: background-color 0.2s ease;
}

.settings-menu-item:hover:not(:disabled) {
  background-color: #f7fafc;
  color: #2d3748;
}

.settings-menu-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.settings-icon {
  font-size: 16px;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .settings-menu {
    right: -8px;
    min-width: 140px;
  }
  
  .settings-menu-item {
    padding: 10px 12px;
    font-size: 13px;
  }
}