import React from "react";
import "./ChatSettingsMenu.css";

const ChatSettingsMenu = ({
  isVisible,
  onClearHistory,
  onSummary,
  onSyncChatHistory,
  onToggleChatSuggestions,
  onToggleMission,
  chatSuggestionsEnabled,
  currentBot,
  messagesLength,
  menuRef,
}) => {
  if (!isVisible) return null;

  return (
    <div ref={menuRef} className="settings-menu">
      <button className="settings-menu-item" onClick={onToggleChatSuggestions}>
        <span className="settings-icon">
          {chatSuggestionsEnabled ? "💡" : "💡"}
        </span>
        Chat Suggestions: {chatSuggestionsEnabled ? "ON" : "OFF"}
      </button>
      <button
        className="settings-menu-item"
        onClick={onToggleMission}
        disabled={!currentBot}
      >
        <span className="settings-icon">🎯</span>
        Mission
      </button>
      <button
        className="settings-menu-item"
        onClick={onSyncChatHistory}
        disabled={!onSyncChatHistory}
        title="Sync chat history to backend"
      >
        <span className="settings-icon">🔄</span>
        Sync History
      </button>
      <button
        className="settings-menu-item"
        onClick={onClearHistory}
        disabled={!currentBot || messagesLength === 0}
      >
        <span className="settings-icon">🗑️</span>
        Clear History
      </button>
      <button
        className="settings-menu-item"
        onClick={onSummary}
        disabled={!currentBot || messagesLength === 0}
      >
        <span className="settings-icon">📊</span>
        Summary
      </button>
    </div>
  );
};

export default ChatSettingsMenu;
