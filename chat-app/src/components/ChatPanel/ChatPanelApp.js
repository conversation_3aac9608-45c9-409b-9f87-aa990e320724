import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useUrlParams } from "../../hooks/useUrlParams";
import ChatPanel from "./ChatPanel";
import PermissionModal from "../Modal/PermissionModal";
import { useChatState } from "../../hooks/useChatState";
import { fetchChatbots } from "../../services/apiServices";
import { Chat } from "../../types";

const ChatPanelApp = () => {
  // Use React Router params to get bot ID from URL
  const { botId } = useParams();
  const navigate = useNavigate();
  const { getApiKeyFromUrl } = useUrlParams();

  const {
    bots,
    setBots,
    currentBotId,
    setCurrentBotId,
    chats,
    currentChat,
    autoTTSEnabled,
    apiKey,
    setApiKey,
    clearChatHistory,
    sendMessage,
    resendMessage,
    deleteMessage,
    lastMessage,
    toggleChatSuggestions,
    chatSuggestionsEnabled,
    mission,
    beforeMission,
  } = useChatState(botId);

  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [microphonePermission, setMicrophonePermission] = useState(null);

  // Enhanced mobile viewport handling
  useEffect(() => {
    const setVh = () => {
      // Set CSS custom property for mobile viewport height
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    };

    setVh();
    window.addEventListener("resize", setVh);
    window.addEventListener("orientationchange", () => {
      // Delay to ensure proper viewport calculation after orientation change
      setTimeout(setVh, 100);
    });

    return () => {
      window.removeEventListener("resize", setVh);
      window.removeEventListener("orientationchange", setVh);
    };
  }, []);

  // Set API key from URL parameter on component mount
  useEffect(() => {
    const urlApiKey = getApiKeyFromUrl();
    if (urlApiKey && urlApiKey !== apiKey) {
      setApiKey(urlApiKey);
    }
  }, [getApiKeyFromUrl, apiKey, setApiKey]);

  // Fetch bots from API if API key is available
  useEffect(() => {
    if (apiKey) {
      const loadBots = async () => {
        try {
          const botsList = await fetchChatbots(apiKey);
          setBots(botsList);
        } catch (error) {
          console.error("Failed to load chatbots:", error);
          // Fall back to local storage if API fails
          setBots(JSON.parse(localStorage.getItem("bots")) || []);
        }
      };
      loadBots();
    }
  }, [apiKey]);

  // Permission modal logic
  useEffect(() => {
    // Check if we should show the permission modal
    const hasAskedPermission = localStorage.getItem(
      "microphone-permission-asked"
    );

    if (
      !hasAskedPermission &&
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia
    ) {
      navigator.permissions
        .query({ name: "microphone" })
        .then((result) => {
          setMicrophonePermission(result.state);
          if (result.state === "prompt") {
            setShowPermissionModal(true);
          }
        })
        .catch(() => {
          // Fallback for browsers that don't support permissions API
          setMicrophonePermission("prompt");
        });
    }
  }, []);

  const requestMicrophonePermission = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicrophonePermission("granted");
      localStorage.setItem("microphone-permission-asked", "true");
      setShowPermissionModal(false);
    } catch (error) {
      console.error("Error requesting microphone permission:", error);
      setMicrophonePermission("denied");
      localStorage.setItem("microphone-permission-asked", "true");
      setShowPermissionModal(false);
    }
  };

  const closePermissionModal = () => {
    setShowPermissionModal(false);
    localStorage.setItem("microphone-permission-asked", "true");
  };

  // Set current bot ID from URL parameter
  useEffect(() => {
    if (botId && bots.length > 0) {
      const bot = bots.find((b) => b.id === botId);
      if (bot) {
        setCurrentBotId(botId);
      }
    }
  }, [botId, bots, setCurrentBotId]);

  // Redirect if bot doesn't exist
  useEffect(() => {
    if (botId && bots.length > 0 && !bots.find((bot) => bot.id === botId)) {
      navigate("/"); // Redirect to home if bot doesn't exist
    }
  }, [botId, bots, navigate]);

  // Update URL if bot selection changes
  useEffect(() => {
    if (currentBotId && currentBotId !== botId) {
      navigate(`/chat/${currentBotId}`);
    }
  }, [currentBotId, navigate, botId]);

  return (
    <div
      className="app-container no-bot-panel"
      style={{ height: "calc(var(--vh, 1vh) * 100)" }}
    >
      <ChatPanel
        currentBot={bots.find((bot) => bot.id === currentBotId)}
        currentChat={
          currentChat ||
          Chat.createEmpty(
            currentBotId,
            bots.find((bot) => bot.id === currentBotId)
          )
        }
        autoTTSEnabled={autoTTSEnabled}
        onSendMessage={sendMessage}
        onResendMessage={resendMessage}
        onClearHistory={clearChatHistory}
        onDeleteMessage={deleteMessage}
        onToggleChatSuggestions={toggleChatSuggestions}
        chatSuggestionsEnabled={chatSuggestionsEnabled}
        apiKey={apiKey}
        lastMessage={lastMessage}
        missionData={mission}
        beforeMissionData={beforeMission}
      />

      {showPermissionModal && (
        <PermissionModal
          onRequestPermission={requestMicrophonePermission}
          onClose={closePermissionModal}
        />
      )}
    </div>
  );
};

export default ChatPanelApp;
