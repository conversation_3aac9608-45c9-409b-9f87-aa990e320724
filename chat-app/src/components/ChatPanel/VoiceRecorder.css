.voice-recorder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.record-btn {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border: none;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  padding: 0;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.record-btn:hover {
  background: linear-gradient(135deg, #c53030 0%, #9c2a2a 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(229, 62, 62, 0.4);
}

.record-btn:disabled {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.recording {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.recording-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #e53e3e;
  font-size: 15px;
  font-weight: 600;
  background: linear-gradient(135deg, #fed7d7 0%, #fbb6b6 100%);
  padding: 8px 16px;
  border-radius: 20px;
  flex: 1;
}

.recording-dot {
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.stop-btn {
  background-color: #4a5568;
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.stop-btn:hover {
  background-color: #2d3748;
}

/* WeChat Style Voice Recorder */
.voice-recorder.wechat-style {
  width: 100%;
  height: 100%;
}

.wechat-record-btn {
  width: 100%;
  height: 48px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  user-select: none;
  -webkit-user-select: none;
  -webkit-touch-callout: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.wechat-record-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #f8f9fa 0%, #e2e8f0 100%);
  border-color: #38b2ac;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.wechat-record-btn:disabled {
  background: linear-gradient(135deg, #f5f5f5 0%, #e2e8f0 100%);
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

.wechat-record-btn.pressed {
  background: linear-gradient(135deg, #38b2ac 0%, #2c7a7b 100%);
  color: white;
  border-color: #38b2ac;
  transform: scale(0.98);
  box-shadow: 0 2px 4px rgba(56, 178, 172, 0.3);
}

.wechat-record-btn.recording {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  color: white;
  border-color: #e53e3e;
  animation: recording-pulse 1.5s infinite;
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.4);
}

.recording-indicator {
  font-size: 12px;
  animation: blink 1s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

@keyframes recording-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(229, 62, 62, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(229, 62, 62, 0);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* Mobile specific styles */
@media (max-width: 768px) {
  .wechat-record-btn {
    height: 52px;
    font-size: 15px;
    min-height: 52px;
    touch-action: manipulation;
    border-radius: 16px;
  }
  
  /* Improve touch targets */
  .voice-recorder .record-btn {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .wechat-record-btn,
  .recording-indicator,
  .recording-dot {
    animation: none;
  }
}