.mission-completion-effect {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
  cursor: pointer;
}

.mission-completion-effect.visible {
  transform: translateX(0);
  opacity: 1;
}

.mission-completion-effect:hover {
  transform: translateX(-5px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

.effect-content {
  padding: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
  position: relative;
}

.effect-icon {
  font-size: 2em;
  line-height: 1;
  flex-shrink: 0;
  animation: bounce 2s infinite;
}

.effect-text {
  flex: 1;
  min-width: 0;
}

.effect-title {
  font-weight: 700;
  font-size: 1.1em;
  margin-bottom: 4px;
  color: #2d3748;
}

.effect-message {
  font-size: 0.9em;
  color: #4a5568;
  line-height: 1.4;
}

.effect-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 1.5em;
  color: #a0aec0;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.effect-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #2d3748;
}

.effect-progress-bar {
  height: 3px;
  background: rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.effect-progress-fill {
  height: 100%;
  width: 100%;
  transform-origin: left;
}

/* Theme variations */
.mission-completion-effect.success {
  border-left: 4px solid #28a745;
}

.mission-completion-effect.failed {
  border-left: 4px solid #dc3545;
}

.mission-completion-effect.all-success {
  border-left: 4px solid #ffd700;
  background: linear-gradient(135deg, #fff9e6 0%, #fff 100%);
}

.mission-completion-effect.all-success .effect-icon {
  animation: bounce 1s infinite, rotate 3s linear infinite;
}

.mission-completion-effect.updated {
  border-left: 4px solid #007bff;
}

/* Animations */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes progressCountdown {
  from {
    transform: scaleX(1);
  }
  to {
    transform: scaleX(0);
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .mission-completion-effect {
    top: 10px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: calc(100vw - 20px);
  }
  
  .effect-content {
    padding: 14px;
    gap: 10px;
  }
  
  .effect-icon {
    font-size: 1.8em;
  }
  
  .effect-title {
    font-size: 1em;
  }
  
  .effect-message {
    font-size: 0.85em;
  }
}
