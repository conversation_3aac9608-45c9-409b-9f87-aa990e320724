import React, { useEffect, useState } from "react";
import "./MissionCompletionEffect.css";

const MissionCompletionEffect = ({
  mission,
  effectType,
  onDismiss,
  onClick,
  autoHideDelay = 10000,
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (autoHideDelay > 0) {
      const timer = setTimeout(() => {
        handleDismiss();
      }, autoHideDelay);

      return () => clearTimeout(timer);
    }
  }, [autoHideDelay]);

  const handleDismiss = () => {
    setIsVisible(false);
    setTimeout(() => {
      onDismiss();
    }, 300); // Wait for animation to complete
  };

  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const getEffectConfig = () => {
    switch (effectType) {
      case "completed":
        return {
          icon: "🎉",
          title: "Mission Completed!",
          message: `Congratulations! You've successfully completed "${mission?.title}".`,
          className: "success",
          color: "#28a745",
        };
      case "failed":
        return {
          icon: "❌",
          title: "Mission Failed",
          message: `Mission "${mission?.title}" has failed. Don't give up!`,
          className: "failed",
          color: "#dc3545",
        };
      case "all-completed":
        return {
          icon: "🏆",
          title: "All Missions Completed!",
          message: "Amazing! You've completed all your missions!",
          className: "all-success",
          color: "#ffd700",
        };
      default:
        return {
          icon: "📋",
          title: "Mission Updated",
          message: `Mission "${mission?.title}" has been updated.`,
          className: "updated",
          color: "#007bff",
        };
    }
  };

  const config = getEffectConfig();

  if (!isVisible) return null;

  return (
    <div
      className={`mission-completion-effect ${config.className} ${
        isVisible ? "visible" : ""
      }`}
    >
      <div className="effect-content" onClick={handleClick}>
        <div className="effect-icon">{config.icon}</div>
        <div className="effect-text">
          <div className="effect-title">{config.title}</div>
          <div className="effect-message">{config.message}</div>
        </div>
        <button
          className="effect-close"
          onClick={(e) => {
            e.stopPropagation();
            handleDismiss();
          }}
        >
          ×
        </button>
      </div>
      <div className="effect-progress-bar">
        <div
          className="effect-progress-fill"
          style={{
            backgroundColor: config.color,
            animation: `progressCountdown ${autoHideDelay}ms linear`,
          }}
        />
      </div>
    </div>
  );
};

export default MissionCompletionEffect;
