.chat-input {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background-color: #f7f8fa;
  border-top: 1px solid #e2e8f0;
  gap: 8px;
  min-height: 60px;
}

/* Mode Switch Button */
.mode-switch-btn {
  width: 44px;
  height: 44px;
  border: 1px solid #d1d1d6;
  border-radius: 8px;
  background-color: #fff;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.mode-switch-btn:hover {
  background-color: #f5f5f5;
  border-color: #38b2ac;
}

.mode-switch-btn:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}

.mode-switch-btn.voice {
  background-color: #38b2ac;
  color: white;
  border-color: #38b2ac;
}

.mode-switch-btn.voice:hover {
  background-color: #319795;
}

/* Voice Input Container */
.voice-input-container {
  flex-grow: 1;
  height: 44px;
  display: flex;
  align-items: center;
}

/* Text Input */
.text-input {
  flex-grow: 1;
  height: 44px;
  border: 1px solid #d1d1d6;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 16px;
  resize: none;
  background-color: #fff;
  color: #2d3748;
  font-family: inherit;
  transition: border-color 0.2s ease;
  overflow-y: auto;
  line-height: 1.4;
}

.text-input:focus {
  outline: none;
  border-color: #38b2ac;
}

.text-input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

/* Send Button */
.send-btn {
  width: 44px;
  height: 44px;
  background-color: #38b2ac;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover:not(:disabled) {
  background-color: #319795;
}

.send-btn:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
}

.send-btn:active {
  transform: scale(0.95);
}

/* Mobile improvements */
@media (max-width: 768px) {
  .chat-input {
    padding: 8px 12px;
    min-height: 56px;
  }

  .mode-switch-btn,
  .send-btn {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .text-input {
    height: 40px;
    font-size: 16px; /* Prevent iOS zoom */
    padding: 0 10px;
  }

  .voice-input-container {
    height: 40px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .mode-switch-btn,
  .text-input,
  .send-btn {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mode-switch-btn,
  .send-btn,
  .text-input {
    transition: none;
  }
}