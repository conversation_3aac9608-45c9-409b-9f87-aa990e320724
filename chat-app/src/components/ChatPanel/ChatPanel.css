.chat-panel {
  width: 75%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.chat-header {
  padding: 18px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
  z-index: 10;
}

.bot-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.bot-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
  background-color: #f7fafc;
  flex-shrink: 0;
}

.bot-image:hover {
  border-color: #38b2ac;
  transition: border-color 0.2s ease;
}

.chat-header h2 {
  font-size: 1.2em;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.bot-header-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.bot-header-text .bot-title {
  font-size: 0.9em;
  color: #718096;
  font-style: italic;
  font-weight: 400;
}

.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 25px;
  display: flex;
  flex-direction: column;
  background-color: #f8fafc;
  background-image: radial-gradient(
      circle at 25px 25px,
      rgba(0, 0, 0, 0.01) 2%,
      transparent 0%
    ),
    radial-gradient(
      circle at 75px 75px,
      rgba(0, 0, 0, 0.01) 2%,
      transparent 0%
    );
  background-size: 100px 100px;
}

/* Add styles for standalone chat panel app */
.app-container .chat-panel:only-child {
  width: 100%;
  height: 100vh;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .chat-panel {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .chat-header {
    padding: 16px 20px;
    flex-shrink: 0;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .bot-header {
    gap: 10px;
  }
  
  .bot-image {
    width: 36px;
    height: 36px;
  }
  
  .chat-header h2 {
    font-size: 1.1em;
  }

  .chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Improve message spacing on mobile */
  .message {
    max-width: 85%;
    margin-bottom: 16px;
    padding: 12px 16px;
    font-size: 0.95rem;
    line-height: 1.4;
  }

  /* Better touch targets for message actions */
  .message-actions {
    padding: 8px 0;
  }

  .message-actions button {
    min-height: 44px;
    min-width: 44px;
    padding: 8px 12px;
  }
}

/* For extra small phones */
@media (max-width: 480px) {
  .chat-header {
    padding: 12px 16px;
  }

  .chat-messages {
    padding: 12px;
  }

  .bot-header {
    gap: 8px;
  }
  
  .bot-image {
    width: 32px;
    height: 32px;
  }
  
  .chat-header h2 {
    font-size: 1rem;
  }

  .message {
    max-width: 90%;
    margin-bottom: 24px;
    padding: 10px 14px;
    font-size: 0.9rem;
  }
}

/* Landscape mode adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .chat-header {
    padding: 12px 20px;
  }

  .chat-messages {
    padding: 12px 16px;
  }

  .bot-image {
    width: 32px;
    height: 32px;
  }
}

/* Add styles for standalone chat panel app - Enhanced mobile */
.app-container .chat-panel:only-child {
  width: 100%;
  height: 100vh;
}

@media (max-width: 768px) {
  .app-container .chat-panel:only-child {
    height: 100vh;
    position: relative;
  }
}

/* Chat header updates */
.chat-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.chat-header-actions {
  position: relative;
  display: flex;
  align-items: center;
}

.settings-button {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  color: #4a5568;
}

.settings-button:hover {
  background-color: #f7fafc;
  color: #2d3748;
}

/* Mobile responsive adjustments for settings button */
@media (max-width: 768px) {
  .settings-button {
    padding: 6px;
    font-size: 18px;
  }
}