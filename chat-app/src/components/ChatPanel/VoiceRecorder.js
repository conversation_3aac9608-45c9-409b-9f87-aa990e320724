import React, { useState, useRef } from "react";
import { API_BASE_URL } from "../../config";
import { getApiHeaders, getFileUploadHeaders } from "../../utils/userAuth";
import { uuid } from "../../utils/uniqueId";
import "./VoiceRecorder.css";
import { ChatMessage } from "../../types";

const VoiceRecorder = ({
  onVoiceMessageReady,
  apiKey,
  disabled,
  isWeChatStyle = false,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isPressed, setIsPressed] = useState(false);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const timerRef = useRef(null);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Start timer
      setRecordingTime(0);
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } catch (error) {
      console.error("Error starting recording:", error);
      alert("Failed to access microphone. Please check permissions.");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      clearInterval(timerRef.current);

      mediaRecorderRef.current.onstop = async () => {
        try {
          if (recordingTime >= 1) {
            const audioBlob = new Blob(audioChunksRef.current, {
              type: "audio/webm",
            });

            // Upload the audio file
            const formData = new FormData();
            formData.append(
              "file",
              audioBlob,
              `chatbot_user_audio-voice-message_${uuid()}.webm`
            );

            const response = await fetch(`${API_BASE_URL}/api/upload`, {
              method: "POST",
              headers: getFileUploadHeaders(),
              body: formData,
            });

            if (!response.ok) {
              throw new Error(`Upload failed: ${response.status}`);
            }

            const data = await response.json();
            const voiceMessage = ChatMessage.createUserVoiceMessage(
              data.url,
              recordingTime
            );
            console.log("Voice message ready:", voiceMessage);
            onVoiceMessageReady(voiceMessage);
          }
          // Release microphone access
          mediaRecorderRef.current.stream
            .getTracks()
            .forEach((track) => track.stop());
        } catch (error) {
          console.error("Error processing voice message:", error);
          alert("Failed to process voice message. Please try again.");
        } finally {
          setIsProcessing(false);
        }
      };
    }
  };

  // WeChat-style long press handlers
  const handleMouseDown = () => {
    if (disabled || isProcessing || !isWeChatStyle) return;
    setIsPressed(true);
    startRecording();
  };

  const handleMouseUp = () => {
    if (!isWeChatStyle) return;
    setIsPressed(false);
    if (isRecording) {
      stopRecording();
    }
  };

  const handleMouseLeave = () => {
    if (!isWeChatStyle) return;
    setIsPressed(false);
    if (isRecording) {
      stopRecording();
    }
  };

  // Regular click handler for non-WeChat style
  const handleClick = () => {
    if (isWeChatStyle) return;

    if (!isRecording) {
      startRecording();
    } else {
      stopRecording();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  if (isWeChatStyle) {
    return (
      <div className="voice-recorder wechat-style">
        <button
          className={`wechat-record-btn ${isPressed ? "pressed" : ""} ${
            isRecording ? "recording" : ""
          }`}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseLeave}
          onTouchStart={handleMouseDown}
          onTouchEnd={handleMouseUp}
          disabled={disabled || isProcessing}
        >
          {isProcessing ? (
            "Processing..."
          ) : isRecording ? (
            <>
              <span className="recording-indicator">🔴</span>
              <span>Recording... {formatTime(recordingTime)}</span>
            </>
          ) : (
            "Hold to Talk"
          )}
        </button>
      </div>
    );
  }

  // Original style for backward compatibility
  return (
    <div className="voice-recorder">
      {!isRecording ? (
        <button
          className="record-btn"
          onClick={handleClick}
          disabled={disabled || isProcessing}
        >
          {isProcessing ? "Processing..." : "🎤"}
        </button>
      ) : (
        <div className="recording">
          <div className="recording-indicator">
            <span className="recording-dot"></span>
            Recording... {formatTime(recordingTime)}
          </div>
          <button className="stop-btn" onClick={stopRecording}>
            ⏹️
          </button>
        </div>
      )}
    </div>
  );
};

export default VoiceRecorder;
