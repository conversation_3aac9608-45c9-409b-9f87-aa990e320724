import React from "react";
import "./ChatToolbar.css";

const ChatToolbar = ({
  onClearHistory,
  onExportChat,
  onImportChat,
  onToggleAutoTTS,
  onSummary,
  autoTTSEnabled,
}) => {
  return (
    <div className="chat-toolbar">
      <button className="toolbar-button" onClick={onClearHistory}>
        <span className="toolbar-icon">🗑️</span> Clear History
      </button>
      {onExportChat && (
        <button className="toolbar-button" onClick={onExportChat}>
          <span className="toolbar-icon">📤</span> Export Chat
        </button>
      )}
      {onImportChat && (
        <button className="toolbar-button" onClick={onImportChat}>
          <span className="toolbar-icon">📥</span> Import Chat
        </button>
      )}
      {onSummary && (
        <button className="toolbar-button" onClick={onSummary}>
          <span className="toolbar-icon">📊</span> Summary
        </button>
      )}
      {onToggleAutoTTS && (
        <button className="toolbar-button" onClick={onToggleAutoTTS}>
          {autoTTSEnabled ? "🔊 Auto TTS: ON" : "🔈 Auto TTS: OFF"}
        </button>
      )}
    </div>
  );
};

export default ChatToolbar;
