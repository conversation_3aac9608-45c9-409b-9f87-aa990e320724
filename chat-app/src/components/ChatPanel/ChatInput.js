import React, { useState, useRef, useEffect } from "react";
import VoiceRecorder from "./VoiceRecorder";
import "./ChatInput.css";
import { Chat, ChatMessage } from "../../types";

const ChatInput = ({ onSendMessage, disabled, apiKey }) => {
  const [message, setMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [inputMode, setInputMode] = useState("text"); // "text" or "voice"
  const textareaRef = useRef(null);

  useEffect(() => {
    if (inputMode === "text") {
      adjustTextareaHeight();
    }
  }, [message, inputMode]);

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = "auto";

    // Set new height based on scrollHeight, with a max height
    const newHeight = Math.min(textarea.scrollHeight, 120);
    textarea.style.height = newHeight + "px";
  };

  const handleChange = (e) => {
    setMessage(e.target.value);
  };

  const handleSubmit = async () => {
    if (!message.trim() || disabled) return;

    setIsSending(true);
    await onSendMessage(ChatMessage.createUserMessage(message.trim()));
    setMessage("");
    setIsSending(false);
  };

  const handleVoiceMessage = async (voiceMessage) => {
    if (!voiceMessage.content || !voiceMessage.duration || disabled) return;

    setIsSending(true);
    // Send voice message with a special format to distinguish it
    await onSendMessage(voiceMessage);
    setIsSending(false);
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const toggleInputMode = () => {
    setInputMode(inputMode === "text" ? "voice" : "text");
  };

  return (
    <div className="chat-input">
      {/* Mode Switch Button */}
      <button
        className={`mode-switch-btn ${inputMode}`}
        onClick={toggleInputMode}
        disabled={disabled || isSending}
        title={
          inputMode === "text"
            ? "Switch to voice input"
            : "Switch to text input"
        }
      >
        {inputMode === "text" ? "🎤" : "⌨️"}
      </button>

      {/* Voice Input Mode */}
      {inputMode === "voice" && (
        <div className="voice-input-container">
          <VoiceRecorder
            onVoiceMessageReady={handleVoiceMessage}
            apiKey={apiKey}
            disabled={disabled || isSending}
            isWeChatStyle={true}
          />
        </div>
      )}

      {/* Text Input Mode */}
      {inputMode === "text" && (
        <>
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleChange}
            onKeyPress={handleKeyPress}
            placeholder="Type a message..."
            disabled={disabled || isSending}
            className="text-input"
          />
          <button
            className="send-btn"
            onClick={handleSubmit}
            disabled={disabled || isSending || !message.trim()}
          >
            {isSending ? (
              <span className="sending-icon">⏳</span>
            ) : (
              <span className="send-icon">📤</span>
            )}
          </button>
        </>
      )}
    </div>
  );
};

export default ChatInput;
