import React, { useState } from "react";
import "./MessageResultPanel.css";

const MessageResultPanel = ({ type, data, isLoading, onClose }) => {
  const [showDetails, setShowDetails] = useState(false);

  if (isLoading) {
    return (
      <div className="message-result-panel active">
        <div className="result-loading">
          <div className="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    );
  }

  if (!data) {
    return null;
  }

  if (data.error) {
    return (
      <div className="message-result-panel active">
        <div className="result-error">Error: {data.error}</div>
      </div>
    );
  }

  const renderTTSContent = () => {
    // console.log("TTS Data:", data);
    if (!data.audio && !data.audio_url) return <div>No audio available</div>;

    return (
      <div className="result-content">
        <audio controls autoPlay>
          <source
            src={
              (data.audio && `data:audio/mp3;base64,${data.audio}`) ||
              data.audio_url
            }
            type="audio/mp3"
          />
          Your browser does not support audio playback.
        </audio>
      </div>
    );
  };

  const renderGrammarContent = () => {
    const suggestions = data.suggestions || {};
    const errorCount = suggestions.errors?.length || 0;

    return (
      <div className="result-content">
        <div className="grammar-header">
          <span className="grammar-badge">{errorCount}</span>
          <h4>Grammar Suggestions</h4>
        </div>

        {suggestions.errors && suggestions.errors.length > 0 && (
          <div className="grammar-errors">
            <h5>Errors:</h5>
            {suggestions.errors.map((error, index) => (
              <div key={index} className="grammar-error">
                <div className="error-text">{error}</div>
              </div>
            ))}
          </div>
        )}

        {suggestions.improvements && suggestions.improvements.length > 0 && (
          <div className="grammar-warnings">
            <h5>Style Improvements:</h5>
            {suggestions.improvements.map((improvement, index) => (
              <div key={index} className="grammar-warning">
                <div className="warning-text">{improvement}</div>
              </div>
            ))}
          </div>
        )}

        {suggestions.suggested_text && (
          <div className="grammar-suggestions">
            <h5>Improved Text:</h5>
            <div className="grammar-improved">
              <p>{suggestions.suggested_text}</p>
            </div>
          </div>
        )}

        {errorCount === 0 && !suggestions.suggested_text && (
          <p>No grammar issues found!</p>
        )}
      </div>
    );
  };

  const renderTranslationContent = () => {
    return (
      <div className="result-content">
        <div className="translation-header">
          <h4>Translation</h4>
        </div>
        <div className="translated-text">{data.translated_text}</div>
      </div>
    );
  };

  const renderASRContent = () => {
    if (!data.transcription) return <div>No transcription available</div>;

    return (
      <div className="result-content">
        <h4>Transcription:</h4>
        <div className="asr-result">{data.transcription}</div>
      </div>
    );
  };

  const renderPronunciationContent = () => {
    if (!data) return <div>No assessment data available</div>;

    const result = data;
    const overallScore = Math.round(result.pronunciation_score);

    // Helper function to get score color class based on overall score
    const getScoreColorClass = (score) => {
      if (score >= 80) return "score-excellent";
      if (score >= 60) return "score-good";
      return "score-poor";
    };

    const scoreColorClass = getScoreColorClass(overallScore);

    return (
      <div className="result-content">
        {/* Overall Score Display with dynamic color */}
        <div className="pronunciation-score">
          <div className={`score-display ${scoreColorClass}`}>
            <div className="score-value">{overallScore}</div>
            <div className="score-label">Overall Score</div>
          </div>
        </div>

        {/* Toggle Button for Details */}
        <div className="details-toggle">
          <button
            className="toggle-details-btn"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? "Hide Details" : "Show Details"}
            <span className={`toggle-icon ${showDetails ? "expanded" : ""}`}>
              ▼
            </span>
          </button>
        </div>

        {/* Collapsible Details Section */}
        <div
          className={`details-section ${
            showDetails ? "expanded" : "collapsed"
          }`}
        >
          {/* Detailed Scores Grid */}
          <div className="score-grid">
            <div className="score-item">
              <div className="score-value">
                {Math.round(result.accuracy_score || 0)}
              </div>
              <div className="score-label">Accuracy</div>
            </div>
            <div className="score-item">
              <div className="score-value">
                {Math.round(result.fluency_score || 0)}
              </div>
              <div className="score-label">Fluency</div>
            </div>
            <div className="score-item">
              <div className="score-value">
                {Math.round(result.completeness_score || 0)}
              </div>
              <div className="score-label">Completeness</div>
            </div>
          </div>

          {/* Text Comparison */}
          {result.reference_text && result.recognized_text && (
            <div className="text-comparison">
              <div className="text-section">
                <h5>Reference Text:</h5>
                <div className="reference-text">{result.reference_text}</div>
              </div>
              <div className="text-section">
                <h5>Your Speech:</h5>
                <div className="recognized-text">{result.recognized_text}</div>
              </div>
            </div>
          )}

          {/* Word Analysis */}
          {false && result.words && result.words.length > 0 && (
            <div className="word-analysis">
              <h5>Word-by-Word Analysis:</h5>
              <div className="word-list">
                {result.words.map((word, index) => (
                  <div
                    key={index}
                    className={`word-item ${getWordScoreClass(
                      word.accuracy_score
                    )}`}
                    title={`Accuracy: ${Math.round(word.accuracy_score)}%`}
                  >
                    <span>{word.word}</span>
                    <span className="word-score">
                      {Math.round(word.accuracy_score)}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Helper function to determine word score class
  const getWordScoreClass = (score) => {
    if (score >= 80) return "excellent";
    if (score >= 60) return "good";
    if (score >= 40) return "fair";
    return "poor";
  };

  // Helper function to determine phoneme score class
  const getPhonemeScoreClass = (score) => {
    if (score >= 80) return "excellent";
    if (score >= 60) return "good";
    if (score >= 40) return "fair";
    return "poor";
  };

  // Helper function to generate improvement tips
  const generateImprovementTips = (result) => {
    const tips = [];

    if (result.accuracy_score < 70) {
      tips.push("Focus on clear pronunciation of individual sounds");
    }
    if (result.fluency_score < 70) {
      tips.push("Practice speaking at a natural pace with proper rhythm");
    }
    if (result.completeness_score < 80) {
      tips.push("Make sure to pronounce all words completely");
    }
    if (result.pronunciation_score >= 90) {
      tips.push("Excellent pronunciation! Keep up the great work!");
    } else if (result.pronunciation_score >= 70) {
      tips.push("Good pronunciation with room for minor improvements");
    }

    // Add specific word improvement tips
    if (result.words) {
      const poorWords = result.words.filter((w) => w.accuracy_score < 60);
      if (poorWords.length > 0) {
        tips.push(
          `Pay special attention to: ${poorWords.map((w) => w.word).join(", ")}`
        );
      }
    }

    return tips.map((tip, index) => <li key={index}>{tip}</li>);
  };

  return (
    <div className="message-result-panel active">
      {type === "tts" && renderTTSContent()}
      {type === "grammar" && renderGrammarContent()}
      {type === "translation" && renderTranslationContent()}
      {type === "asr" && renderASRContent()}
      {type === "pronunciation" && renderPronunciationContent()}
    </div>
  );
};

export default MessageResultPanel;
