.message-result-panel {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
  background-color: #f8fafc;
  border-radius: 6px;
  margin-top: 4px;
}

.message-result-panel.active {
  max-height: 300px;
  border: 1px solid #e2e8f0;
  margin-top: 8px;
  overflow-y: auto;
}

.result-content {
  padding: 10px;
}

.result-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px;
}

.result-error {
  color: #e53e3e;
  padding: 10px;
  font-size: 0.9em;
}

.grammar-header,
.translation-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.grammar-badge {
  background-color: #e53e3e;
  color: white;
  border-radius: 50%;
  min-width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8em;
  font-weight: bold;
  margin-right: 8px;
}

.grammar-errors {
  margin-bottom: 12px;
}

.grammar-error {
  background-color: #fff8f8;
  border-left: 3px solid red;
  padding: 8px;
  margin-bottom: 8px;
}

.error-text {
  color: red;
  font-size: 0.9em;
  margin-bottom: 4px;
}

.grammar-improved {
  background-color: #f0fff4;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid green;
}

.grammar-improved h5 {
  margin-top: 0;
  margin-bottom: 5px;
  color: green;
}

.grammar-improved p {
  color: green;
}

.grammar-warnings {
  margin-bottom: 12px;
}

.grammar-warning {
  background-color: #fff8f8;
  border-left: 3px solid orange;
  padding: 8px;
  margin-bottom: 8px;
}

.warning-text {
  color: orange;
  font-size: 0.9em;
}

.translated-text {
  background-color: #ebf8ff;
  padding: 10px;
  border-radius: 4px;
  border-left: 3px solid #3182ce;
}

.asr-result {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 5px;
  font-size: 14px;
  line-height: 1.5;
  white-space: pre-wrap;
}

/* Pronunciation Assessment Styles */
.pronunciation-header {
  margin-bottom: 20px;
  text-align: center;
}

.pronunciation-header h4 {
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.pronunciation-header h4:before {
  content: "🎯";
  font-size: 1.2em;
}

/* Overall Score Display */
.pronunciation-score {
  text-align: center;
  margin-bottom: 15px; /* Reduced from 25px */
}

.score-display {
  display: inline-block;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  margin: 0 auto;
  transition: all 0.3s ease;
}

/* Score Color Classes */
.score-display.score-excellent {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  box-shadow: 0 8px 20px rgba(72, 187, 120, 0.4);
}

.score-display.score-good {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  box-shadow: 0 8px 20px rgba(237, 137, 54, 0.4);
}

.score-display.score-poor {
  background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
  box-shadow: 0 8px 20px rgba(229, 62, 62, 0.4);
}

.score-display .score-value {
  font-size: 2.5rem;
  font-weight: bold;
  line-height: 1;
}

.score-display .score-label {
  font-size: 0.9rem;
  font-weight: 500;
  opacity: 0.9;
}

/* Add hover effects for better interactivity */
.score-display:hover {
  transform: scale(1.05);
}

.score-display.score-excellent:hover {
  box-shadow: 0 12px 24px rgba(72, 187, 120, 0.5);
}

.score-display.score-good:hover {
  box-shadow: 0 12px 24px rgba(237, 137, 54, 0.5);
}

.score-display.score-poor:hover {
  box-shadow: 0 12px 24px rgba(229, 62, 62, 0.5);
}

/* Detailed Scores Grid */
.score-grid {
  /* display: grid; */
  /* grid-template-columns: repeat(3, 1fr); */
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.score-item {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 15px;
  text-align: center;
  transition: all 0.3s ease;
}

.score-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.score-item .score-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2d3748;
  margin-bottom: 5px;
}

.score-item .score-label {
  font-size: 0.85rem;
  color: #718096;
  font-weight: 500;
}

/* Text Comparison */
.text-comparison {
  margin-bottom: 25px;
}

.text-section {
  margin-bottom: 15px;
}

.text-section h5 {
  color: #4a5568;
  font-size: 0.95rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.reference-text {
  background: #f7fafc;
  border-left: 4px solid #4299e1;
  padding: 12px 15px;
  border-radius: 8px;
  font-family: 'Segoe UI', system-ui, sans-serif;
  line-height: 1.5;
}

.recognized-text {
  background: #f0fff4;
  border-left: 4px solid #48bb78;
  padding: 12px 15px;
  border-radius: 8px;
  font-family: 'Segoe UI', system-ui, sans-serif;
  line-height: 1.5;
}

/* Word Analysis */
.word-analysis {
  margin-bottom: 25px;
}

.word-analysis h5 {
  color: #4a5568;
  font-size: 0.95rem;
  margin-bottom: 12px;
  font-weight: 600;
}

.word-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.word-item {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.word-item.excellent {
  background-color: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.word-item.good {
  background-color: #bee3f8;
  color: #2a4365;
  border: 1px solid #90cdf4;
}

.word-item.fair {
  background-color: #fef5e7;
  color: #744210;
  border: 1px solid #f6e05e;
}

.word-item.poor {
  background-color: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.word-score {
  background-color: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.word-error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #e53e3e;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.75rem;
}

/* Phoneme Analysis */
.phoneme-analysis {
  margin-bottom: 25px;
}

.phoneme-analysis h5 {
  color: #4a5568;
  font-size: 0.95rem;
  margin-bottom: 12px;
  font-weight: 600;
}

.word-phonemes {
  margin-bottom: 12px;
  padding: 10px;
  background: #fafafa;
  border-radius: 8px;
}

.word-title {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 6px;
}

.phoneme-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.phoneme-item {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: help;
}

.phoneme-item.excellent {
  background-color: #c6f6d5;
  color: #22543d;
}

.phoneme-item.good {
  background-color: #bee3f8;
  color: #2a4365;
}

.phoneme-item.fair {
  background-color: #fef5e7;
  color: #744210;
}

.phoneme-item.poor {
  background-color: #fed7d7;
  color: #742a2a;
}

/* Improvement Tips */
.improvement-tips {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 20px;
  border-radius: 12px;
  margin-top: 20px;
}

.improvement-tips h5 {
  margin-bottom: 12px;
  font-size: 1rem;
  font-weight: 600;
}

.improvement-tips ul {
  margin: 0;
  padding-left: 20px;
}

.improvement-tips li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.improvement-tips li:last-child {
  margin-bottom: 0;
}

/* Details Toggle Section */
.details-toggle {
  text-align: center;
  margin: 20px 0;
}

.toggle-details-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.toggle-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.toggle-details-btn:active {
  transform: translateY(0);
}

.toggle-icon {
  transition: transform 0.3s ease;
  font-size: 0.8em;
}

.toggle-icon.expanded {
  transform: rotate(180deg);
}

/* Collapsible Details Section */
.details-section {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  max-height: 0;
}

.details-section.expanded {
  opacity: 1;
  max-height: 2000px; /* Large enough to accommodate all content */
}

.details-section.collapsed {
  opacity: 0;
  max-height: 0;
  margin: 0;
  padding: 0;
}

/* Add padding when expanded */
.details-section.expanded .score-grid,
.details-section.expanded .text-comparison,
.details-section.expanded .word-analysis,
.details-section.expanded .improvement-tips {
  margin-bottom: 25px;
}

/* Smooth animation for grid items */
.details-section.expanded .score-item {
  animation: slideInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.details-section.expanded .score-item:nth-child(1) {
  animation-delay: 0.1s;
}

.details-section.expanded .score-item:nth-child(2) {
  animation-delay: 0.2s;
}

.details-section.expanded .score-item:nth-child(3) {
  animation-delay: 0.3s;
}

/* Slide in animation for details */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness for toggle button */
@media (max-width: 768px) {
  .toggle-details-btn {
    padding: 10px 16px;
    font-size: 0.85rem;
  }
  
  .details-section.expanded {
    max-height: 1500px;
  }
}

/* Add subtle background for details section when expanded */
.details-section.expanded {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
  border-radius: 12px;
  padding: 20px;
  margin-top: 10px;
}

/* Enhanced hover effects for collapsed state */
.details-section.collapsed {
  pointer-events: none;
}

.details-section.expanded {
  pointer-events: auto;
}

/* Animation for score display */
@keyframes scoreCountUp {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.score-display {
  animation: scoreCountUp 0.6s ease-out;
}

.score-item {
  animation: scoreCountUp 0.6s ease-out;
  animation-delay: calc(var(--delay, 0) * 0.1s);
}

/* Add a subtle animation for score reveal */
@keyframes scoreReveal {
  from {
    transform: scale(0.8) rotate(-10deg);
    opacity: 0;
  }
  to {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.score-display {
  animation: scoreReveal 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
}