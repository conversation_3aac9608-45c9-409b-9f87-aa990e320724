.message {
  max-width: 90%;
  margin-bottom: 20px;
  padding: 14px 18px;
  border-radius: 18px;
  position: relative;
  line-height: 1.5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.bot {
  align-self: flex-start;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-bottom-left-radius: 4px;
  padding: 16px 18px;
}

.message.user {
  align-self: flex-end;
  background-color: #38b2ac;
  /* color: white; */
  border-bottom-right-radius: 4px;
}

.message-timestamp {
  position: absolute;
  bottom: -18px;
  font-size: 0.8em;
  color: #4a5568;
  right: 10px;
  font-weight: 500;
}

/* Bot content styles */
.bot-content {
  overflow-x: auto;
}

.bot-content pre {
  background-color: #f8fafc;
  border-radius: 6px;
  padding: 12px;
  margin: 12px 0;
  border: 1px solid #e2e8f0;
}

.bot-content code {
  font-family: 'Fira Code', 'Courier New', monospace;
  font-size: 0.9em;
}

.bot-content img {
  max-width: 100%;
  border-radius: 6px;
  margin: 10px 0;
}

.bot-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
  border-radius: 6px;
  overflow: hidden;
}

.bot-content th,
.bot-content td {
  border: 1px solid #e2e8f0;
  padding: 10px;
}

.bot-content th {
  background-color: #f8fafc;
}

/* Typing Indicator Styles */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
}

.typing-dots {
  display: flex;
  align-items: center;
}

.typing-dots span {
  height: 8px;
  width: 8px;
  margin: 0 2px;
  background-color: #38b2ac;
  border-radius: 50%;
  display: inline-block;
  opacity: 0.7;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
  animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-dot {
  0%,
  80%,
  100% {
    transform: scale(0.7);
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .message {
    max-width: 90%;
    padding: 12px 14px;
  }

  .message-toolbar {
    opacity: 0.8; /* Always show toolbar buttons on mobile */
  }

  .toolbar-btn {
    min-width: 36px;
    min-height: 36px;
    padding: 8px;
  }
  
  .typing-dots span {
    height: 7px;
    width: 7px;
  }
  
  .voice-message-audio {
    max-width: 100%;
  }
  
  /* Improve code display on small screens */
  .bot-content pre {
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 8px;
  }
  
  .bot-content code {
    font-size: 0.85em;
  }
}

/* Voice Message Styles - WeChat style */
.voice-message {
  display: flex;
  align-items: center;
  min-width: 120px;
  max-width: 200px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.voice-message:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.voice-play-button {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  background: none;
  border: none;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  position: relative;
  outline: none;
}

.voice-play-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

.voice-play-button:active {
  transform: scale(0.98);
}

.voice-play-button.playing {
  background: rgba(56, 178, 172, 0.1);
}

.voice-duration {
  font-size: 15px;
  font-weight: 600;
  color: inherit;
  margin-right: 8px;
  min-width: 30px;
  text-align: left;
}

.sound-waves {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-left: auto;
  position: relative;
}

.sound-waves::before {
  content: "▶";
  font-size: 12px;
  color: #38b2ac;
  margin-right: 6px;
  transition: all 0.2s ease;
}

.voice-play-button.playing .sound-waves::before {
  content: "⏸";
}

.wave {
  width: 3px;
  background: linear-gradient(to top, #38b2ac, #4fd1c7);
  border-radius: 2px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.wave-1 {
  height: 12px;
  animation: wave-animation 1.5s ease-in-out infinite;
}

.wave-2 {
  height: 18px;
  animation: wave-animation 1.5s ease-in-out infinite 0.2s;
}

.wave-3 {
  height: 24px;
  animation: wave-animation 1.5s ease-in-out infinite 0.4s;
}

@keyframes wave-animation {
  0%, 100% {
    transform: scaleY(0.3);
    opacity: 0.4;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Pause animation when not playing */
.voice-play-button:not(.playing) .wave {
  animation-play-state: paused;
}

/* User voice messages (right side) */
.message.user .voice-message {
  background: linear-gradient(135deg, #38b2ac 0%, #2c7a7b 100%);
  color: white;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(56, 178, 172, 0.3);
}

.message.user .voice-play-button {
  color: white;
}

.message.user .voice-play-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.message.user .sound-waves::before {
  color: white;
}

.message.user .wave {
  background: linear-gradient(to top, #ffffff, #e6fffa);
}

/* Bot voice messages (left side) */
.message.bot .voice-message {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  color: #2d3748;
  border: 1px solid #e2e8f0;
  border-radius: 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.message.bot .voice-play-button {
  color: #2d3748;
}

.message.bot .voice-play-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.message.bot .sound-waves::before {
  color: #38b2ac;
}

.message.bot .wave {
  background: linear-gradient(to top, #38b2ac, #4fd1c7);
}

/* Playing state */
.voice-play-button.playing .sound-waves .wave {
  animation-play-state: running;
}

/* System message divider styles */
.system-message-divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  width: 100%;
}

.divider-line {
  flex: 1;
  height: 1px;
  background-color: #e2e8f0;
}

.divider-text {
  padding: 0 16px;
  font-size: 0.875rem;
  color: #718096;
  font-style: italic;
  white-space: nowrap;
}