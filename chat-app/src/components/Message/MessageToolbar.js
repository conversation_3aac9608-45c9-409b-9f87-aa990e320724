import React from "react";
import "./MessageToolbar.css";
import { ChatMessageSenderType } from "../../types/ChatMessageType";

const MessageToolbar = ({
  sender,
  onTTS,
  onGrammar,
  onTranslation,
  onDelete,
  onResend,
  onASR,
  onPronunciation,
  isLastMessage,
  message,
  isVoiceMessage,
}) => {
  // Helper to get pronunciation score from message if available
  const getPronunciationScore = () => {
    // You would need to pass the pronunciation result through props
    // or store it in the message object to access it here
    return null; // Placeholder - implement based on your data flow
  };

  const getScoreIndicator = (score) => {
    if (!score) return "📊";
    if (score >= 80) return "📊🟢";
    if (score >= 60) return "📊🟡";
    return "📊🔴";
  };

  const audioRef = React.useRef(null);

  // console.log("MessageToolbar", { sender, isVoiceMessage, message });
  return (
    <div className="message-toolbar">
      {/* Only show TTS button for non-voice messages since voice messages have their own play button */}
      {!isVoiceMessage && (
        <button className="toolbar-btn" title="Text to Speech" onClick={onTTS}>
          🔊
        </button>
      )}

      {isVoiceMessage && (
        <button
          className="toolbar-btn"
          title="Transcribe Voice Message"
          onClick={onASR}
        >
          🎯
        </button>
      )}

      {isVoiceMessage &&
        sender === ChatMessageSenderType.USER &&
        onPronunciation && (
          <button
            className="toolbar-btn"
            title="Pronunciation Assessment"
            onClick={onPronunciation}
          >
            {getScoreIndicator(getPronunciationScore())}
          </button>
        )}

      {sender === ChatMessageSenderType.USER && onGrammar && (
        <button
          className="toolbar-btn"
          title="Grammar Correction"
          onClick={onGrammar}
        >
          ✏️
        </button>
      )}

      {!isVoiceMessage && (
        <button
          className="toolbar-btn"
          title="Translate"
          onClick={onTranslation}
        >
          🌐
        </button>
      )}

      {sender === ChatMessageSenderType.USER && isLastMessage && onResend && (
        <button
          className="toolbar-btn resend-btn"
          title="Resend Message"
          onClick={onResend}
        >
          🔄
        </button>
      )}

      <button
        className="toolbar-btn delete-btn"
        title="Delete Message"
        onClick={onDelete}
      >
        🗑️
      </button>
    </div>
  );
};

export default MessageToolbar;
