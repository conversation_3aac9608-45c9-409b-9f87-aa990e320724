import React, { useState, useRef, useEffect } from "react";
import "./BotList.css";

const BotList = ({
  bots,
  currentBotId,
  onSelectBot,
  onAddBot,
  onEditBot,
  onDeleteBot,
  onApiKeySettings,
}) => {
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    botId: null,
  });
  const contextMenuRef = useRef(null);

  useEffect(() => {
    // Close context menu when clicking outside
    const handleClickOutside = (event) => {
      if (
        contextMenuRef.current &&
        !contextMenuRef.current.contains(event.target)
      ) {
        setContextMenu({ ...contextMenu, visible: false });
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [contextMenu]);

  const handleContextMenu = (e, botId) => {
    e.preventDefault();
    // Show context menu at mouse position
    setContextMenu({
      visible: true,
      x: e.pageX,
      y: e.pageY,
      botId,
    });
  };

  const handleEditBot = () => {
    onEditBot(bots.find((b) => b.id === contextMenu.botId));
    setContextMenu({ ...contextMenu, visible: false });
  };

  const handleDeleteBot = () => {
    const bot = bots.find((b) => b.id === contextMenu.botId);
    if (bot) {
      onDeleteBot(contextMenu.botId);
    }
    setContextMenu({ ...contextMenu, visible: false });
  };

  return (
    <div className="bot-panel">
      <div className="bot-list">
        {bots.map((bot) => (
          <div
            key={bot.id}
            className={`bot-item ${bot.id === currentBotId ? "active" : ""}`}
            onClick={() => onSelectBot(bot.id)}
            onContextMenu={(e) => handleContextMenu(e, bot.id)}
          >
            <div className="bot-avatar">
              {bot.image_url ? (
                <img
                  src={bot.image_url}
                  alt={bot.name}
                  className="bot-avatar-image"
                />
              ) : (
                bot.name.charAt(0)
              )}
            </div>
            <div className="bot-info">
              <div className="bot-name">
                {bot.name}
                <span className={`bot-type-badge ${bot.bot_type || "normal"}`}>
                  {bot.bot_type === "mission" ? "🎯" : "💬"}
                </span>
                {bot.bot_type === "mission" &&
                  bot.hasMissions &&
                  bot.hasMissions() && (
                    <span
                      className="missions-badge"
                      title="Has missions defined"
                    >
                      ✅
                    </span>
                  )}
              </div>
              {bot.hasTitle && bot.hasTitle() && (
                <div className="bot-title">{bot.getTitle()}</div>
              )}
              <div className="bot-description">
                {bot.bot_type === "mission" &&
                bot.getMissions &&
                bot.getMissions()
                  ? `Missions: ${bot.getMissions().substring(0, 25)}${
                      bot.getMissions().length > 25 ? "..." : ""
                    }`
                  : `${bot.persona.substring(0, 30)}${
                      bot.persona.length > 30 ? "..." : ""
                    }`}
              </div>
            </div>
            <button
              className="bot-delete-btn"
              title="Delete bot"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteBot(bot.id);
              }}
            >
              ×
            </button>
          </div>
        ))}
      </div>

      {contextMenu.visible && (
        <div
          ref={contextMenuRef}
          className="bot-context-menu"
          style={{ top: contextMenu.y, left: contextMenu.x }}
        >
          <div className="context-menu-item" onClick={handleEditBot}>
            ✏️ Edit Bot
          </div>
          <div className="context-menu-item delete" onClick={handleDeleteBot}>
            🗑️ Delete Bot
          </div>
        </div>
      )}

      <div className="add-bot-btn" onClick={onAddBot}>
        + Add New Bot
      </div>
      <div className="add-bot-btn" onClick={onApiKeySettings}>
        ⚙️ API Key Settings
      </div>
    </div>
  );
};

export default BotList;
