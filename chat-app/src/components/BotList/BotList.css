.bot-panel {
  width: 25%;
  background-color: #2d3748;
  display: flex;
  flex-direction: column;
  color: white;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.bot-list {
  flex-grow: 1;
  overflow-y: auto;
  padding: 15px 10px;
}

.bot-item {
  display: flex;
  align-items: center;
  padding: 14px;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 10px;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  position: relative;
}

.bot-item:hover {
  background-color: #3a4556;
  transform: translateX(2px);
}

.bot-item.active {
  background-color: #3a4556;
  border-left: 3px solid #38b2ac;
}

.bot-avatar {
  width: 42px;
  height: 42px;
  background-color: #38b2ac;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-weight: bold;
  font-size: 18px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.bot-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.bot-info {
  flex-grow: 1;
}

.bot-name {
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 0.95rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.bot-title {
  font-size: 0.85rem;
  color: #a0aec0;
  margin-bottom: 4px;
  font-style: italic;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bot-type-badge {
  font-size: 0.7rem;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.bot-type-badge.normal {
  color: #4299e1;
}

.bot-type-badge.mission {
  color: #f56565;
}

.missions-badge {
  font-size: 0.7rem;
  margin-left: 4px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.bot-description {
  font-size: 0.8em;
  color: #cbd5e0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.add-bot-btn {
  padding: 16px;
  text-align: center;
  background-color: #1a202c;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
}

.add-bot-btn:hover {
  background-color: #2d3748;
}

.bot-delete-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #718096;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 2px 8px;
  border-radius: 4px;
}

.bot-delete-btn:hover {
  background-color: #e53e3e;
  color: white;
}

.bot-item:hover .bot-delete-btn {
  opacity: 1;
}

/* Context Menu Styles */
.bot-context-menu {
  position: fixed;
  background-color: #2d3748;
  border: 1px solid #4a5568;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  z-index: 1000;
  overflow: hidden;
  min-width: 160px;
}

.context-menu-item {
  padding: 10px 15px;
  cursor: pointer;
  color: white;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.context-menu-item:hover {
  background-color: #3a4556;
}

.context-menu-item.delete {
  color: #fc8181;
}

.context-menu-item.delete:hover {
  background-color: rgba(229, 62, 62, 0.2);
}

/* Enhanced mobile responsive for bot list */
@media (max-width: 768px) {
  .bot-panel {
    width: 100%;
    height: 100%;
  }

  .bot-list {
    padding: 12px 8px;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  .bot-item {
    padding: 16px 12px;
    margin-bottom: 8px;
    border-radius: 12px;
    /* Improve touch targets */
    min-height: 64px;
  }

  .bot-avatar {
    width: 48px;
    height: 48px;
    margin-right: 14px;
    border-radius: 12px;
  }

  .bot-name {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 4px;
  }

  .bot-description {
    font-size: 0.85rem;
    line-height: 1.3;
  }

  .add-bot-btn {
    padding: 18px 16px;
    font-size: 1rem;
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }
}

/* For extra small phones */
@media (max-width: 480px) {
  .bot-list {
    padding: 10px 6px;
  }

  .bot-item {
    padding: 14px 10px;
    min-height: 60px;
  }

  .bot-avatar {
    width: 44px;
    height: 44px;
    margin-right: 12px;
  }

  .bot-name {
    font-size: 0.95rem;
  }

  .bot-description {
    font-size: 0.8rem;
  }

  .add-bot-btn {
    padding: 16px 14px;
    font-size: 0.95rem;
    min-height: 52px;
  }
}