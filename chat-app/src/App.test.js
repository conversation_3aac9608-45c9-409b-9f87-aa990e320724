import { render, screen } from '@testing-library/react';
import App from './App';

// Mock the hooks and services
jest.mock('./hooks/useUrlParams', () => ({
  useUrlParams: () => ({
    getApiKeyFromUrl: () => null
  })
}));

jest.mock('./hooks/useChatState', () => ({
  useChatState: () => ({
    bots: [],
    setBots: jest.fn(),
    currentBotId: null,
    setCurrentBotId: jest.fn(),
    chats: {},
    setChats: jest.fn(),
    currentChat: null,
    setCurrentChat: jest.fn(),
    autoTTSEnabled: false,
    chatSuggestionsEnabled: false,
    apiKey: null,
    setApiKey: jest.fn(),
    clearChatHistory: jest.fn(),
    sendMessage: jest.fn(),
    resendMessage: jest.fn(),
    deleteMessage: jest.fn(),
    toggleAutoTTS: jest.fn(),
    toggleChatSuggestions: jest.fn(),
    lastMessage: null,
    mission: null,
    beforeMission: null,
  })
}));

jest.mock('./services/apiServices', () => ({
  fetchChatbots: jest.fn(),
  saveChatbot: jest.fn(),
  deleteChatbot: jest.fn(),
}));

// Mock navigator.permissions
Object.defineProperty(navigator, 'permissions', {
  value: {
    query: jest.fn().mockResolvedValue({ state: 'granted' })
  },
  writable: true
});

test('renders chat bots header', () => {
  render(<App />);
  const headerElement = screen.getByText(/🤖 Chat Bots/i);
  expect(headerElement).toBeInTheDocument();
});

test('renders app container', () => {
  render(<App />);
  const appElement = screen.getByRole('main') || document.querySelector('.app');
  expect(appElement).toBeInTheDocument();
});
