import { WS_API_BASE_URL, API_BASE_URL } from "../config";
import { getWebSocketUrl, getApiHeaders } from "../utils/userAuth";

export const DEFAULT_VOICE = "en_female_amanda_mars_bigtts"; // Default voice if none provided
class TTSStreamPlayer {
  constructor(voice = DEFAULT_VOICE, apiKey = null, voiceSpeed = 1.0) {
    this.VOICE = voice || DEFAULT_VOICE;
    this.VOICE_SPEED = voiceSpeed || 1.0; // Add voice speed property
    this.apiKey = apiKey;
    this.audioChunks = [];
    this.audioChunksCount = 0;
    this.audioQueue = [];
    this.isPlaying = false;
    this.isWaitingForAudioEnd = false;
    this.bufferingTimeout = null;
    this.currentAudio = null;
    this.currentWebSocket = null;
    this.onLoadingChange = null;
    this.onPlaybackComplete = null;
    this.onSaveAudioChunks = null;

    // New properties for incremental streaming
    this.isIncrementalMode = false;
    this.incrementalBuffer = "";
    this.lastSentLength = 0;

    // Constants
    this.BUFFER_TIMEOUT_MS = 500;
    this.MIN_CHUNKS = 10;
  }

  setVoice(voice) {
    this.VOICE = voice || DEFAULT_VOICE;
  }

  // Add method to set voice speed
  setVoiceSpeed(voiceSpeed) {
    this.VOICE_SPEED = voiceSpeed || 1.0;
  }
  /**
   * Set API key for upload operations
   */
  setApiKey(apiKey) {
    this.apiKey = apiKey;
  }

  /**
   * Play TTS for the given text
   * @param {string} text - Text to convert to speech
   * @param {Function} onLoadingChange - Callback for loading state changes
   * @param {Function} onPlaybackComplete - Callback when playback is complete
   */
  playTTS(messageId, text, onLoadingChange, onPlaybackComplete) {
    // If already playing, stop current playback
    if (this.isPlaying || this.currentWebSocket) {
      this.stop();
      return;
    }

    this.onLoadingChange = onLoadingChange;
    this.onPlaybackComplete = onPlaybackComplete;

    // Set initial state
    this.isWaitingForAudioEnd = true;
    this.onLoadingChange?.(true);

    this._streamTTS(messageId, text);
  }

  /**
   * Start incremental TTS streaming for bot messages as they arrive
   * @param {Function} onLoadingChange - Callback for loading state changes
   * @param {Function} onPlaybackComplete - Callback when playback is complete
   */
  startIncrementalTTS(onLoadingChange, onPlaybackComplete) {
    // If already in incremental mode or playing, stop first
    if (this.isIncrementalMode || this.isPlaying || this.currentWebSocket) {
      this.stop();
    }

    this.onLoadingChange = onLoadingChange;
    this.onPlaybackComplete = onPlaybackComplete;
    this.isIncrementalMode = true;
    this.incrementalBuffer = "";
    this.lastSentLength = 0;
    this.isWaitingForAudioEnd = true;
    this.onLoadingChange?.(true);

    this._initIncrementalWebSocket();
  }

  /**
   * Send incremental text to TTS stream
   * @param {string} newText - The accumulated text so far
   */
  sendIncrementalText(newText) {
    if (!this.isIncrementalMode || !this.currentWebSocket) {
      return;
    }

    // Only send the new portion of text
    if (newText.length > this.lastSentLength) {
      const newPortion = newText.substring(this.lastSentLength);
      this.incrementalBuffer += newPortion;
      this.lastSentLength = newText.length;

      // Send new text chunk
      if (this.currentWebSocket.readyState === WebSocket.OPEN) {
        this.currentWebSocket.send(newPortion);
      }
    }
  }

  /**
   * Complete the incremental TTS streaming
   */
  completeIncrementalTTS() {
    if (!this.isIncrementalMode || !this.currentWebSocket) {
      return;
    }

    // Send end marker
    if (this.currentWebSocket.readyState === WebSocket.OPEN) {
      this.currentWebSocket.send("<|END|>");
    }
  }

  /**
   * Initialize WebSocket for incremental streaming
   * @private
   */
  _initIncrementalWebSocket() {
    try {
      const ws = new WebSocket(getWebSocketUrl("/api/tts_stream"));
      this.currentWebSocket = ws;

      ws.onopen = () => {
        // Send voice configuration
        ws.send(
          JSON.stringify({
            voice: this.VOICE,
            mode: "incremental",
            voiceSpeed: this.VOICE_SPEED,
          })
        );
      };

      ws.onmessage = (event) => {
        if (event.data instanceof Blob) {
          // Buffer the audio chunk
          this.audioChunks.push(event.data);
          this.audioChunksCount++;
          // console.log("recv trunk: ", this.audioChunksCount);
          this.audioQueue.push(event.data);
          this._manageBuffer();
        } else if (event.data.startsWith("{")) {
          // Handle JSON status messages
          try {
            const statusData = JSON.parse(event.data);
            if (statusData.status === "completed") {
              // Streaming is complete

              this.isIncrementalMode = false;
              ws.close();
              this.currentWebSocket = null;
            }
          } catch (e) {
            console.error("TTS incremental WS status error:", e);
            this._handleIncrementalError();
          }
        }
      };

      ws.onerror = (err) => {
        console.error("TTS incremental streaming error:", err);
        this._handleIncrementalError();
      };

      ws.onclose = () => {
        console.log("TTS incremental WebSocket closed");
        this.currentWebSocket = null;
        this.isIncrementalMode = false;
      };
    } catch (e) {
      console.error("TTS incremental WS setup failed:", e);
      this._handleIncrementalError();
    }
  }

  /**
   * Handle errors in incremental mode
   * @private
   */
  _handleIncrementalError() {
    this.isIncrementalMode = false;
    this.onLoadingChange?.(false);
    this.isWaitingForAudioEnd = false;
    this.currentWebSocket = null;
  }

  /**
   * Stop current playback and clean up resources
   */
  stop() {
    // Stop current audio if playing
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }

    // Close WebSocket if open
    if (
      this.currentWebSocket &&
      this.currentWebSocket.readyState === WebSocket.OPEN
    ) {
      this.currentWebSocket.close();
      this.currentWebSocket = null;
    }

    // Clear buffering timeout
    if (this.bufferingTimeout) {
      clearTimeout(this.bufferingTimeout);
      this.bufferingTimeout = null;
    }

    // Reset all state
    this.isPlaying = false;
    this.isWaitingForAudioEnd = false;
    this.isIncrementalMode = false;
    this.incrementalBuffer = "";
    this.lastSentLength = 0;
    this.audioQueue = [];
    this.audioChunks = [];
    this.audioChunksCount = 0;

    // Reset UI state
    this.onLoadingChange?.(false);
  }

  /**
   * Play audio from URL
   * @private
   */
  _playFromUrl(audioUrl) {
    try {
      const audio = new Audio(audioUrl);
      this.currentAudio = audio;

      audio.onended = () => {
        this.onLoadingChange?.(false);
        this.isWaitingForAudioEnd = false;
        this.currentAudio = null;
        this.onPlaybackComplete?.();
      };

      audio.onerror = () => {
        this.onLoadingChange?.(false);
        this.isWaitingForAudioEnd = false;
        this.currentAudio = null;
      };

      audio.play().catch((err) => {
        console.error("Failed to play audio from URL:", err);
        this.onLoadingChange?.(false);
        this.isWaitingForAudioEnd = false;
        this.currentAudio = null;
      });
    } catch (error) {
      console.error("Audio URL playback error:", error);
      this.onLoadingChange?.(false);
      this.isWaitingForAudioEnd = false;
    }
  }

  /**
   * Stream TTS from the API
   * @private
   */
  _streamTTS(messageId, text) {
    try {
      const ws = new WebSocket(
        getWebSocketUrl("/api/tts_stream", null, { message_id: messageId })
      );
      this.currentWebSocket = ws;

      ws.onopen = () => {
        ws.send(
          JSON.stringify({ voice: this.VOICE, voiceSpeed: this.VOICE_SPEED })
        );
        ws.send(text);
        ws.send("<|END|>");
      };

      ws.onmessage = (event) => {
        if (event.data instanceof Blob) {
          // Buffer the audio chunk
          this.audioChunks.push(event.data);
          this.audioChunksCount++;
          this.audioQueue.push(event.data);
          this._manageBuffer();
        } else if (event.data.startsWith("{")) {
          // Handle JSON status messages
          try {
            const statusData = JSON.parse(event.data);
            if (statusData.status === "completed") {
              // Clean up for next time
              this.audioChunks = [];
              ws.close();
              this.currentWebSocket = null;
            }
          } catch (e) {
            console.error("TTS WS status error:", e);
            this.onLoadingChange?.(false);
            this.isWaitingForAudioEnd = false;
            this.currentWebSocket = null;
          }
        }
      };

      ws.onerror = (err) => {
        console.error("TTS streaming error:", err);
        this.onLoadingChange?.(false);
        this.isWaitingForAudioEnd = false;
        this.currentWebSocket = null;
      };

      ws.onclose = () => {
        console.log("TTS WebSocket closed");
        this.currentWebSocket = null;
      };
    } catch (e) {
      console.error("TTS WS setup failed:", e);
      this.onLoadingChange?.(false);
      this.isWaitingForAudioEnd = false;
    }
  }

  /**
   * Schedule or immediately play based on chunk count or timeout
   * @private
   */
  _manageBuffer() {
    if (this.bufferingTimeout) {
      clearTimeout(this.bufferingTimeout);
    }
    if (this.isPlaying) return;

    if (this.audioQueue.length >= this.MIN_CHUNKS) {
      this._playQueue();
    } else {
      this.bufferingTimeout = setTimeout(() => {
        if (!this.isPlaying && this.audioQueue.length > 0) {
          this._playQueue();
        }
      }, this.BUFFER_TIMEOUT_MS);
    }
  }

  /**
   * Play whatever is in the audio queue as one blob
   * @private
   */
  _playQueue() {
    if (this.audioQueue.length === 0) {
      this.isPlaying = false;
      this.currentAudio = null;

      // Check if we should end loading state when all audio is done
      if (this.isWaitingForAudioEnd && !this.isIncrementalMode) {
        this.onLoadingChange?.(false);
        this.isWaitingForAudioEnd = false;
        this.onPlaybackComplete?.();
      }
      return;
    }

    this.isPlaying = true;
    // Pull everything out
    const chunks = this.audioQueue.splice(0);
    const blob = new Blob(chunks, { type: "audio/mpeg" });
    const url = URL.createObjectURL(blob);
    const audio = new Audio(url);
    this.currentAudio = audio;

    audio.onended = () => {
      URL.revokeObjectURL(url);
      this.isPlaying = false;
      this.currentAudio = null;

      // If more buffered, keep going
      if (this.audioQueue.length > 0) {
        this._playQueue();
      } else {
        // No more audio to play, check if we should end loading state
        if (this.isWaitingForAudioEnd && !this.isIncrementalMode) {
          this.onLoadingChange?.(false);
          this.isWaitingForAudioEnd = false;
          this.onPlaybackComplete?.();
        }
        this.onSaveAudioChunks?.();
      }
    };

    audio.onerror = () => {
      URL.revokeObjectURL(url);
      this.isPlaying = false;
      this.currentAudio = null;

      if (this.audioQueue.length > 0) {
        this._playQueue();
      } else {
        // Error occurred, end loading state
        if (this.isWaitingForAudioEnd && !this.isIncrementalMode) {
          this.onLoadingChange?.(false);
          this.isWaitingForAudioEnd = false;
        }
      }
    };

    audio.play().catch(() => {
      this.isPlaying = false;
      this.currentAudio = null;

      // Play failed, end loading state
      if (this.isWaitingForAudioEnd && !this.isIncrementalMode) {
        this.onLoadingChange?.(false);
        this.isWaitingForAudioEnd = false;
      }
    });
  }

  /**
   * Play audio chunk directly from WebSocket chat
   * @param {Uint8Array} audioChunk - Audio chunk data
   */
  playAudioChunk(audioChunk) {
    try {
      // Convert to Blob and add to audio queue
      const audioBlob = new Blob([audioChunk], { type: "audio/mpeg" });
      this.audioQueue.push(audioBlob);
      this.audioChunks.push(audioBlob);
      this.audioChunksCount++;
      this._manageBuffer();
    } catch (error) {
      console.error("Error playing audio chunk:", error);
    }
  }

  finishPlayAudioTrunks(text, onSuccess = null) {
    // Save all audio chunks to cache as a single blob
    this.onSaveAudioChunks = () => {
      onSuccess?.();
      this.onSaveAudioChunks = null;

      // Clear audio chunks after saving
      this.audioChunks = [];
      this.audioChunksCount = 0;
      this.audioQueue = [];
      this.isPlaying = false;
      this.currentAudio = null;
      this.isWaitingForAudioEnd = false;
    };
  }
}

export default TTSStreamPlayer;
