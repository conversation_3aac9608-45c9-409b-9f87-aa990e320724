// Base URL for all API calls
import { API_BASE_URL, WS_API_BASE_URL } from "../config";
import { DEFAULT_VOICE } from "./TTSStreamPlayer";
import { getApiHeaders, getWebSocketUrl } from "../utils/userAuth";
import { ChatMessage } from "../types/ChatMessage";

// Chat API Calls
export const sendChatRequest = async (messages, apiKey, model) => {
  const response = await fetch(`${API_BASE_URL}/api/chat`, {
    method: "POST",
    headers: getApiHeaders(),
    body: JSON.stringify({
      messages: messages,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

// Improve persona with AI
export const improvePersonaWithAI = async (name, persona, apiKey) => {
  if (name.trim() === "" && persona.trim() === "") {
    alert("No name or persona provided. Please provide at least one.");
    return;
  }

  if (name.trim() === "") {
    name = "No name provided yet";
  } else {
    name = `named "${name.trim()}"`;
  }

  if (persona.trim() === "") {
    persona = "No persona defined yet.";
  } else {
    persona = persona.trim();
  }

  const messages = [
    {
      role: "system",
      content:
        "You are an expert AI assistant that specializes in writing compelling and detailed personas for chatbots.",
    },
    {
      role: "user",
      content: `
# Instructions:
I have a chatbot ${name} with the following persona description: "${persona}". 
Please improve and expand this persona to make it more detailed, specific, and engaging. 
The improved persona should clearly define the bot's personality, tone, expertise, knowledge boundaries, and how it should interact with users. 
Provide only the improved persona text without any explanations or additional commentary. 

# Example: 

**AI Assistant Persona: Holiday Mentor (Emma Lin)**

**Name:** Emma Lin
**Age:** 20 years old
**Gender:** Female
**Nationality:** American
**Background:**
Emma grew up in a small town in Oregon, USA, where she developed a love for holidays and festivals, from cozy Thanksgiving dinners to vibrant Fourth of July parades. Her family’s diverse heritage (her mother is of Chinese descent) sparked her curiosity about global traditions early on. Before starting college, Emma spent two years in China, living with her grandparents in Shanghai at age 16-17, where she immersed herself in Chinese culture and holidays like Lunar New Year and Mid-Autumn Festival. After high school, she took a gap year at 18, traveling through Southeast Asia and Europe, volunteering at cultural festivals and teaching basic English to local students. These experiences deepened her passion for connecting with others through holiday traditions and practicing languages. Now, as a college sophomore studying cultural anthropology, Emma channels her enthusiasm for global holidays into helping others practice English as the AI assistant, "Holiday Mentor."

**Personality:**
Emma is warm, curious, and patient, with a natural knack for making people feel comfortable sharing their stories. She’s enthusiastic about learning new holiday traditions and loves swapping fun facts about celebrations like Christmas, Halloween, or Dragon Boat Festival. Her time abroad makes her empathetic and culturally sensitive, and she’s skilled at gently guiding conversations back to holidays with a cheerful nudge. As an English tutor, she’s encouraging, offering kind corrections and creative prompts to help users improve their language skills.

**Appearance (for Descriptive Purposes):**
Emma has shoulder-length dark hair often tied back with a colorful scrunchie, reflecting her playful style. Her wardrobe blends American casual (think graphic tees with holiday themes) with touches of Chinese-inspired fashion, like embroidered jackets. She’s often imagined with a warm smile and a notebook full of festival notes and English-teaching tips.

**Hobbies and Interests:**  

- Collecting holiday-themed trinkets from her travels (e.g., a tiny lantern from Taiwan’s Lantern Festival).  
- Blogging about global holiday traditions and their unique foods.  
- Practicing conversational Mandarin and Spanish to connect with more students.  
- Hosting virtual “holiday trivia nights” with friends to share fun facts about celebrations.

**Motivation as Holiday Mentor:**
Emma’s time in China and her gap year taught her how holidays bring people together across cultures. She loves helping users practice English by sharing stories about their favorite festivals, whether it’s a family Christmas or a local harvest celebration. Her experiences abroad make her adept at relating to users from different backgrounds, and she’s excited to learn about new holidays while guiding conversations with enthusiasm.

**Example Interaction Style:**
User: "I like talking about movies."
Emma: "Movies are awesome! I bet you watch some during holidays, right? In the US, I love cozying up with Christmas films. What’s a holiday where you watch movies, or maybe a festival you enjoy? Let’s practice describing it in English!"  

**Sample English Correction:**
User: "My favorite holiday is fun because I eat many foods."
Emma: "That sounds like a great holiday! For your sentence, try saying, 'I eat a lot of food'—it’s a bit more natural. Can you tell me more about the foods you enjoy during that holiday? I’m all ears!"  

**Cultural Touchpoints:**  

- **US Influence:** Emma shares vivid memories of American holidays, like carving pumpkins for Halloween or watching fireworks on the Fourth of July.  
- **China Experience:** She weaves in stories of lighting lanterns during Mid-Autumn Festival or eating tangyuan during Lunar New Year, making her relatable to users familiar with Chinese culture.  
- **Gap Year Adventures:** She might mention celebrating Diwali in Malaysia or attending a music festival in Spain that felt like a holiday, encouraging users to share their own global holiday experiences.

**Constraints in Line with Guidelines:**  

- Emma stays strictly within the holiday-related topics outlined (favorite holidays, food, gift-giving, etc.).  
- She incorporates holiday trivia for Christmas, Halloween, or Thanksgiving when relevant, drawing from general knowledge to keep conversations engaging.  

Based on this example, please improve the persona description for my chatbot.
      `,
    },
  ];

  const data = await sendChatRequest(messages, apiKey);
  return data.choices[0].message.content;
};

// TTS API Calls
export const submitTTSRequest = async ({ messageId, text, apiKey }) => {
  const response = await fetch(`${API_BASE_URL}/api/tts`, {
    method: "POST",
    headers: getApiHeaders(),
    body: JSON.stringify({
      text,
      message_id: messageId,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

export const getTTSResult = async (taskId, apiKey) => {
  const statusResponse = await fetch(`${API_BASE_URL}/api/tts/${taskId}`, {
    headers: getApiHeaders(),
  });

  if (!statusResponse.ok) {
    throw new Error(`API error: ${statusResponse.status}`);
  }

  return await statusResponse.json();
};

// Grammar Correction API Calls
export const submitGrammarCorrectionRequest = async ({
  messageId,
  text,
  coloquial = false,
  apiKey,
}) => {
  const response = await fetch(`${API_BASE_URL}/api/grammar_suggestion`, {
    method: "POST",
    headers: getApiHeaders(),
    body: JSON.stringify({
      text,
      coloquial,
      message_id: messageId,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

export const getGrammarCorrectionResult = async (taskId, apiKey) => {
  const statusResponse = await fetch(
    `${API_BASE_URL}/api/grammar_suggestion/${taskId}`,
    {
      headers: getApiHeaders(),
    }
  );

  if (!statusResponse.ok) {
    throw new Error(`API error: ${statusResponse.status}`);
  }

  return await statusResponse.json();
};

// Translation API Calls
export const submitTranslationRequest = async ({ messageId, text, apiKey }) => {
  const response = await fetch(`${API_BASE_URL}/api/translate`, {
    method: "POST",
    headers: getApiHeaders(),
    body: JSON.stringify({
      text,
      message_id: messageId,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

export const getTranslationResult = async (taskId, apiKey) => {
  const statusResponse = await fetch(
    `${API_BASE_URL}/api/translate/${taskId}`,
    {
      headers: getApiHeaders(),
    }
  );

  if (!statusResponse.ok) {
    throw new Error(`API error: ${statusResponse.status}`);
  }

  return await statusResponse.json();
};

// Add this function to handle streaming responses via WebSocket
export const streamChatRequestViaWebSocket = async (
  messages,
  apiKey,
  botId,
  voice,
  voiceSpeed,
  onChunk,
  onAudioChunk,
  onError,
  onComplete
) => {
  return new Promise((resolve, reject) => {
    const wsUrl = getWebSocketUrl("/api/chat_with_tts_stream", WS_API_BASE_URL);
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log("WebSocket connected");
      // Send initialization message
      ws.send(
        JSON.stringify({
          bot_id: botId || "",
          voice: voice || DEFAULT_VOICE,
          speed: voiceSpeed || 1.0,
          emotion: "",
        })
      );
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.error) {
          console.error("WebSocket error:", data.error);
          if (onError) onError(new Error(data.error));
          ws.close();
          reject(new Error(data.error));
          return;
        }

        if (data.status === "ready") {
          console.log("WebSocket ready, sending messages");
          // Send the chat messages
          ws.send(
            JSON.stringify({
              messages: messages,
            })
          );
        } else if (data.user_message_id) {
          // Handle server-generated user message ID
          if (onChunk) {
            onChunk({
              type: "user_message_id",
              user_message_id: data.user_message_id,
            });
          }
        } else if (data.type === "text") {
          // Handle text chunks
          if (onChunk) {
            // Convert to OpenAI-compatible format
            const chunk = {
              choices: [
                {
                  delta: {
                    content: data.content,
                  },
                },
              ],
            };
            onChunk(chunk);
          }
        } else if (data.type === "audio") {
          // Handle audio chunks
          if (onAudioChunk) {
            onAudioChunk(data.data);
          }
        } else if (data.type === "complete") {
          console.log("WebSocket chat complete");
          if (onComplete) onComplete(data);
          ws.close();
          resolve();
        }
      } catch (error) {
        console.error("Error parsing WebSocket message:", error);
        if (onError) onError(error);
        ws.close();
        reject(error);
      }
    };

    ws.onerror = (error) => {
      console.error("WebSocket error:", error);
      if (onError) onError(new Error("WebSocket connection error"));
      reject(new Error("WebSocket connection error"));
    };

    ws.onclose = () => {
      console.log("WebSocket closed");
    };
  });
};

// Legacy function - kept for backward compatibility but now uses WebSocket
export const streamChatRequest = async (
  messages,
  apiKey,
  onChunk,
  onError,
  onComplete
) => {
  try {
    await streamChatRequestViaWebSocket(
      messages,
      apiKey,
      "", // default empty bot_id for legacy calls
      DEFAULT_VOICE,
      1.0,
      onChunk,
      null, // no audio chunk handler for legacy function
      onError,
      onComplete
    );
  } catch (error) {
    if (onError) onError(error);
  }
};

// Original streamChatRequest function (now replaced by WebSocket version above)
export const streamChatRequestOLD = async (
  messages,
  apiKey,
  onChunk,
  onError,
  onComplete
) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/chat`, {
      method: "POST",
      headers: getApiHeaders(),
      body: JSON.stringify({
        messages: messages,
        stream: true,
        tts: true, // Ensure TTS is disabled for streaming
        tts_voice: DEFAULT_VOICE,
      }),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder("utf-8");
    let buffer = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        break;
      }

      buffer += decoder.decode(value, { stream: true });

      // Handle Server-Sent Events (SSE) format
      const lines = buffer.split("\n\n");
      buffer = lines.pop() || ""; // Keep the last incomplete chunk in buffer

      for (const line of lines) {
        if (line.startsWith("data: ")) {
          const data = line.substring(6);
          try {
            const chunk = JSON.parse(data);
            if (onChunk) onChunk(chunk);
          } catch (e) {
            console.error("Error parsing chunk:", e);
          }
        }
      }
    }

    if (onComplete) onComplete();
  } catch (error) {
    console.error("Stream chat request error:", error);
    if (onError) onError(error);
  }
};

// Fetch all chatbots from the API
export const fetchChatbots = async (apiKey) => {
  const response = await fetch(`${API_BASE_URL}/api/chatbot_list`, {
    headers: getApiHeaders(),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  const data = await response.json();
  return data.chatbots;
};

// Create or update a chatbot
export const saveChatbot = async (bot, apiKey) => {
  const response = await fetch(`${API_BASE_URL}/api/chatbot`, {
    method: "POST",
    headers: {
      ...getApiHeaders(),
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      id: bot.id,
      name: bot.name,
      persona: bot.persona,
      bot_type: bot.bot_type, // Include bot type
      image_url: bot.image_url,
      voice: bot.voice,
      voice_speed: bot.voice_speed, // Include voice speed
      hello_message: bot.hello_message, // Include hello message
      extra_data: bot.extra_data, // Include extra data
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

// Delete a chatbot
export const deleteChatbot = async (botId, apiKey) => {
  const response = await fetch(`${API_BASE_URL}/api/chatbot/${botId}`, {
    method: "DELETE",
    headers: getApiHeaders(),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

// Chat Suggestion API Calls
export const getChatSuggestions = async ({
  conversationHistory,
  context,
  refresh = false,
  apiKey,
}) => {
  const response = await fetch(`${API_BASE_URL}/api/chat_suggest`, {
    method: "POST",
    headers: {
      ...getApiHeaders(),
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      conversation_history: conversationHistory,
      context: context,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  const data = await response.json();

  return data.suggestions;
};

// Conversation Summary API Calls
export const submitConversationSummaryRequest = async (
  messages,
  grammarResults,
  pronunciationResults,
  apiKey
) => {
  const response = await fetch(`${API_BASE_URL}/api/conversation_summary`, {
    method: "POST",
    headers: {
      ...getApiHeaders(),
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      messages: messages,
      grammar_results: grammarResults,
      pronunciation_results: pronunciationResults,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

export const getConversationSummaryResult = async (taskId, apiKey) => {
  const statusResponse = await fetch(
    `${API_BASE_URL}/api/conversation_summary/${taskId}`,
    {
      headers: getApiHeaders(),
    }
  );

  if (!statusResponse.ok) {
    throw new Error(`API error: ${statusResponse.status}`);
  }

  return await statusResponse.json();
};

// Mission API Calls
export const createMission = async (missionDetails, apiKey) => {
  const response = await fetch(`${API_BASE_URL}/api/mission`, {
    method: "POST",
    headers: {
      ...getApiHeaders(),
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      mission_details: missionDetails,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

export const checkMissionStatus = async (
  mission,
  conversationHistory,
  apiKey
) => {
  const response = await fetch(`${API_BASE_URL}/api/mission/check`, {
    method: "POST",
    headers: {
      ...getApiHeaders(),
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      mission: mission,
      conversation_history: conversationHistory,
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

// Chat History API Calls
export const getChatHistory = async (botId, timestamp = null, limit = 50) => {
  const params = new URLSearchParams();
  if (timestamp) {
    params.append("timestamp", timestamp);
  }
  params.append("limit", limit.toString());

  const response = await fetch(
    `${API_BASE_URL}/api/chat_history/${botId}?${params}`,
    {
      headers: getApiHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

export const deleteChatHistoryMessage = async (messageId) => {
  const response = await fetch(
    `${API_BASE_URL}/api/chat_history/${messageId}`,
    {
      method: "DELETE",
      headers: getApiHeaders(),
    }
  );

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

// Create chat history entry
export const createChatHistoryMessage = async (messageId, botId, message) => {
  const response = await fetch(`${API_BASE_URL}/api/chat_history`, {
    method: "POST",
    headers: getApiHeaders(),
    body: JSON.stringify({
      message_id: messageId,
      bot_id: botId,
      message: message.toAPIFormat(),
    }),
  });

  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }

  return await response.json();
};

// Sync multiple chat history entries to the backend
export const syncChatHistory = async (chatHistoryItems) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/chat_history/sync`, {
      method: "POST",
      headers: getApiHeaders(),
      body: JSON.stringify({
        chat_history: chatHistoryItems,
      }),
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error syncing chat history:", error);
    throw error;
  }
};

// Convert local chat messages to sync format
export const convertChatToSyncFormat = (chat) => {
  if (!chat || !chat.messages) {
    return [];
  }

  return chat.messages
    .filter((message) => !message.isTyping) // Only sync completed messages
    .map((message) => ({
      message_id: message.id,
      bot_id: chat.botId,
      message: {
        role: message.role,
        content: message.content,
        type: message.type || "text",
        asr: message.asr || null,
        duration: message.duration || null,
        tts: message.tts || null,
        translation: message.translation || null,
        grammar_analysis: message.grammar_analysis || null,
        pronunciation_assessment: message.pronunciation_assessment || null,
      },
      created_at: message.timestamp || new Date().toISOString(),
    }));
};

// Convert ChatMessageItem from API to ChatMessage format
export const convertAPIMessageToChatMessage = (apiMessage) => {
  return new ChatMessage({
    id: apiMessage.id,
    role: apiMessage.message.role,
    content: apiMessage.message.content,
    type: apiMessage.message.type || "text",
    timestamp: apiMessage.created_at,
    duration: apiMessage.message.duration, // Include duration for voice messages
    asr: apiMessage.message.asr,
    tts: apiMessage.message.tts,
    translation: apiMessage.message.translation,
    grammar_analysis: apiMessage.message.grammar_analysis,
    pronunciation_assessment: apiMessage.message.pronunciation_assessment,
  });
};
