"""Test runner script for the Gusto AI Base Service."""

import subprocess
import sys
import os


def run_tests():
    """Run all tests with coverage."""
    print("🧪 Running unit tests...")

    # Set test environment variables
    os.environ["ENVIRONMENT"] = "test"
    os.environ["API_KEY"] = "test-api-key"
    os.environ["DB"] = "sqlite:///test.db"

    # Add current directory to Python path for src module imports
    current_dir = os.path.dirname(os.path.abspath(__file__))
    os.environ["PYTHONPATH"] = current_dir

    # Check if tests directory exists
    if not os.path.exists("tests"):
        print("❌ Tests directory not found!")
        return 1

    # Check if src directory exists
    if not os.path.exists("src"):
        print("❌ Source directory not found!")
        return 1

    # Run tests with coverage and JUnit XML output
    cmd = [
        "uv", "run", "pytest",
        "tests/",
        "--cov=src",
        "--cov-report=html",
        "--cov-report=term-missing",
        "--cov-report=xml",
        "--junit-xml=junit.xml",
        "-v",
        "--asyncio-mode=auto",
        "--tb=short"
    ]

    print(f"🔍 Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=os.environ.copy())

    # Ensure basic report files exist even if tests fail
    if not os.path.exists("junit.xml"):
        print("⚠️  Creating minimal junit.xml file")
        with open("junit.xml", "w") as f:
            f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            f.write('<testsuites name="pytest" tests="0" failures="0" errors="0" time="0.0">\n')
            f.write('</testsuites>\n')

    if not os.path.exists("coverage.xml"):
        print("⚠️  Creating minimal coverage.xml file")
        with open("coverage.xml", "w") as f:
            f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            f.write('<coverage version="0.0" timestamp="0" lines-valid="0" lines-covered="0" line-rate="0.0">\n')
            f.write('<sources><source>src</source></sources>\n')
            f.write('<packages></packages>\n')
            f.write('</coverage>\n')

    if not os.path.exists("htmlcov"):
        print("⚠️  Creating minimal htmlcov directory")
        os.makedirs("htmlcov", exist_ok=True)
        with open("htmlcov/index.html", "w") as f:
            f.write('<html><body><h1>No coverage data available</h1></body></html>')

    # Check what files were actually generated
    print("\n📁 Checking generated files:")
    if os.path.exists("htmlcov"):
        print("✅ HTML coverage report available in htmlcov/")

    if os.path.exists("coverage.xml"):
        print("✅ XML coverage report available: coverage.xml")

    if os.path.exists("junit.xml"):
        print("✅ JUnit report available: junit.xml")

    if result.returncode == 0:
        print("\n✅ All tests passed!")
    else:
        print("\n❌ Some tests failed!")
        print("💡 Check the test output above for details")

    return result.returncode


def run_specific_test(test_file):
    """Run a specific test file."""
    print(f"🧪 Running tests in {test_file}...")

    cmd = [
        "uv", "run", "pytest",
        f"tests/{test_file}",
        "-v",
        "--asyncio-mode=auto"
    ]

    result = subprocess.run(cmd)
    return result.returncode


if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_file = sys.argv[1]
        exit_code = run_specific_test(test_file)
    else:
        exit_code = run_tests()

    sys.exit(exit_code)
