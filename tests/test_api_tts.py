"""Tests for TTS API endpoints."""

import pytest
import json
import base64
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestTTSAPI:
    """Test cases for TTS API endpoints."""

    def test_create_tts_task_success(self, client, valid_headers):
        """Test creating TTS task successfully."""
        response = client.post(
            "/api/tts",
            headers=valid_headers,
            json={
                "text": "Hello world",
                "voice": "en_female_amanda_mars_bigtts",
                "speed": 1.0,
                "message_id": "msg-123"
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == "submitted"

    def test_create_tts_task_missing_text(self, client, valid_headers):
        """Test creating TTS task without text."""
        response = client.post(
            "/api/tts",
            headers=valid_headers,
            json={
                "voice": "en_female_amanda_mars_bigtts",
                "speed": 1.0
            }
        )

        assert response.status_code == 422  # Validation error

    def test_create_tts_task_unauthorized(self, client):
        """Test creating TTS task without API key."""
        response = client.post(
            "/api/tts",
            json={
                "text": "Hello world",
                "voice": "en_female_amanda_mars_bigtts"
            }
        )

        assert response.status_code == 403

    def test_get_tts_task_status_success(self, client, valid_headers):
        """Test getting TTS task status - success case."""
        response = client.get(
            "/api/tts/task-123",
            headers=valid_headers
        )

        # Should return some response (might be 404 if task doesn't exist, but that's ok for testing)
        assert response.status_code in [200, 404, 500]

    def test_get_tts_task_status_not_found(self, client, valid_headers):
        """Test getting TTS task status - task not found."""
        response = client.get(
            "/api/tts/nonexistent-task",
            headers=valid_headers
        )

        # Should return some response (likely 404 or 500)
        assert response.status_code in [404, 500]

    def test_get_tts_voices(self, client, valid_headers):
        """Test getting available TTS voices."""
        response = client.get(
            "/api/tts/voices",
            headers=valid_headers
        )

        # Should return some response
        assert response.status_code in [200, 404, 500]

    def test_create_tts_task_with_custom_speed(self, client, valid_headers):
        """Test creating TTS task with custom speed."""
        response = client.post(
            "/api/tts",
            headers=valid_headers,
            json={
                "text": "Hello world",
                "voice": "en_female_amanda_mars_bigtts",
                "speed": 1.5,
                "message_id": "msg-123"
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data

    def test_tts_empty_text(self, client, valid_headers):
        """Test TTS with empty text."""
        response = client.post(
            "/api/tts",
            headers=valid_headers,
            json={
                "text": "",
                "voice": "en_female_amanda_mars_bigtts"
            }
        )

        assert response.status_code == 422  # Validation error for empty text
