"""Test configuration and fixtures."""

import os
import sys
import pytest
from unittest.mock import Mock, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient
import tempfile
from io import BytesIO

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Set test environment variables
os.environ["API_KEY"] = "test-api-key"
os.environ["DB"] = "sqlite:///test.db"
os.environ["ENVIRONMENT"] = "test"

# Mock all external dependencies
os.environ["DOUBAO_APP_ID"] = "test-app-id"
os.environ["DOUBAO_API_TOKEN"] = "test-token"
os.environ["DOUBAO_TTS_APP_ID"] = "test-tts-app-id"
os.environ["DOUBAO_TTS_API_TOKEN"] = "test-tts-token"
os.environ["DOUBAO_STT_APP_ID"] = "test-stt-app-id"
os.environ["DOUBAO_STT_ACCESS_TOKEN"] = "test-stt-token"
os.environ["ELSA_API_TOKEN"] = "test-elsa-token"
os.environ["XFYUN_APP_ID"] = "test-xfyun-app-id"
os.environ["XFYUN_API_SECRET"] = "test-xfyun-secret"
os.environ["XFYUN_API_KEY"] = "test-xfyun-key"
os.environ["ALIYUN_OSS_ACCESS_KEY_ID"] = "test-oss-key"
os.environ["ALIYUN_OSS_ACCESS_KEY_SECRET"] = "test-oss-secret"
os.environ["ALIYUN_OSS_BUCKET_NAME"] = "test-bucket"
os.environ["ALIYUN_OSS_ENDPOINT"] = "test-endpoint"
os.environ["OPENAI_API_KEY"] = "test-openai-key"
os.environ["OPENAI_API_BASE"] = "https://test-api.openai.com/v1"
os.environ["AZURE_SPEECH_KEY"] = "test-azure-key"
os.environ["AZURE_SPEECH_REGION"] = "test-region"

# Import the app after setting environment variables
from src.api import app


@pytest.fixture
def client():
    """Create a test client for the FastAPI app."""
    return TestClient(app)


@pytest.fixture
async def async_client():
    """Create an async test client for the FastAPI app."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac


@pytest.fixture
def valid_headers():
    """Valid API headers for testing."""
    return {"X-API-KEY": "test-api-key"}


@pytest.fixture
def valid_user_headers():
    """Valid user headers for testing."""
    return {"X-5E-USER": '{"id": "test-user-123"}'}


@pytest.fixture
def mock_audio_file():
    """Create a mock audio file for testing."""
    audio_content = b"fake audio content"
    return BytesIO(audio_content)


@pytest.fixture
def temp_audio_file():
    """Create a temporary audio file for testing."""
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as f:
        f.write(b"fake audio content")
        temp_path = f.name
    yield temp_path
    # Clean up
    if os.path.exists(temp_path):
        os.remove(temp_path)


@pytest.fixture(autouse=True)
def mock_external_services(monkeypatch):
    """Mock all external services."""
    # Mock Sentry
    mock_sentry = Mock()
    monkeypatch.setattr("sentry_sdk.capture_exception", mock_sentry)
    monkeypatch.setattr("sentry_sdk.capture_message", mock_sentry)

    # Mock database operations
    mock_db = Mock()
    mock_db.init_db = Mock()
    mock_db.create_task = Mock()
    mock_db.update_task = Mock()
    mock_db.get_task = Mock(return_value={
        "task_id": "test-task-id",
        "status": "completed",
        "result": {"test": "result"},
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:01:00Z"
    })
    monkeypatch.setattr("src.db", mock_db)

    # Mock upload functions
    monkeypatch.setattr("src.upload.upload", Mock(
        return_value="https://test.com/audio.wav"))
    monkeypatch.setattr("src.upload.upload_bytes", Mock(
        return_value="https://test.com/audio.mp3"))
    monkeypatch.setattr("src.upload.delete_uploaded_file", Mock())

    # Mock scheduler
    monkeypatch.setattr("src.scheduler.start_scheduler", Mock())

    # Mock ASR functions
    mock_asr = Mock()
    mock_asr.submit_task = Mock(return_value=("task-123", "log-456"))
    mock_asr.query_task = Mock()
    mock_asr.convert_to_16kHz_mono_wav_audio = AsyncMock(
        return_value="https://test.com/converted.wav")
    mock_asr.convert_to_srt = Mock(
        return_value="1\n00:00:00,000 --> 00:00:05,000\nTest transcript\n")
    monkeypatch.setattr("src.asr", mock_asr)

    # Mock pronunciation assessment
    monkeypatch.setattr("src.pronunciation_azure.continuous_pronunciation_assessment",
                        Mock(return_value={"status": "success", "data": {"score": 85}}))
    monkeypatch.setattr("src.pronunciation_elsa.pronunciation_assessment",
                        AsyncMock(return_value={"speakers": [], "transcript": "test", "success": True}))

    # Mock chat functions
    monkeypatch.setattr("src.chat.generate_response",
                        AsyncMock(return_value={"choices": [{"message": {"content": "Test response"}}]}))
    monkeypatch.setattr("src.chat.generate_stream_response",
                        AsyncMock(return_value=iter(["Test", " response"])))

    # Mock audio utilities
    monkeypatch.setattr(
        "src.utils.audio_utils.change_audio_format", Mock(return_value="test.wav"))
    monkeypatch.setattr(
        "src.utils.audio_utils.get_audio_duration", Mock(return_value=10.0))
    monkeypatch.setattr("src.utils.audio_utils.convert_to_16kHz_mono_wav_audio",
                        AsyncMock(return_value="https://test.com/converted.wav"))

    # Mock file utilities
    monkeypatch.setattr("src.utils.file_utils.check_if_valid_url", Mock())

    # Mock TTS functions
    monkeypatch.setattr("src.tools.tts.text_to_speech", AsyncMock(
        return_value="dGVzdCBhdWRpbw=="))  # base64 encoded "test audio"

    # Mock grammar suggestion
    monkeypatch.setattr("src.tools.grammar_suggestion.generate_grammar_suggestion", AsyncMock(return_value=Mock(
        errors=[], improvements=[], suggested_text="Test text"
    )))

    # Mock translation
    monkeypatch.setattr("src.tools.translate.translate_text", AsyncMock(return_value="Translated text"))

    # Mock chat suggestions
    monkeypatch.setattr("src.tools.chat_suggestion.generate_chat_suggestions", AsyncMock(
        return_value=["Suggestion 1", "Suggestion 2"]))

    # Mock mission tools
    monkeypatch.setattr("src.tools.mission.create_missions", AsyncMock(return_value=Mock(
        id="mission-123", title="Test Mission", status="active"
    )))
    monkeypatch.setattr("src.tools.mission.check_mission", AsyncMock(return_value=Mock(
        id="mission-123", title="Test Mission", status="completed"
    )))


@pytest.fixture
def mock_websocket():
    """Mock WebSocket for testing."""
    mock_ws = Mock()
    mock_ws.accept = AsyncMock()
    mock_ws.send_json = AsyncMock()
    mock_ws.send_bytes = AsyncMock()
    mock_ws.receive_json = AsyncMock()
    mock_ws.receive_text = AsyncMock()
    mock_ws.close = AsyncMock()
    mock_ws.headers = {"X-API-KEY": "test-api-key"}
    mock_ws.query_params = {}
    return mock_ws
