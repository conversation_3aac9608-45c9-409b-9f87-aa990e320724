"""Tests for Pronunciation Assessment API endpoints."""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestPronunciationAPI:
    """Test cases for Pronunciation Assessment API endpoints."""

    def test_azure_pronunciation_assessment_with_file(self, client, valid_headers, mock_audio_file):
        """Test Azure pronunciation assessment with audio file."""
        with patch('src.pronunciation_azure.continuous_pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "status": "success",
                "data": {
                    "accuracy_score": 85.5,
                    "fluency_score": 78.2,
                    "completeness_score": 92.1,
                    "pronunciation_score": 81.3,
                    "words": [
                        {
                            "word": "hello",
                            "accuracy_score": 90.0,
                            "error_type": "None"
                        },
                        {
                            "word": "world",
                            "accuracy_score": 80.0,
                            "error_type": "Mispronunciation"
                        }
                    ]
                }
            }
            
            response = client.post(
                "/api/pronunciation_assessment",
                headers=valid_headers,
                files={"audio_file": ("test.wav", mock_audio_file, "audio/wav")},
                data={
                    "reference_text": "hello world",
                    "return_json": "true"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert "data" in data
            assert data["data"]["accuracy_score"] == 85.5

    def test_azure_pronunciation_assessment_with_url(self, client, valid_headers):
        """Test Azure pronunciation assessment with audio URL."""
        with patch('src.pronunciation_azure.continuous_pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "status": "success",
                "data": {
                    "accuracy_score": 88.0,
                    "fluency_score": 82.5,
                    "completeness_score": 95.0,
                    "pronunciation_score": 85.0
                }
            }
            
            response = client.post(
                "/api/pronunciation_assessment",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "reference_text": "hello world",
                    "return_json": "true"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"
            assert data["data"]["accuracy_score"] == 88.0

    def test_azure_pronunciation_assessment_missing_input(self, client, valid_headers):
        """Test Azure pronunciation assessment without audio input."""
        response = client.post(
            "/api/pronunciation_assessment",
            headers=valid_headers,
            data={
                "reference_text": "hello world"
            }
        )
        
        assert response.status_code == 400
        assert "Either audio_file or audio_url must be provided" in response.json()["error"]

    def test_azure_pronunciation_assessment_missing_reference_text(self, client, valid_headers, mock_audio_file):
        """Test Azure pronunciation assessment without reference text."""
        response = client.post(
            "/api/pronunciation_assessment",
            headers=valid_headers,
            files={"audio_file": ("test.wav", mock_audio_file, "audio/wav")}
        )
        
        assert response.status_code == 422  # Validation error

    def test_elsa_pronunciation_assessment_with_file(self, client, valid_headers, mock_audio_file):
        """Test Elsa pronunciation assessment with audio file."""
        with patch('src.pronunciation_elsa.pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "speakers": [
                    {
                        "speaker_id": "speaker_1",
                        "pronunciation_score": 82.5,
                        "fluency_score": 78.0,
                        "segments": [
                            {
                                "text": "hello",
                                "pronunciation_score": 85.0,
                                "start_time": 0.0,
                                "end_time": 0.5
                            },
                            {
                                "text": "world",
                                "pronunciation_score": 80.0,
                                "start_time": 0.6,
                                "end_time": 1.0
                            }
                        ]
                    }
                ],
                "transcript": "hello world",
                "success": True
            }
            
            response = client.post(
                "/api/pronunciation_assessment/elsa",
                headers=valid_headers,
                files={"audio_file": ("test.wav", mock_audio_file, "audio/wav")},
                data={
                    "return_json": "true",
                    "sync": "true"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "speakers" in data
            assert len(data["speakers"]) == 1
            assert data["speakers"][0]["pronunciation_score"] == 82.5

    def test_elsa_pronunciation_assessment_with_url(self, client, valid_headers):
        """Test Elsa pronunciation assessment with audio URL."""
        with patch('src.pronunciation_elsa.pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "speakers": [],
                "transcript": "hello world",
                "success": True
            }
            
            response = client.post(
                "/api/pronunciation_assessment/elsa",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "return_json": "true",
                    "sync": "true"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "transcript" in data

    def test_elsa_pronunciation_assessment_missing_input(self, client, valid_headers):
        """Test Elsa pronunciation assessment without audio input."""
        response = client.post(
            "/api/pronunciation_assessment/elsa",
            headers=valid_headers,
            data={
                "return_json": "true"
            }
        )
        
        assert response.status_code == 400
        assert "Either audio_file or audio_url must be provided" in response.json()["error"]

    def test_azure_pronunciation_assessment_unauthorized(self, client):
        """Test Azure pronunciation assessment without API key."""
        response = client.post(
            "/api/pronunciation_assessment",
            data={
                "audio_url": "https://example.com/audio.wav",
                "reference_text": "hello world"
            }
        )
        
        assert response.status_code == 403

    def test_elsa_pronunciation_assessment_unauthorized(self, client):
        """Test Elsa pronunciation assessment without API key."""
        response = client.post(
            "/api/pronunciation_assessment/elsa",
            data={
                "audio_url": "https://example.com/audio.wav"
            }
        )
        
        assert response.status_code == 403

    def test_azure_pronunciation_assessment_error_handling(self, client, valid_headers):
        """Test Azure pronunciation assessment error handling."""
        with patch('src.pronunciation_azure.continuous_pronunciation_assessment') as mock_assess:
            mock_assess.side_effect = Exception("Azure service unavailable")
            
            response = client.post(
                "/api/pronunciation_assessment",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "reference_text": "hello world"
                }
            )
            
            assert response.status_code == 500
            assert "Azure service unavailable" in response.json()["detail"]

    def test_elsa_pronunciation_assessment_error_handling(self, client, valid_headers):
        """Test Elsa pronunciation assessment error handling."""
        with patch('src.pronunciation_elsa.pronunciation_assessment') as mock_assess:
            mock_assess.side_effect = Exception("Elsa service unavailable")
            
            response = client.post(
                "/api/pronunciation_assessment/elsa",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav"
                }
            )
            
            assert response.status_code == 500

    @pytest.mark.asyncio
    async def test_elsa_pronunciation_assessment_async(self, async_client, valid_headers):
        """Test Elsa pronunciation assessment using async client."""
        with patch('src.pronunciation_elsa.pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "speakers": [],
                "transcript": "async test",
                "success": True
            }
            
            response = await async_client.post(
                "/api/pronunciation_assessment/elsa",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "sync": "true"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True

    def test_azure_pronunciation_assessment_with_granularity(self, client, valid_headers):
        """Test Azure pronunciation assessment with different granularity levels."""
        with patch('src.pronunciation_azure.continuous_pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "status": "success",
                "data": {
                    "accuracy_score": 85.0,
                    "phonemes": [
                        {
                            "phoneme": "h",
                            "accuracy_score": 90.0
                        },
                        {
                            "phoneme": "ɛ",
                            "accuracy_score": 80.0
                        }
                    ]
                }
            }
            
            response = client.post(
                "/api/pronunciation_assessment",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "reference_text": "hello",
                    "granularity": "phoneme"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "success"

    def test_elsa_pronunciation_assessment_with_grammar_vocab(self, client, valid_headers):
        """Test Elsa pronunciation assessment with grammar vocabulary forcing."""
        with patch('src.pronunciation_elsa.pronunciation_assessment') as mock_assess:
            mock_assess.return_value = {
                "speakers": [],
                "transcript": "forced grammar vocabulary",
                "success": True,
                "grammar_corrections": [
                    {
                        "original": "I are happy",
                        "corrected": "I am happy"
                    }
                ]
            }
            
            response = client.post(
                "/api/pronunciation_assessment/elsa",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "force_grammar_vocab": "true"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
