"""Tests for Tools API endpoints (Grammar, Translation, Mission, etc.)."""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestGrammarSuggestionAPI:
    """Test cases for Grammar Suggestion API endpoints."""

    def test_grammar_suggestion_success(self, client, valid_headers):
        """Test successful grammar suggestion."""
        response = client.post(
            "/api/grammar_suggestion",
            headers=valid_headers,
            json={
                "text": "I are happy and I gonna go to the store.",
                "colloquial": False
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == "submitted"

    def test_grammar_suggestion_colloquial(self, client, valid_headers):
        """Test grammar suggestion with colloquial text."""
        response = client.post(
            "/api/grammar_suggestion",
            headers=valid_headers,
            json={
                "text": "Hey, what's up?",
                "colloquial": True
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data

    def test_grammar_suggestion_unauthorized(self, client):
        """Test grammar suggestion without API key."""
        response = client.post(
            "/api/grammar_suggestion",
            json={
                "text": "I are happy"
            }
        )

        assert response.status_code == 403

    def test_grammar_suggestion_empty_text(self, client, valid_headers):
        """Test grammar suggestion with empty text."""
        response = client.post(
            "/api/grammar_suggestion",
            headers=valid_headers,
            json={
                "text": ""
            }
        )

        assert response.status_code == 422  # Validation error


class TestTranslationAPI:
    """Test cases for Translation API endpoints."""

    def test_translate_text_success(self, client, valid_headers):
        """Test successful text translation."""
        response = client.post(
            "/api/translate",
            headers=valid_headers,
            json={
                "text": "Hello world",
                "target_language": "es",
                "source_language": "en"
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data
        assert data["status"] == "submitted"

    def test_translate_text_auto_detect(self, client, valid_headers):
        """Test translation with auto-detected source language."""
        response = client.post(
            "/api/translate",
            headers=valid_headers,
            json={
                "text": "Hola mundo",
                "target_language": "en"
            }
        )

        assert response.status_code == 200
        data = response.json()
        assert "task_id" in data

    def test_translate_text_unauthorized(self, client):
        """Test translation without API key."""
        response = client.post(
            "/api/translate",
            json={
                "text": "Hello world",
                "target_language": "es"
            }
        )
        
        assert response.status_code == 403

    def test_translate_text_empty(self, client, valid_headers):
        """Test translation with empty text."""
        response = client.post(
            "/api/translate",
            headers=valid_headers,
            json={
                "text": "",
                "target_language": "es"
            }
        )
        
        assert response.status_code == 422  # Validation error


class TestMissionAPI:
    """Test cases for Mission API endpoints."""

    def test_create_mission_success(self, client, valid_headers):
        """Test successful mission creation."""
        response = client.post(
            "/api/mission",
            headers=valid_headers,
            json={
                "mission_details": {
                    "topic": "English pronunciation",
                    "level": "beginner",
                    "focus_areas": ["th sounds", "vowel sounds"]
                }
            }
        )

        assert response.status_code in [200, 500]  # May fail due to missing dependencies

    def test_create_mission_unauthorized(self, client):
        """Test mission creation without API key."""
        response = client.post(
            "/api/mission",
            json={
                "mission_details": {
                    "topic": "English pronunciation"
                }
            }
        )

        assert response.status_code == 403
