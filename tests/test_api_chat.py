"""Tests for Chat API endpoints."""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestChatAPI:
    """Test cases for Chat API endpoints."""

    def test_chat_completion_success(self, client, valid_headers):
        """Test successful chat completion."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Hello! How can I help you today?",
                            "role": "assistant"
                        }
                    }
                ],
                "usage": {
                    "prompt_tokens": 10,
                    "completion_tokens": 8,
                    "total_tokens": 18
                }
            }
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello"
                        }
                    ],
                    "model": "doubao-1-5-lite-32k-250115",
                    "temperature": 0.7
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data
            assert len(data["choices"]) == 1
            assert data["choices"][0]["message"]["content"] == "Hello! How can I help you today?"

    def test_chat_completion_streaming(self, client, valid_headers):
        """Test streaming chat completion."""
        with patch('src.chat.generate_stream_response') as mock_stream:
            mock_stream.return_value = iter(["Hello", " there", "!"])
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello"
                        }
                    ],
                    "stream": True
                }
            )
            
            assert response.status_code == 200
            # For streaming responses, we should get a streaming response
            assert response.headers.get("content-type") == "text/plain; charset=utf-8"

    def test_chat_completion_unauthorized(self, client):
        """Test chat completion without API key."""
        response = client.post(
            "/api/chat",
            json={
                "messages": [
                    {
                        "role": "user",
                        "content": "Hello"
                    }
                ]
            }
        )
        
        assert response.status_code == 403

    def test_chat_completion_invalid_messages(self, client, valid_headers):
        """Test chat completion with invalid message format."""
        response = client.post(
            "/api/chat",
            headers=valid_headers,
            json={
                "messages": [
                    {
                        "role": "invalid_role",
                        "content": "Hello"
                    }
                ]
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_chat_completion_empty_messages(self, client, valid_headers):
        """Test chat completion with empty messages array."""
        response = client.post(
            "/api/chat",
            headers=valid_headers,
            json={
                "messages": []
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_chat_completion_with_system_message(self, client, valid_headers):
        """Test chat completion with system message."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "I'm a helpful assistant specialized in coding.",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful coding assistant."
                        },
                        {
                            "role": "user",
                            "content": "What can you help me with?"
                        }
                    ]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data

    def test_chat_completion_with_custom_model(self, client, valid_headers):
        """Test chat completion with custom model."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Response from custom model",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello"
                        }
                    ],
                    "model": "doubao-1-5-pro-32k-250115"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data

    def test_chat_completion_with_temperature(self, client, valid_headers):
        """Test chat completion with custom temperature."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Creative response",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Be creative"
                        }
                    ],
                    "temperature": 1.2
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data
            
            # Verify temperature was passed correctly
            mock_generate.assert_called_once()
            call_args = mock_generate.call_args
            assert call_args[1]["temperature"] == 1.2

    def test_chat_completion_error_handling(self, client, valid_headers):
        """Test chat completion error handling."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.side_effect = Exception("OpenAI API error")
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello"
                        }
                    ]
                }
            )
            
            assert response.status_code == 500
            assert "OpenAI API error" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_chat_completion_async(self, async_client, valid_headers):
        """Test chat completion using async client."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Async response",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = await async_client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello async"
                        }
                    ]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data

    def test_chat_completion_long_conversation(self, client, valid_headers):
        """Test chat completion with long conversation history."""
        messages = []
        for i in range(10):
            messages.extend([
                {"role": "user", "content": f"User message {i}"},
                {"role": "assistant", "content": f"Assistant response {i}"}
            ])
        messages.append({"role": "user", "content": "Final question"})
        
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Final response",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": messages
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data

    def test_chat_completion_with_max_tokens(self, client, valid_headers):
        """Test chat completion with max_tokens parameter."""
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Short response",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = client.post(
                "/api/chat",
                headers=valid_headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Give me a short answer"
                        }
                    ],
                    "max_tokens": 50
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data

    def test_chat_completion_with_user_context(self, client, valid_headers, valid_user_headers):
        """Test chat completion with user context."""
        headers = {**valid_headers, **valid_user_headers}
        
        with patch('src.chat.generate_response') as mock_generate:
            mock_generate.return_value = {
                "choices": [
                    {
                        "message": {
                            "content": "Hello user!",
                            "role": "assistant"
                        }
                    }
                ]
            }
            
            response = client.post(
                "/api/chat",
                headers=headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "Hello"
                        }
                    ]
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "choices" in data
