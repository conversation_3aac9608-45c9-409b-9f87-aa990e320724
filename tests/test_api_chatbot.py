"""Tests for Chatbot Management API endpoints."""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient


class TestChatbotAPI:
    """Test cases for Chatbot Management API endpoints."""

    def test_create_chatbot_success(self, client, valid_headers):
        """Test successful chatbot creation."""
        with patch('src.db.create_or_update_chatbot') as mock_create:
            mock_create.return_value = {
                "id": "bot-123",
                "name": "English Tutor",
                "persona": "A friendly English teacher who helps with pronunciation and grammar",
                "bot_type": "tutor",
                "image_url": "https://example.com/bot-avatar.png",
                "voice": "en_female_amanda_mars_bigtts",
                "voice_speed": 1.0,
                "hello_message": "Hello! I'm here to help you learn English.",
                "extra_data": {"specialty": "pronunciation"},
                "created_at": "2025-01-01T00:00:00Z",
                "updated_at": "2025-01-01T00:00:00Z"
            }
            
            response = client.post(
                "/api/chatbot",
                headers=valid_headers,
                json={
                    "id": "bot-123",
                    "name": "English Tutor",
                    "persona": "A friendly English teacher who helps with pronunciation and grammar",
                    "bot_type": "tutor",
                    "image_url": "https://example.com/bot-avatar.png",
                    "voice": "en_female_amanda_mars_bigtts",
                    "voice_speed": 1.0,
                    "hello_message": "Hello! I'm here to help you learn English.",
                    "extra_data": {"specialty": "pronunciation"}
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == "bot-123"
            assert data["name"] == "English Tutor"
            assert data["bot_type"] == "tutor"

    def test_update_chatbot_success(self, client, valid_headers):
        """Test successful chatbot update."""
        with patch('src.db.create_or_update_chatbot') as mock_update:
            mock_update.return_value = {
                "id": "bot-123",
                "name": "Advanced English Tutor",
                "persona": "An advanced English teacher specializing in business English",
                "bot_type": "tutor",
                "image_url": "https://example.com/new-avatar.png",
                "voice": "en_male_jackson_mars_bigtts",
                "voice_speed": 0.9,
                "hello_message": "Welcome! Let's improve your business English skills.",
                "extra_data": {"specialty": "business_english"},
                "created_at": "2025-01-01T00:00:00Z",
                "updated_at": "2025-01-01T01:00:00Z"
            }
            
            response = client.post(
                "/api/chatbot",
                headers=valid_headers,
                json={
                    "id": "bot-123",
                    "name": "Advanced English Tutor",
                    "persona": "An advanced English teacher specializing in business English",
                    "bot_type": "tutor",
                    "image_url": "https://example.com/new-avatar.png",
                    "voice": "en_male_jackson_mars_bigtts",
                    "voice_speed": 0.9,
                    "hello_message": "Welcome! Let's improve your business English skills.",
                    "extra_data": {"specialty": "business_english"}
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == "Advanced English Tutor"
            assert data["voice"] == "en_male_jackson_mars_bigtts"

    def test_get_chatbot_success(self, client, valid_headers):
        """Test successful chatbot retrieval."""
        with patch('src.db.get_chatbot') as mock_get:
            mock_get.return_value = {
                "id": "bot-123",
                "name": "English Tutor",
                "persona": "A friendly English teacher",
                "bot_type": "tutor",
                "image_url": "https://example.com/bot-avatar.png",
                "voice": "en_female_amanda_mars_bigtts",
                "voice_speed": 1.0,
                "hello_message": "Hello! I'm here to help you learn English.",
                "extra_data": {"specialty": "pronunciation"},
                "created_at": "2025-01-01T00:00:00Z",
                "updated_at": "2025-01-01T00:00:00Z"
            }
            
            response = client.get(
                "/api/chatbot/bot-123",
                headers=valid_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["id"] == "bot-123"
            assert data["name"] == "English Tutor"

    def test_get_chatbot_not_found(self, client, valid_headers):
        """Test chatbot retrieval when bot doesn't exist."""
        with patch('src.db.get_chatbot') as mock_get:
            mock_get.return_value = None
            
            response = client.get(
                "/api/chatbot/nonexistent-bot",
                headers=valid_headers
            )
            
            assert response.status_code == 404
            assert "Chatbot not found" in response.json()["detail"]

    def test_get_all_chatbots_success(self, client, valid_headers):
        """Test successful retrieval of all chatbots."""
        with patch('src.db.get_all_chatbots') as mock_get_all:
            mock_get_all.return_value = [
                {
                    "id": "bot-123",
                    "name": "English Tutor",
                    "persona": "A friendly English teacher",
                    "bot_type": "tutor",
                    "image_url": "https://example.com/bot1.png",
                    "voice": "en_female_amanda_mars_bigtts",
                    "voice_speed": 1.0,
                    "hello_message": "Hello! I'm here to help you learn English.",
                    "extra_data": {"specialty": "pronunciation"}
                },
                {
                    "id": "bot-456",
                    "name": "Conversation Partner",
                    "persona": "A casual conversation partner for practice",
                    "bot_type": "companion",
                    "image_url": "https://example.com/bot2.png",
                    "voice": "en_male_jackson_mars_bigtts",
                    "voice_speed": 1.1,
                    "hello_message": "Hey there! Want to chat?",
                    "extra_data": {"style": "casual"}
                }
            ]
            
            response = client.get(
                "/api/chatbots",
                headers=valid_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "chatbots" in data
            assert len(data["chatbots"]) == 2
            assert data["chatbots"][0]["id"] == "bot-123"
            assert data["chatbots"][1]["id"] == "bot-456"

    def test_delete_chatbot_success(self, client, valid_headers):
        """Test successful chatbot deletion."""
        with patch('src.db.delete_chatbot') as mock_delete:
            mock_delete.return_value = True
            
            response = client.delete(
                "/api/chatbot/bot-123",
                headers=valid_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "deleted successfully" in data["message"]

    def test_delete_chatbot_not_found(self, client, valid_headers):
        """Test chatbot deletion when bot doesn't exist."""
        with patch('src.db.delete_chatbot') as mock_delete:
            mock_delete.return_value = False
            
            response = client.delete(
                "/api/chatbot/nonexistent-bot",
                headers=valid_headers
            )
            
            assert response.status_code == 404
            assert "Chatbot not found" in response.json()["detail"]

    def test_get_available_voices(self, client, valid_headers):
        """Test getting available voice options."""
        response = client.get(
            "/api/voices",
            headers=valid_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "voices" in data
        assert len(data["voices"]) > 0
        
        # Check voice structure
        voice = data["voices"][0]
        assert "id" in voice
        assert "name" in voice
        assert "language" in voice
        assert "gender" in voice

    def test_create_chatbot_unauthorized(self, client):
        """Test chatbot creation without API key."""
        response = client.post(
            "/api/chatbot",
            json={
                "name": "Test Bot",
                "persona": "A test bot"
            }
        )
        
        assert response.status_code == 403

    def test_get_chatbot_unauthorized(self, client):
        """Test chatbot retrieval without API key."""
        response = client.get("/api/chatbot/bot-123")
        
        assert response.status_code == 403

    def test_delete_chatbot_unauthorized(self, client):
        """Test chatbot deletion without API key."""
        response = client.delete("/api/chatbot/bot-123")
        
        assert response.status_code == 403

    def test_create_chatbot_validation_error(self, client, valid_headers):
        """Test chatbot creation with invalid data."""
        response = client.post(
            "/api/chatbot",
            headers=valid_headers,
            json={
                "name": "",  # Empty name should fail validation
                "persona": "A test bot"
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_create_chatbot_with_minimal_data(self, client, valid_headers):
        """Test chatbot creation with minimal required data."""
        with patch('src.db.create_or_update_chatbot') as mock_create:
            mock_create.return_value = {
                "id": "bot-minimal",
                "name": "Minimal Bot",
                "persona": "A simple bot",
                "bot_type": "general",
                "image_url": None,
                "voice": "en_female_amanda_mars_bigtts",
                "voice_speed": 1.0,
                "hello_message": "Hello!",
                "extra_data": {},
                "created_at": "2025-01-01T00:00:00Z",
                "updated_at": "2025-01-01T00:00:00Z"
            }
            
            response = client.post(
                "/api/chatbot",
                headers=valid_headers,
                json={
                    "name": "Minimal Bot",
                    "persona": "A simple bot"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == "Minimal Bot"

    def test_chatbot_error_handling(self, client, valid_headers):
        """Test chatbot API error handling."""
        with patch('src.db.create_or_update_chatbot') as mock_create:
            mock_create.side_effect = Exception("Database error")
            
            response = client.post(
                "/api/chatbot",
                headers=valid_headers,
                json={
                    "name": "Error Bot",
                    "persona": "A bot that causes errors"
                }
            )
            
            assert response.status_code == 500
            assert "Failed to create or update chatbot" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_create_chatbot_async(self, async_client, valid_headers):
        """Test chatbot creation using async client."""
        with patch('src.db.create_or_update_chatbot') as mock_create:
            mock_create.return_value = {
                "id": "async-bot",
                "name": "Async Bot",
                "persona": "An async bot",
                "bot_type": "general",
                "voice": "en_female_amanda_mars_bigtts",
                "voice_speed": 1.0,
                "hello_message": "Hello async!",
                "extra_data": {}
            }
            
            response = await async_client.post(
                "/api/chatbot",
                headers=valid_headers,
                json={
                    "name": "Async Bot",
                    "persona": "An async bot"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["name"] == "Async Bot"
