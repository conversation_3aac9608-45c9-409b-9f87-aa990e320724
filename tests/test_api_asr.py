"""Tests for ASR API endpoints."""

import pytest
import json
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from httpx import AsyncClient
import tempfile
import os
from io import BytesIO


class TestASRAPI:
    """Test cases for ASR API endpoints."""

    def test_create_asr_task_with_file(self, client, valid_headers, mock_audio_file):
        """Test creating ASR task with audio file upload."""
        with patch('src.asr.submit_task') as mock_submit:
            mock_submit.return_value = ("task-123", "log-456")
            
            response = client.post(
                "/api/asr",
                headers=valid_headers,
                files={"file": ("test.wav", mock_audio_file, "audio/wav")},
                data={"srt": "false", "message_id": "msg-123"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "task_id" in data
            assert "log_id" in data
            assert data["status"] == "submitted"

    def test_create_asr_task_with_url(self, client, valid_headers):
        """Test creating ASR task with audio URL."""
        with patch('src.asr.submit_task') as mock_submit:
            mock_submit.return_value = ("task-123", "log-456")
            
            response = client.post(
                "/api/asr",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "srt": "true",
                    "message_id": "msg-123"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "task_id" in data
            assert "log_id" in data
            assert data["status"] == "submitted"

    def test_create_asr_task_missing_input(self, client, valid_headers):
        """Test creating ASR task without file or URL."""
        response = client.post(
            "/api/asr",
            headers=valid_headers,
            data={"srt": "false"}
        )
        
        assert response.status_code == 400
        assert "Either file or audio_url must be provided" in response.json()["detail"]

    def test_create_asr_task_unauthorized(self, client):
        """Test creating ASR task without API key."""
        response = client.post(
            "/api/asr",
            data={"audio_url": "https://example.com/audio.wav"}
        )

        assert response.status_code in [401, 403]  # Either is acceptable for unauthorized

    def test_get_asr_task_status_success(self, client, valid_headers):
        """Test getting ASR task status - success case."""
        with patch('src.asr.query_task') as mock_query:
            mock_query.return_value = {
                "status": "completed",
                "result": {
                    "utterances": [
                        {
                            "text": "Hello world",
                            "start_time": 0.0,
                            "end_time": 2.0
                        }
                    ]
                }
            }
            
            response = client.get(
                "/api/asr/task-123",
                headers=valid_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
            assert "result" in data
            assert "text" in data

    def test_get_asr_task_status_processing(self, client, valid_headers):
        """Test getting ASR task status - still processing."""
        with patch('src.asr.query_task') as mock_query:
            mock_query.return_value = {
                "status": "processing"
            }
            
            response = client.get(
                "/api/asr/task-123",
                headers=valid_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "processing"

    def test_get_asr_task_status_not_found(self, client, valid_headers):
        """Test getting ASR task status - task not found."""
        with patch('src.asr.query_task') as mock_query:
            mock_query.side_effect = Exception("Task not found")
            
            response = client.get(
                "/api/asr/nonexistent-task",
                headers=valid_headers
            )
            
            assert response.status_code == 500

    def test_create_asr_v2_task_with_file(self, client, valid_headers, mock_audio_file):
        """Test creating ASR v2 task with audio file upload."""
        with patch('src.asr_v2.recognize_audio') as mock_recognize:
            mock_recognize.return_value = {
                "status": "completed",
                "text": "Hello world",
                "result": {
                    "utterances": [
                        {
                            "text": "Hello world",
                            "start_time": 0.0,
                            "end_time": 2.0
                        }
                    ]
                }
            }
            
            response = client.post(
                "/api/asr_v2",
                headers=valid_headers,
                files={"file": ("test.wav", mock_audio_file, "audio/wav")},
                data={"srt": "false", "message_id": "msg-123"}
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
            assert "text" in data
            assert "result" in data

    def test_create_asr_v2_task_with_url(self, client, valid_headers):
        """Test creating ASR v2 task with audio URL."""
        with patch('src.asr_v2.recognize_audio') as mock_recognize:
            mock_recognize.return_value = {
                "status": "completed",
                "text": "Hello world",
                "result": {
                    "utterances": [
                        {
                            "text": "Hello world",
                            "start_time": 0.0,
                            "end_time": 2.0
                        }
                    ]
                }
            }
            
            response = client.post(
                "/api/asr_v2",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "srt": "true",
                    "message_id": "msg-123"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
            assert "text" in data

    def test_create_asr_v2_task_missing_input(self, client, valid_headers):
        """Test creating ASR v2 task without file or URL."""
        response = client.post(
            "/api/asr_v2",
            headers=valid_headers,
            data={"srt": "false"}
        )
        
        assert response.status_code == 400
        assert "Either file or audio_url must be provided" in response.json()["detail"]

    def test_create_asr_v2_task_unauthorized(self, client):
        """Test creating ASR v2 task without API key."""
        response = client.post(
            "/api/asr_v2",
            data={"audio_url": "https://example.com/audio.wav"}
        )
        
        assert response.status_code == 403

    @pytest.mark.asyncio
    async def test_create_asr_task_async(self, async_client, valid_headers):
        """Test creating ASR task using async client."""
        with patch('src.asr.submit_task') as mock_submit:
            mock_submit.return_value = ("task-123", "log-456")
            
            response = await async_client.post(
                "/api/asr",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "srt": "false"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "task_id" in data
            assert "log_id" in data

    def test_asr_task_with_srt_format(self, client, valid_headers):
        """Test ASR task requesting SRT format output."""
        with patch('src.asr.submit_task') as mock_submit, \
             patch('src.asr.query_task') as mock_query:
            
            mock_submit.return_value = ("task-123", "log-456")
            mock_query.return_value = {
                "status": "completed",
                "result": {
                    "utterances": [
                        {
                            "text": "Hello world",
                            "start_time": 0.0,
                            "end_time": 2.0
                        }
                    ]
                }
            }
            
            # Create task with SRT format
            response = client.post(
                "/api/asr",
                headers=valid_headers,
                data={
                    "audio_url": "https://example.com/audio.wav",
                    "srt": "true"
                }
            )
            
            assert response.status_code == 200
            task_id = response.json()["task_id"]
            
            # Get task status
            response = client.get(
                f"/api/asr/{task_id}",
                headers=valid_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "completed"
            # SRT format should be included when requested
            assert "srt" in data or "result" in data

    def test_asr_error_handling(self, client, valid_headers):
        """Test ASR API error handling."""
        with patch('src.asr.submit_task') as mock_submit:
            mock_submit.side_effect = Exception("ASR service unavailable")
            
            response = client.post(
                "/api/asr",
                headers=valid_headers,
                data={"audio_url": "https://example.com/audio.wav"}
            )
            
            assert response.status_code == 500
            assert "ASR service unavailable" in response.json()["detail"]
